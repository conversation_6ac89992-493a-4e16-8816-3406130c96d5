#!/usr/bin/env -S bun run
/**
 * Bun CLI: Compose Flutter Briefing (daily or weekly)
 *
 * Usage:
 *   bun run scripts/briefing-compose.ts [daily|weekly]
 *   # default mode is "daily"
 *
 * This script reads briefing items from the local DB via listItemsSince()
 * and composes Markdown & HTML outputs using composeBriefing().
 * Outputs are written to:
 *   out/briefing-daily.{md,html}
 *   out/briefing-weekly.{md,html}
 */

import fs from "node:fs/promises";
import path from "node:path";
import process from "node:process";

import { listItemsSince } from "../src/briefing/db";
import { composeBriefing } from "../src/briefing/compose";

// Types
type Mode = "daily" | "weekly";

function parseMode(argv: string[]): Mode {
  const arg = (argv[2] || "daily").toLowerCase();
  if (arg === "daily" || arg === "weekly") return arg;
  if (arg === "-h" || arg === "--help") {
    printUsage();
    process.exit(0);
  }
  console.error(`Invalid mode: ${arg}`);
  printUsage();
  process.exit(1);
}

function printUsage() {
  console.log(`\nCompose Flutter Briefing\n\nUsage:\n  bun run scripts/briefing-compose.ts [daily|weekly]\n\nExamples:\n  bun run scripts/briefing-compose.ts\n  bun run scripts/briefing-compose.ts weekly\n`);
}

function now(): number {
  return Date.now();
}

function ms(hours: number): number {
  return hours * 60 * 60 * 1000;
}

function isoDate(date: Date): string {
  return date.toISOString().slice(0, 10); // YYYY-MM-DD
}

function buildDateLabel(mode: Mode, end: Date): string {
  if (mode === "daily") return isoDate(end);
  const start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000);
  return `${isoDate(start)} – ${isoDate(end)}`;
}

async function ensureOutDir(): Promise<string> {
  const outDir = path.join(process.cwd(), "out");
  await fs.mkdir(outDir, { recursive: true });
  return outDir;
}

async function writeOutputs(outDir: string, baseName: string, markdown: string, html: string) {
  const mdPath = path.join(outDir, `${baseName}.md`);
  const htmlPath = path.join(outDir, `${baseName}.html`);
  await fs.writeFile(mdPath, markdown, "utf8");
  await fs.writeFile(htmlPath, html, "utf8");
  return { mdPath, htmlPath };
}

async function main() {
  const mode = parseMode(Bun.argv);
  const end = new Date(now());
  const windowMs = mode === "daily" ? ms(24) : ms(24 * 7);
  const sinceMs = end.getTime() - windowMs;

  const items = listItemsSince(sinceMs);
  const dateLabel = buildDateLabel(mode, end);
  const { markdown, html, subject } = composeBriefing(dateLabel, items);

  const outDir = await ensureOutDir();
  const base = mode === "daily" ? "briefing-daily" : "briefing-weekly";
  const { mdPath, htmlPath } = await writeOutputs(outDir, base, markdown, html);

  console.log(subject);
  // Optional: also log file paths for visibility
  console.log(`Wrote: ${path.relative(process.cwd(), mdPath)} and ${path.relative(process.cwd(), htmlPath)}`);
}

main().catch((err) => {
  console.error("Failed to compose briefing:", err?.stack || err);
  process.exit(1);
});
