#!/usr/bin/env bun
import { ensureDb, initDb, initializeStatements, bulkUpsertItems, startRun, finishRun } from "../src/briefing/db";
import { loadAllSources } from "../src/briefing/sources";
import { ItemSchema, type Item } from "../src/briefing/types";

async function main() {
  const db = ensureDb();
  initDb(db);
  initializeStatements(db);

  let runId = -1; // Initialize with a non-valid ID
  let fetched = 0;
  let validated = 0;
  let inserted = 0;
  let updated = 0;
  let validationFailed = 0;
  let upsertFailed = 0;

  try {
    runId = startRun(db);
    const items = await loadAllSources();
    fetched = items.length;

    const validItems: Item[] = [];
    for (const it of items) {
      const parsed = ItemSchema.safeParse(it);
      if (!parsed.success) {
        console.warn('Item schema validation failed for item:', it.id || it.url || it.title, parsed.error.issues);
        validationFailed++;
        continue;
      }
      validItems.push(parsed.data);
    }
    validated = validItems.length;

    try {
      const { inserted: insertedCount, updated: updatedCount } = bulkUpsertItems(db, validItems);
      inserted = insertedCount;
      updated = updatedCount;
    } catch (ue) {
      console.warn('bulkUpsertItems failed', ue);
      upsertFailed = validItems.length;
    }

    console.log(`Ingested: fetched=${fetched}, validated=${validated}, inserted=${inserted}, updated=${updated}, validationFailed=${validationFailed}, upsertFailed=${upsertFailed}`);
  } catch (e) {
    console.error("Ingest error", e);
    process.exitCode = 1;
  } finally {
    if (runId !== -1) {
      finishRun(db, runId, { fetched, validated, inserted: inserted + updated, validationFailed, upsertFailed });
    } else {
      // If runId was never set, ensure a default finishRun is called
      finishRun(db, -1, { fetched, validated, inserted: inserted + updated, validationFailed, upsertFailed });
    }
  }
}
main();
