[{"id": "tag:github.com,2008:Repository/31792824/3.37.0-0.1.pre", "title": "3.37.0-0.1.pre", "url": "https://github.com/flutter/flutter/releases/tag/3.37.0-0.1.pre", "source": "flutter_releases", "section": "Releases", "publishedAt": "2025-09-16T18:55:17.000Z", "summary": "engine version update for 3.37.0-0.1 (#175445)"}, {"id": "tag:github.com,2008:Repository/31792824/3.35.4", "title": "3.35.4: engine version update (#175422)", "url": "https://github.com/flutter/flutter/releases/tag/3.35.4", "source": "flutter_releases", "section": "Releases", "publishedAt": "2025-09-16T14:27:41.000Z", "summary": "https://ci.chromium.org/ui/p/dart-internal/builders/flutter/Linux%20flutter_release/168/overview\nRelease engine version check failed."}, {"id": "tag:github.com,2008:Repository/31792824/3.37.0-0.0.pre", "title": "3.37.0-0.0.pre", "url": "https://github.com/flutter/flutter/releases/tag/3.37.0-0.0.pre", "source": "flutter_releases", "section": "Releases", "publishedAt": "2025-09-08T18:47:19.000Z", "summary": "Bump engine version for Flutter 3.37 (#175081)"}, {"id": "tag:github.com,2008:Repository/31792824/3.35.3", "title": "3.35.3", "url": "https://github.com/flutter/flutter/releases/tag/3.35.3", "source": "flutter_releases", "section": "Releases", "publishedAt": "2025-09-03T21:54:31.000Z", "summary": "Update engine version for Flutter 3.35.3 stable hotfix. (#174901)"}, {"id": "tag:github.com,2008:Repository/31792824/3.36.0-0.4.pre", "title": "3.36.0-0.4.pre: [3.36 CP] Add a 'bad' commit to the beta, to be reverted. (#174658)", "url": "https://github.com/flutter/flutter/releases/tag/3.36.0-0.4.pre", "source": "flutter_releases", "section": "Releases", "publishedAt": "2025-08-28T18:29:08.000Z", "summary": "This is innocuous, but we can download it from the SDK archive to ensure the file exists/does not exist.\nTowards #172011."}, {"id": "tag:github.com,2008:Repository/31792824/3.36.0-0.5.pre", "title": "3.36.0-0.5.pre: [3.36] CP #174459 (Fix bug in test_golden_comparator) (#174603)", "url": "https://github.com/flutter/flutter/releases/tag/3.36.0-0.5.pre", "source": "flutter_releases", "section": "Releases", "publishedAt": "2025-08-27T23:02:04.000Z", "summary": "Manual CP for #174459."}, {"id": "tag:github.com,2008:Repository/31792824/3.36.0-0.3.pre", "title": "3.36.0-0.3.pre: [3.36] CP #174459 (Fix bug in test_golden_comparator) (#174603)", "url": "https://github.com/flutter/flutter/releases/tag/3.36.0-0.3.pre", "source": "flutter_releases", "section": "Releases", "publishedAt": "2025-08-27T23:02:04.000Z", "summary": "Manual CP for #174459."}, {"id": "tag:github.com,2008:Repository/31792824/3.36.0-0.2.pre", "title": "3.36.0-0.2.pre: Prepare hotfixes for `3.36.X` (#174380)", "url": "https://github.com/flutter/flutter/releases/tag/3.36.0-0.2.pre", "source": "flutter_releases", "section": "Releases", "publishedAt": "2025-08-25T17:21:35.000Z", "summary": "... just an engine.version is needed (today)."}, {"id": "tag:github.com,2008:Repository/31792824/3.35.2", "title": "Prepare to publish `3.35.2` (#174377)", "url": "https://github.com/flutter/flutter/releases/tag/3.35.2", "source": "flutter_releases", "section": "Releases", "publishedAt": "2025-08-25T17:21:35.000Z", "summary": "... more CHANGELOG.md updates, another bump to engine.revision."}, {"id": "tag:github.com,2008:Repository/31792824/3.36.0-0.1.pre", "title": "3.36.0-0.1.pre: [3.36] Create `engine.version` (#173749)", "url": "https://github.com/flutter/flutter/releases/tag/3.36.0-0.1.pre", "source": "flutter_releases", "section": "Releases", "publishedAt": "2025-08-14T00:21:27.000Z", "summary": "Pointing at 877970c."}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-254.0.dev", "title": "3.10.0-254.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-254.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-28T00:03:06.000Z", "summary": "3.10.0-254.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-253.0.dev", "title": "3.10.0-253.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-253.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-27T16:03:16.000Z", "summary": "3.10.0-253.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-252.0.dev", "title": "3.10.0-252.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-252.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-27T00:02:59.000Z", "summary": "3.10.0-252.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-251.0.dev", "title": "3.10.0-251.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-251.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-26T20:09:44.000Z", "summary": "3.10.0-251.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-250.0.dev", "title": "3.10.0-250.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-250.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-26T16:04:24.000Z", "summary": "3.10.0-250.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-249.0.dev", "title": "3.10.0-249.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-249.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-26T12:03:09.000Z", "summary": "3.10.0-249.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-248.0.dev", "title": "3.10.0-248.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-248.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-26T00:03:21.000Z", "summary": "3.10.0-248.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-247.0.dev", "title": "3.10.0-247.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-247.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-25T20:09:08.000Z", "summary": "3.10.0-247.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-246.0.dev", "title": "3.10.0-246.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-246.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-25T16:03:25.000Z", "summary": "3.10.0-246.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-245.0.dev", "title": "3.10.0-245.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-245.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-25T12:09:25.000Z", "summary": "3.10.0-245.0.dev"}, {"id": "urn:uuid:5a6669cf-ae24-4e56-bf1f-aad9045c0a0b", "title": "v0.11.3 of flutter_gemma", "url": "https://pub.dev/packages/flutter_gemma", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T17:09:57.384Z", "summary": "The plugin allows running the Gemma AI model locally on a device from a Flutter application. Includes support for Gemma 3 Nano models with optimized MediaPipe GenAI v0.10.24.\n\nChangelog excerpt:\n- 🌐 **Web Multimodal Support**: Added full multimodal image processing support for web platform\n- 📚 **MediaPipe 0.10.25**: Updated to MediaPipe GenAI v0.10.25 for web compatibility\n- 📦 **LiterTLM Format Support**: Added support for `.litertlm`model files optimized for web platform"}, {"id": "urn:uuid:533340e1-0810-4c80-878f-4ee8efb517b3", "title": "v1.9.1 of simple_ui", "url": "https://pub.dev/packages/simple_ui", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T16:49:19.084Z", "summary": "自定义简单 UI 组件"}, {"id": "urn:uuid:ae71cc12-194c-453b-bfaa-e8ea2dbc76fd", "title": "v4.4.12 of flutter_slick", "url": "https://pub.dev/packages/flutter_slick", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T16:48:35.489Z", "summary": "Slick patterns, components, navigation and services for Flutter.\n\nChangelog excerpt:\n- Added loading indicator for while rewrite code runs"}, {"id": "urn:uuid:e694a80a-1229-4d80-bc42-483513ee116c", "title": "v0.0.1 of puzzlify", "url": "https://pub.dev/packages/puzzlify", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T16:41:46.641Z", "summary": "A Flutter widget that turns any image into an interactive puzzle game.\n\nChangelog excerpt:\n### Added\n\n- Initial release of Puzzlify 🎉\n- Support for creating puzzles from local images (Uint8List).\n- Puzzle grid with customizable rows and columns.\n- Controller with `recreate`, `derange`, and `check`methods.\n- Basic shimmer loading state while image is being processed."}, {"id": "urn:uuid:3514ba47-58df-4d30-bdb2-9118dcee911b", "title": "v4.4.11 of flutter_slick", "url": "https://pub.dev/packages/flutter_slick", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T16:40:34.556Z", "summary": "Slick patterns, components, navigation and services for Flutter.\n\nChangelog excerpt:\n- Added loading indicator for while rewrite code runs"}, {"id": "urn:uuid:66f70ac3-659e-4589-9820-e6a2f80d0a0d", "title": "v0.0.9 of hivez", "url": "https://pub.dev/packages/hivez", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T16:31:54.089Z", "summary": "The cleanest way to use Hive in production. Type-safe, concurrency-safe, boilerplate-free. (Using hive_ce)\n\nChangelog excerpt:\n- Improved API structure, type safety and made unnecessary public members private\n- Improved logging performance by using a function builder instead of a string literal\n- Added basic logs to `initialize`, `flush`, `compact`, `deleteFromDisk`, and `closeBox`operations\n- Added extensive tests for backup extension methods for all box types testing both JSON and compressed backups and many more tests for all box types\n- Fixed missing exports for extension methods\n- To improve the auto-completion and[...]"}, {"id": "urn:uuid:afddb9f7-4adb-4507-a241-728c2db2f655", "title": "v4.4.10 of flutter_slick", "url": "https://pub.dev/packages/flutter_slick", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T16:29:16.575Z", "summary": "Slick patterns, components, navigation and services for Flutter.\n\nChangelog excerpt:\n- Added loading indicator for while rewrite code runs"}, {"id": "urn:uuid:c14e30ff-ec86-46f6-b70f-f2c0f5b65198", "title": "v0.1.1 of nexusevent_flutter", "url": "https://pub.dev/packages/nexusevent_flutter", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T16:28:07.010Z", "summary": "NexusEvent Flutter SDK - 跨平台消息推送SDK，支持Discord、Slack等多种平台\n\nChangelog excerpt:\n### Added\n\n- Initial Flutter SDK release\n- Discord webhook support with rich embed functionality\n- Slack webhook and Bot API integration\n- Telegram Bot API support\n- Cross-platform compatibility (iOS, Android, Web, Desktop)\n- Comprehensive error handling and validation\n- Asynchronous API design\n- Type-safe configuration classes\n\n### Features\n\n- `NexusEventClient`main client class\n- `DiscordWebhookConfig`for Discord webhook configuration\n- `SlackWebhookConfig`for Slack webhook configuration\n- `Te[...]"}, {"id": "urn:uuid:1bae1588-9cdc-49eb-8964-b40424d36204", "title": "v1.9.0 of simple_ui", "url": "https://pub.dev/packages/simple_ui", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T16:26:15.026Z", "summary": "自定义简单 UI 组件"}, {"id": "urn:uuid:8072d625-9002-4927-b631-84aa1b92e02f", "title": "v0.2.3 of flutter_bloc_form_plus", "url": "https://pub.dev/packages/flutter_bloc_form_plus", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T16:25:57.629Z", "summary": "Create Beautiful Forms in Flutter. The easiest way to Prefill, Async Validation, Update Form Fields, and Show Progress, Failures or Navigate by Reacting to the Form State.\n\nChangelog excerpt:\n### Added\n\n- **`showTitle`property**to `StepperFormBlocBuilder`   - Default: `true`(backwards compatible).\n  - Allows hiding stepper titles when set to `false`.\n \n\n## Adds flexibility for cleaner UIs (e.g., when using a progress bar or when step labels are unnecessary).\n\n### Example\n\n```\n`StepperFormBlocBuilder( formBloc: context.read(), type: StepperType.horizontal, showTitle: false, // hides step titles );````"}, {"id": "urn:uuid:203d52bd-8a84-4354-b9c1-b6676b48fca9", "title": "v0.2.3 of bloc_form_plus", "url": "https://pub.dev/packages/bloc_form_plus", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T16:25:03.725Z", "summary": "Easy Form State Management using BLoC pattern. Separate the Form State and Business Logic from the User Interface. Async Validation, Progress, Dynamic fields, and more.\n\nChangelog excerpt:\n- **Minor fix of changelog arrangement**"}, {"id": "urn:uuid:2be3c35e-8943-45d5-b343-9e1b8136bec9", "title": "v0.5.0 of openai_webrtc", "url": "https://pub.dev/packages/openai_webrtc", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T15:59:03.079Z", "summary": "webrtc support for openai_core and the OpenAI realtime api\n\nChangelog excerpt:\nUpdate to 0.8.0 openai_core"}, {"id": "urn:uuid:851b9c27-f79d-4b53-b484-82f0131b7828", "title": "v0.8.0 of openai_core", "url": "https://pub.dev/packages/openai_core", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T15:57:46.780Z", "summary": "openai support for dart including the responses api, the realtime api, and more.\n\nChangelog excerpt:\nAdd support for realtime usage, add more realtime events"}, {"id": "urn:uuid:5483ec23-d6c9-432c-ace0-a6f0010e283c", "title": "v0.4.0 of tailwindcss_build", "url": "https://pub.dev/packages/tailwindcss_build", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T15:54:55.209Z", "summary": "A comprehensive Flutter package that brings the power and convenience of Tailwind CSS utility classes to Flutter development."}, {"id": "urn:uuid:c8ec7a4b-64f7-4493-aa36-7533184214c7", "title": "v0.7.0 of openai_core", "url": "https://pub.dev/packages/openai_core", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T15:54:16.670Z", "summary": "openai support for dart including the responses api, the realtime api, and more.\n\nChangelog excerpt:\nImprove support for GA Realtime API"}, {"id": "urn:uuid:89baf20d-2b06-4f3a-b19c-1e00224a4016", "title": "v1.5.7 of taboola_sdk_beta", "url": "https://pub.dev/packages/taboola_sdk_beta", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T15:48:04.888Z", "summary": "Taboola's Flutter Core SDK. This SDK provides access to Taboola's Widget and Feed integrations for Flutter applications."}, {"id": "urn:uuid:82d91749-af08-47a0-9735-0350767c9077", "title": "v1.0.0 of radar_chart_plus", "url": "https://pub.dev/packages/radar_chart_plus", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T15:37:59.750Z", "summary": "A versatile Flutter package for creating beautiful and customizable radar charts. Easily visualize multivariate data for comparisons and analysis.\n\nChangelog excerpt:\n- Fixed warnings and improved README\n- Added example folder\n- Added LICENSE file\n- Minor improvements for pub.dev scoring"}, {"id": "urn:uuid:63b1c25b-54c7-4678-99f3-96db45ac2e7b", "title": "v1.1.2 of nui_database", "url": "https://pub.dev/packages/nui_database", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T15:30:39.417Z", "summary": "A new Flutter package."}, {"id": "urn:uuid:d8886021-9b37-4cae-a6cf-1a0c18e528c2", "title": "v7.2.0 of squadron", "url": "https://pub.dev/packages/squadron", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T15:24:35.257Z", "summary": "Multithreading and worker thread pool for Dart / Flutter, to offload CPU-bound and heavy I/O tasks to Isolate or Web Worker threads.\n\nChangelog excerpt:\n- Support sending Dart `DateTime`/ JS `Date`instances to/from Workers.\n- Fix warnings from [pub.dev](https://pub.dev/packages/squadron/score)."}, {"id": "urn:uuid:2774a166-9bb2-41ac-8915-7abca5c6d738", "title": "v0.0.1 of shelf_sse", "url": "https://pub.dev/packages/shelf_sse", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T15:15:20.760Z", "summary": "A shelf handler that wires up a listener for every connection.\n\nChangelog excerpt:\n- Introduce `sse<PERSON><PERSON><PERSON>`for lifting Shelf requests into `SseChannel`s.\n- Provide a server/client example demonstrating bi-directional SSE.\n- Add unit tests covering server-to-client messaging and validation logic."}, {"id": "urn:uuid:62542638-941e-40af-8844-7e4b5047a88e", "title": "v1.5.6 of taboola_sdk_beta", "url": "https://pub.dev/packages/taboola_sdk_beta", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T15:13:22.824Z", "summary": "Taboola's Flutter Core SDK. This SDK provides access to Taboola's Widget and Feed integrations for Flutter applications."}, {"id": "urn:uuid:f0e767c5-702f-425a-8ae8-73f83b0fffb5", "title": "v1.0.1 of nui_error_handler", "url": "https://pub.dev/packages/nui_error_handler", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T15:11:32.472Z", "summary": "A new Flutter package for error handling"}, {"id": "urn:uuid:da96feea-a140-4379-85c2-6efa2a9df23a", "title": "v2.1.2 of ecache", "url": "https://pub.dev/packages/ecache", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T15:06:45.470Z", "summary": "Provide a dart cache library usable on web, flutter and server side\n\n\nChangelog excerpt:\n- Fix: prevent exception if cache is disposed() and async entry finishes afterwards"}, {"id": "urn:uuid:5ff9fe52-7b81-4e89-9757-12c43363db77", "title": "v1.3.2 of ai_providers", "url": "https://pub.dev/packages/ai_providers", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T15:00:49.985Z", "summary": "Unified AI provider integration for Flutter - OpenAI, Google AI, xAI, Android native TTS with consistent API.\n\nChangelog excerpt:\n### 🔧 Mejoras en API\n\n- **`AiAudioParams.audioFormat`con valor por defecto**: Ahora es `String`(no nullable) con valor por defecto `'pcm'`\n- **Uso simplificado**: `AiAudioParams()`sin parámetros funciona perfectamente para casos comunes\n- **PCM universal**: Formato recomendado compatible con todos los proveedores\n\n### ⚡ Breaking Changes Menores\n\n- `audioFormat`cambió de `String?`a `String`con valor por defecto `'pcm'`\n- Eliminadas verificaciones de null innecesarias en providers\n\n### 📚 Documen[...]"}, {"id": "urn:uuid:db2829ef-eaef-444c-9e5d-086f67457941", "title": "v0.0.1+1-alpha of fdevs_pops", "url": "https://pub.dev/packages/fdevs_pops", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T14:57:32.072Z", "summary": "A powerful and flexible Flutter package for managing overlays such as dialogs, snackbars, and custom overlays with unique identifiers."}, {"id": "urn:uuid:eb5bff8b-18b3-428c-aa97-408274ff7e2a", "title": "v1.0.1 of nui_map", "url": "https://pub.dev/packages/nui_map", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T14:54:47.850Z", "summary": "A new Flutter package for Google Map"}, {"id": "urn:uuid:fe99f613-5c4c-42e9-9fc1-f24ca4bf7413", "title": "v0.0.15 of openvidu_flutter", "url": "https://pub.dev/packages/openvidu_flutter", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T14:50:13.784Z", "summary": "Migration from openvidu-android to Flutter. The package contains a working example that targets OpenVidu 2.29.0.\n\nChangelog excerpt:\n### Fixed\n\n- Fixed API design issues in request_config.dart by making private types public\n- Removed unreachable switch default case in Config._getMethod()\n- Fixed duplicate @override annotations in PlainResponse class\n- Resolved all library_private_types_in_public_api warnings\n- Improved code quality and adherence to Dart best practices"}, {"id": "urn:uuid:cbd9e9c6-84c7-4905-a3c0-45ad53732d49", "title": "v1.0.2 of iversioning", "url": "https://pub.dev/packages/iversioning", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T14:47:22.379Z", "summary": "A new Flutter package."}, {"id": "urn:uuid:9d51150e-f3c7-4e5d-872c-341c07842aba", "title": "v1.3.1 of ai_providers", "url": "https://pub.dev/packages/ai_providers", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T14:43:05.096Z", "summary": "Unified AI provider integration for Flutter - OpenAI, Google AI, xAI, Android native TTS with consistent API.\n\nChangelog excerpt:\n### 🧹 Simplificación y Mejoras\n\n- **Eliminado `TranscribeInstructions`**: Simplificamos la arquitectura de audio eliminando la clase `TranscribeInstructions`que tenía características no utilizadas (anti-hallucination). Ahora `AI.listen()`usa directamente `AISystemPrompt`.\n- **`AiAudioParams`clarificado**: La documentación ahora especifica claramente que `AiAudioParams`es exclusivamente para **síntesis de voz (TTS)**con `AI.speak()`, no para transcripción.\n- **Demo actualizado**: El ejemplo `aud[...]"}, {"id": "urn:uuid:ae3da23c-85fb-463b-959a-7f80945a0ed0", "title": "v1.3.0 of enroll_plugin", "url": "https://pub.dev/packages/enroll_plugin", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T14:37:54.381Z", "summary": "eNROLL is a compliance solution that prevents identity fraud and phishing. Powered by AI, it reduces errors and speeds up identification, ensuring secure verification.\n\nChangelog excerpt:\n- Updating Innovatrics to be 8.11.0."}, {"id": "urn:uuid:4f189a97-c825-46c6-8059-a7b047098ecd", "title": "v0.6.1 of pars_validator", "url": "https://pub.dev/packages/pars_validator", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T14:37:07.412Z", "summary": "A lightweight Flutter library for validating Persian-specific data formats like national codes,bank card numbers, phone numbers, Shamsi dates, and more.\n\nChangelog excerpt:\nString options improvements\n\n- Added `General.isNumeric('123')`and `'123'.isNumeric()`utility:   - Supports English, Persian and Arabic-Indic digits.\n  - Can validate both integers and decimal numbers (using `.`, `,`, or `٫`as decimal separator)."}, {"id": "urn:uuid:34d26d14-a40d-49a8-bea5-3f2ce3f486b2", "title": "v2.0.2 of signals_async", "url": "https://pub.dev/packages/signals_async", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T14:09:07.005Z", "summary": "A reactive asynchronous signal library that extends the signals package with ComputedFuture and ComputedStream for handling async operations and streams reactively.\n\nChangelog excerpt:\n- Skip loading state on chained initial values"}, {"id": "urn:uuid:3bd02f74-5367-4fb4-993d-dc6c4d93937d", "title": "v1.5.5 of taboola_sdk_beta", "url": "https://pub.dev/packages/taboola_sdk_beta", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:56:49.555Z", "summary": "Taboola's Flutter Core SDK. This SDK provides access to Taboola's Widget and Feed integrations for Flutter applications."}, {"id": "urn:uuid:bddae768-6580-40a5-a0d6-a5989fc86279", "title": "v0.0.1 of sketchura_ui", "url": "https://pub.dev/packages/sketchura_ui", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:53:44.799Z", "summary": "Sketchura UI is a Flutter UI builder package that helps you reduce boilerplate code and build modern interfaces faster. It provides simple, reusable, and customizable components to speed up your devel [...]\n\nChangelog excerpt:\n- Initial release of Sketchura UI.\n- Added CustomButton widget."}, {"id": "urn:uuid:d45efa16-48a5-46e1-9c55-63b07b25436f", "title": "v0.0.1 of sketchura", "url": "https://pub.dev/packages/sketchura", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:48:14.304Z", "summary": "Sketchura UI is a Flutter UI builder package that helps you reduce boilerplate code and build modern interfaces faster. It provides simple, reusable, and customizable components to speed up your devel [...]\n\nChangelog excerpt:\n- Initial release of Sketchura UI.\n- Added CustomButton widget."}, {"id": "urn:uuid:e82f6ac8-1395-4965-811c-13bfb1ce180f", "title": "v1.0.6 of linklytics_flutter", "url": "https://pub.dev/packages/linklytics_flutter", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:41:40.726Z", "summary": "A Flutter plugin for integrating Linklytics analytics into your Flutter applications."}, {"id": "urn:uuid:16a643f6-c908-4c21-b0f8-2fe6bbb9213e", "title": "v0.0.13 of todo_flutter", "url": "https://pub.dev/packages/todo_flutter", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:38:15.745Z", "summary": "Flutter project development scaffold\n\nChangelog excerpt:\n- fix BaseRequest bugs, when params = {}"}, {"id": "urn:uuid:257c2a47-26a1-4236-955a-d794cc26c4e2", "title": "v0.3.0 of enhanced_preferences", "url": "https://pub.dev/packages/enhanced_preferences", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:37:47.240Z", "summary": "Wraps platform-specific persistent storage for simple data.\n\nChangelog excerpt:\n- feat: Implements `keys()`.\n- fix(darwin): Support CocoaPods.\n- ci: Improve tests.\n- ci: Add GitHub Actions.\n- chore: Bump flutter_lints.\n- chore: Remove unused import.\n- doc: Add badges.\n- doc(darwin): Add macOS.\n- doc(darwin): Update podspec document."}, {"id": "urn:uuid:f44a702c-9285-4e33-9277-504bdc797928", "title": "v0.0.2 of one_firebase", "url": "https://pub.dev/packages/one_firebase", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:36:02.189Z", "summary": "A comprehensive Firebase service wrapper for Flutter with Riverpod integration, providing type-safe CRUD operations, batch updates, pagination, and real-time data streaming for Firestore."}, {"id": "urn:uuid:f2c05469-f05e-43db-9481-7644af0cf00b", "title": "v2.0.2 of amount_to_word", "url": "https://pub.dev/packages/amount_to_word", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:32:13.191Z", "summary": "A comprehensive Flutter package for converting numbers and amounts to words in multiple languages (Persian, English, Turkish) with full currency support and smart pluralization.\n\nChangelog excerpt:\n### Fixed\n\n- **Turkish Currency**: Fixed \"kurus\" to \"kuruş\" in Turkish Lira sub-unit name\n- **App Name**: Updated partner app name to \"Fida Invoice\" in English"}, {"id": "urn:uuid:248dd9d4-**************-11d97eaa3905", "title": "v0.0.1 of linklytics_feedback", "url": "https://pub.dev/packages/linklytics_feedback", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:29:25.031Z", "summary": "A Flutter package for collecting and managing user feedback with built-in analytics and reporting capabilities.\n\nChangelog excerpt:\n### Added\n\n- Initial release of Linklytics Feedback package\n- Core feedback collection widgets\n- Basic analytics integration\n- Customizable feedback forms\n- Support for multiple feedback types (ratings, comments, suggestions)\n- Real-time feedback reporting capabilities\n- User-friendly interface components\n\n### Features\n\n- Easy integration with existing Flutter applications\n- Lightweight and performant feedback collection\n- Built-in analytics for feedback tracking\n- Customizable UI components to [...]"}, {"id": "urn:uuid:f564219d-1bbc-4066-9593-24c90fc8942c", "title": "v0.0.1 of one_firebase", "url": "https://pub.dev/packages/one_firebase", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:28:07.305Z", "summary": "A comprehensive Firebase service wrapper for Flutter with Riverpod integration, providing type-safe CRUD operations, batch updates, pagination, and real-time data streaming for Firestore.\n\nChangelog excerpt:\n- TODO: Describe initial release."}, {"id": "urn:uuid:33b69ccd-431c-4f23-b487-09b2eb3e825a", "title": "v0.0.1 of linklytics_apps", "url": "https://pub.dev/packages/linklytics_apps", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:25:54.505Z", "summary": "Package to get User apps from Linklytics\n\nChangelog excerpt:\n### Added\n\n- Initial release of Linklytics Apps package\n- Core `LinklyticsApps`class for managing app listings\n- `App`model with support for Google Play Store and App Store URLs\n- `StoreData`model for handling app store analytics data\n- `AppsList`widget for displaying apps in a list format\n- `AppItem`widget for individual app display\n- `ApiService`for handling API communications\n- `AppListController`for state management using GetX\n- Customizable `AppListStrings`for UI text localization\n- Platfor[...]"}, {"id": "urn:uuid:dbd2fd5b-b0d0-45fb-84df-aa977291f37f", "title": "v0.0.14 of openvidu_flutter", "url": "https://pub.dev/packages/openvidu_flutter", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:23:44.511Z", "summary": "Migration from openvidu-android to Flutter. The package contains a working example that targets OpenVidu 2.29.0.\n\nChangelog excerpt:\n### Fixed\n\n- Added missing type annotations across all source files\n- Fixed missing return types for methods in RemoteParticipant, CustomWebSocket, and ApiClient\n- Improved type safety with explicit variable declarations\n- Enhanced code maintainability and IDE support"}, {"id": "urn:uuid:353d6c93-d82f-407b-9cce-3df3acbac013", "title": "v1.0.3 of change_project_name", "url": "https://pub.dev/packages/change_project_name", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:17:06.484Z", "summary": "A CLI tool to rename Flutter/Dart projects and update all package references automatically"}, {"id": "urn:uuid:15628523-4eb2-40c9-99e0-5e4b65988563", "title": "v0.0.62 of juggle_im", "url": "https://pub.dev/packages/juggle_im", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:16:57.609Z", "summary": "Juggle IM SDK"}, {"id": "urn:uuid:fcf6862e-**************-8a2890b4962b", "title": "v2.0.1 of amount_to_word", "url": "https://pub.dev/packages/amount_to_word", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:15:26.240Z", "summary": "A comprehensive Flutter package for converting numbers and amounts to words in multiple languages (Persian, English, Turkish) with full currency support and smart pluralization.\n\nChangelog excerpt:\n### Added\n\n- **Support Section**: Added support section for فاکتور فیدا app in README\n- **Partnership**: Official partnership with فاکتور فیدا - professional invoice application"}, {"id": "urn:uuid:70551625-bbf4-4eae-af5f-8ca1979e2c39", "title": "v1.1.0 of loredart_tensor", "url": "https://pub.dev/packages/loredart_tensor", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T13:05:53.434Z", "summary": "A Dart-pure package for manipulation with tensors (multidimensional arrays of data) inspired by the TensorFlow API.\n\nChangelog excerpt:\n- Update dependencies to Dart version 3"}, {"id": "urn:uuid:b73b0ccb-7813-4d9c-aac5-e4f3cefff6e6", "title": "v2.0.0 of amount_to_word", "url": "https://pub.dev/packages/amount_to_word", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T12:52:32.278Z", "summary": "A comprehensive Flutter package for converting numbers and amounts to words in multiple languages (Persian, English, Turkish) with full currency support and smart pluralization.\n\nChangelog excerpt:\n### Added\n\n- **Turkish Language Support**: Complete number-to-word conversion for Turkish language\n- **Multi-language Currency Units**: Currency units now support Persian, English, and Turkish\n- **Smart Pluralization**: Automatic singular/plural handling for English currencies\n- **Extensible Architecture**: Easy to add new languages via `addLanguage()`method\n- **Comprehensive Currency Support**: 7 predefined currencies with proper localization\n- **Enhanced Testing**: 100% test coverage with edge[...]"}, {"id": "urn:uuid:4f2c3ea8-a481-49fd-a029-1cba5d7f3423", "title": "v0.0.13 of openvidu_flutter", "url": "https://pub.dev/packages/openvidu_flutter", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T12:45:27.627Z", "summary": "Migration from openvidu-android to Flutter. The package contains a working example that targets OpenVidu 2.29.0.\n\nChangelog excerpt:\n### Changed\n\n- Updated Android Gradle Plugin from 7.3.0 → 8.6.1\n- Updated Kotlin version from 1.7.10 → 2.1.0\n- Updated Gradle version from 7.6.3 → 8.7\n- Updated compileSdk from flutter.compileSdkVersion → 36\n- Updated targetSdkVersion from flutter.targetSdkVersion → 36\n- Enhanced JVM arguments with better memory management\n- Added compatibility flags for Android builds\n- Enabled Gradle caching and parallel builds for better performance\n\n### Fixed\n\n- Resolved Kotlin compilation errors in Flutter [...]"}, {"id": "urn:uuid:4eb59e32-4466-40ed-b797-fe8625f24b9a", "title": "v1.5.4 of taboola_sdk_beta", "url": "https://pub.dev/packages/taboola_sdk_beta", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T12:33:23.536Z", "summary": "Taboola's Flutter Core SDK. This SDK provides access to Taboola's Widget and Feed integrations for Flutter applications."}, {"id": "urn:uuid:c0502d27-2a70-4511-bb92-a9263ee3ca4c", "title": "v0.0.2 of responsive_scaler", "url": "https://pub.dev/packages/responsive_scaler", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T12:16:01.975Z", "summary": "A Flutter package that offers automatic, boilerplate-free responsive scaling for text, icons, and spacing across different screen sizes.\n\nChangelog excerpt:\n- Updated and improved documentation\n- Migrated to flutter_lints 6.0.0 for better linting support"}, {"id": "urn:uuid:8f7459d2-a572-46dd-85ac-c512fd43dd0d", "title": "v7.0.3 of biometric_signature", "url": "https://pub.dev/packages/biometric_signature", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T12:12:02.605Z", "summary": "Flutter biometric functionality for cryptographic signing and encryption using the Secure Enclave and the StrongBox\n\nChangelog excerpt:\n- Updating documentations.\n- Minor bug fixes."}, {"id": "urn:uuid:b9f35eba-4e94-4ebb-82b6-e6a6928118ea", "title": "v6.2.3+1 of dart_test_tools", "url": "https://pub.dev/packages/dart_test_tools", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T12:07:51.193Z", "summary": "Additional tools and helpers for writing dart unit tests and GitHub Actions Workflows.\n\nChangelog excerpt:\n### Changed\n\n- Allow build 4.0.0"}, {"id": "urn:uuid:105945e4-5edd-4f4f-a5ce-7916958aa93d", "title": "v0.0.1 of responsive_scaler", "url": "https://pub.dev/packages/responsive_scaler", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T12:04:52.079Z", "summary": "A Flutter package that offers automatic, boilerplate-free responsive scaling for text, icons, and spacing across different screen sizes."}, {"id": "urn:uuid:27e73082-6494-4715-a543-f95909085b04", "title": "v1.1.1 of nigeria_geo_sdk", "url": "https://pub.dev/packages/nigeria_geo_sdk", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T12:00:39.675Z", "summary": "High-performance Flutter SDK for comprehensive Nigerian geographic data including states, LGAs, wards, and postal codes. Zero configuration required.\n\nChangelog excerpt:\n### Fixed\n\n- **CRITICAL**: Removed unnecessary `assets/config.yaml`requirement from pubspec.yaml\n- Fixed iOS build error: \"No file or variants found for asset: packages/nigeria_geo_sdk/assets/config.yaml\"\n- SDK now works completely without any configuration files (as intended)\n\n### Notes\n\n- This is a critical hotfix for the asset loading issue\n- All functionality remains the same - SDK uses built-in optimized configuration\n- Users who were getting build errors should update to this version immediately"}, {"id": "urn:uuid:********-9eb9-44d0-875f-1db8970d748f", "title": "v0.0.1+2 of fast_app_base", "url": "https://pub.dev/packages/fast_app_base", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T11:58:38.549Z", "summary": "Kickstart your Flutter app with fast_app_base — a ready-to-use foundation offering theme, routing, internationalization, dialogs, and Snackbar support out of the box.\n\nChangelog excerpt:\nTemplate bug fix"}, {"id": "urn:uuid:42605b54-c490-4b4f-b26e-7659d828bc09", "title": "v1.0.2 of flutter_text_parser", "url": "https://pub.dev/packages/flutter_text_parser", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T11:51:12.500Z", "summary": "flutter_text_parser is a Dart library that allows you to parse any string with custom tags and render it as styled spanned text. It supports inline formatting like bold, italic, colors, and more, maki [...]\n\nChangelog excerpt:\n- Initial release."}, {"id": "urn:uuid:97953e6e-d697-45cf-9940-e533c5a8f590", "title": "v1.5.3 of taboola_sdk_beta", "url": "https://pub.dev/packages/taboola_sdk_beta", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T11:39:04.094Z", "summary": "Taboola's Flutter Core SDK. This SDK provides access to Taboola's Widget and Feed integrations for Flutter applications."}, {"id": "urn:uuid:06ee491d-5e57-4662-a996-89378f95997e", "title": "v2.0.24 of in_app_purchaser", "url": "https://pub.dev/packages/in_app_purchaser", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T11:25:31.790Z", "summary": "It simplifies the integration of in-app purchase services, handles subscription lifecycle events, provides revenue analytics, and ensures a smooth user experience across iOS and Android.\n\nChangelog excerpt:\n- Add localized contents [price]\n- Filter unnecessary price zeros [ex. 1.304 => 1.3, 4.00 => 4, 54.4783 => 54.48]\n- Rename currency reference name [{CURRENCY_NAME} => {CURRENCY_CODE}, {CURRENCY_SIGN} => {CURRENCY_SYMBOL}]\n- Improved Restore purchase status [restoredAvailable => exist, restoredUnavailable => empty]"}, {"id": "urn:uuid:e613dc80-713c-4f0d-b1fd-ddace9665403", "title": "v1.5.2 of taboola_sdk_beta", "url": "https://pub.dev/packages/taboola_sdk_beta", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T11:22:41.755Z", "summary": "Taboola's Flutter Core SDK. This SDK provides access to Taboola's Widget and Feed integrations for Flutter applications."}, {"id": "urn:uuid:d330c90e-8d6a-40e0-8f09-e0f0b462e84f", "title": "v0.0.6 of king_helper", "url": "https://pub.dev/packages/king_helper", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T11:15:00.855Z", "summary": "A Flutter package for helper methods and widgets\n\nChangelog excerpt:\n- Dependencies updated."}, {"id": "urn:uuid:bd3952d8-dcab-47df-882a-801679b1892d", "title": "v0.0.54 of king_cache", "url": "https://pub.dev/packages/king_cache", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T11:13:09.546Z", "summary": "A Flutter package for caching api data and markdown content to disk and improving app performance. It uses the http package to make api calls and use cache directory to cache data with specialized sup [...]\n\nChangelog excerpt:\n- Fixed the issues with web\n- Renamed Couple of Functions\n- Updated Example app to have all those\n- if you find any issues, please create an issue on github and we will fix it as soon as possible"}, {"id": "urn:uuid:19ba624e-6c54-48e2-82df-463fb529b19e", "title": "v0.1.15 of alghwalbi_core_app", "url": "https://pub.dev/packages/alghwalbi_core_app", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T11:12:43.521Z", "summary": "alghwalbi_core_app package is primarily intended for personal use, but feel free to use it in your projects."}, {"id": "urn:uuid:98bd29de-5d13-4db6-8658-0f7f569b6609", "title": "v1.1.4 of auro_stream_live", "url": "https://pub.dev/packages/auro_stream_live", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T11:07:03.072Z", "summary": "Auro Stream Live Stream Plugin for iOS/Android/Desktop"}, {"id": "urn:uuid:a1044dd9-4236-46d2-ab04-103ec9960112", "title": "v0.0.8 of hivez", "url": "https://pub.dev/packages/hivez", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T10:52:55.980Z", "summary": "The cleanest way to use Hive in production. Type-safe, concurrency-safe, boilerplate-free. (Using hive_ce)\n\nChangelog excerpt:\n- Improved performance by removing unnecessary checks and validation while making the package even more type safe and flexible\n- Added search extension methods for all box types, and added extensive tests with all box types   - `search`for searching the box for values that match the search query. It supports pagination, sorting and improved search with multiple search terms.\n \n- Fixed casting issues with isolated boxes"}, {"id": "urn:uuid:a730755a-ea76-4513-908c-2caa1d4ca1eb", "title": "v1.0.0 of country_code_max", "url": "https://pub.dev/packages/country_code_max", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T10:35:03.173Z", "summary": "A beautiful, animated, and theme-aware country code picker for Flutter that works seamlessly across web and mobile platforms.\n\nChangelog excerpt:\n### Added\n\n- Initial release of Country Code Max\n- Beautiful animated country code picker component\n- Theme-aware design that adapts to light and dark themes\n- Cross-platform support for web and mobile\n- Smart search functionality by country name, code, or dial code\n- Favorites system for frequently used countries\n- Optional country flags display\n- Form validation support with error states\n- Responsive design for different screen sizes\n- Smooth animations and micro-interactions\n- Comprehensive d[...]"}, {"id": "urn:uuid:4fb5aa78-8a95-41c9-a417-1c6e89dad614", "title": "v5.0.3 of flutter_yfree", "url": "https://pub.dev/packages/flutter_yfree", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T10:26:38.443Z", "summary": "YFree_Flutter Lite Version, Lightweight framework for Flutter development"}, {"id": "urn:uuid:d4597b3e-6a7c-4e90-b427-94f69fd718a1", "title": "v0.1.9 of dep_audit", "url": "https://pub.dev/packages/dep_audit", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T10:25:59.064Z", "summary": "A Dart and Flutter dependency audit tool that identifies outdated packages and security vulnerabilities.\n\nChangelog excerpt:\n### Fixed\n\n- Shortened package description to comply with pub.dev guidelines and improve pub points score\n- Package description now under 180 characters for better readability"}, {"id": "urn:uuid:1d1d4bed-3058-4f97-995d-37689acfabf1", "title": "v2.0.12+3 of cyber_req", "url": "https://pub.dev/packages/cyber_req", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T10:19:49.169Z", "summary": "A flexible Flutter/Dart API client for backends with dynamic headers, secure token management, and comprehensive callbacks."}, {"id": "urn:uuid:5e3bd4c4-a1f6-4e09-b43f-73a56ac93d60", "title": "v0.9.20 of rdf_core", "url": "https://pub.dev/packages/rdf_core", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T10:17:52.892Z", "summary": "A type-safe, modular Dart library for modeling, encoding, and decoding RDF data.\n\nChangelog excerpt:\n### Changed\n\n- **N-Quads Encoder API**: Removed unused `baseUri`parameter from `convert()`and `encode()`methods for cleaner API\n\n### Fixes\n\n- Formatting fixes (dart format)\n- **N-Quads Encoder**: When set to canonical, it will deduplicate the tuples before outputting the result"}, {"id": "urn:uuid:90f293a5-f42c-4734-aafc-e4ecf2d36608", "title": "v5.0.2 of flutter_yfree", "url": "https://pub.dev/packages/flutter_yfree", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T10:16:11.235Z", "summary": "YFree_Flutter Lite Version, Lightweight framework for Flutter development"}, {"id": "urn:uuid:1a7174f8-3fbd-4897-8d70-50133fea8436", "title": "v2.0.12+2 of cyber_req", "url": "https://pub.dev/packages/cyber_req", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T10:14:48.036Z", "summary": "A flexible Flutter/Dart API client for backends with dynamic headers, secure token management, and comprehensive callbacks."}, {"id": "urn:uuid:dc7fd176-cac3-4311-a5af-17a6980ddaeb", "title": "v1.5.0 of facial_liveness_detection_flutter_plugin", "url": "https://pub.dev/packages/facial_liveness_detection_flutter_plugin", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T10:13:10.229Z", "summary": "人脸活体检测，支持静默，炫彩，眨眼，张嘴，摇头，点头，远近及随意动作组合，支持Android,IOS, HMOS, H5,小程序等"}, {"id": "urn:uuid:8086d6ea-34ed-41e6-a19e-1e92f68a685b", "title": "v1.1.0 of nigeria_geo_sdk", "url": "https://pub.dev/packages/nigeria_geo_sdk", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T10:09:50.428Z", "summary": "High-performance Flutter SDK for comprehensive Nigerian geographic data including states, LGAs, wards, and postal codes. Optimized for fast response times with direct database access.\n\nChangelog excerpt:\n### Added\n\n- Performance optimizations with direct database access architecture\n- Enhanced widget pickers to show comprehensive data (50+ states, 50+ LGAs, 100+ wards)\n- Production server configuration ([http://20.63.52.179:3000](http://20.63.52.179:3000))\n- Comprehensive README with detailed usage examples\n- New pagination demonstration in example app\n- Support for all 37 Nigerian states, 774 LGAs, and 8,840+ wards\n\n### Changed\n\n- **BREAKING**: Reduced default API timeout from 30s to 5s for opt[...]"}, {"id": "urn:uuid:a4872fdb-907d-43bc-b87f-5c90905427ec", "title": "v5.0.1 of flutter_yfree", "url": "https://pub.dev/packages/flutter_yfree", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T10:08:09.133Z", "summary": "YFree_Flutter Lite Version, Lightweight framework for Flutter development"}, {"id": "urn:uuid:6e4732c5-8b61-44b9-ad4d-69479b531fcd", "title": "v5.0.0 of flutter_yfree", "url": "https://pub.dev/packages/flutter_yfree", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T10:06:17.409Z", "summary": "YFree_Flutter Lite Version, Lightweight framework for Flutter development"}, {"id": "urn:uuid:fbe64b11-641f-4f09-ae54-afd386e01390", "title": "v0.0.1+1 of fast_app_base", "url": "https://pub.dev/packages/fast_app_base", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T10:05:48.099Z", "summary": "Kickstart your Flutter app with fast_app_base — a ready-to-use foundation offering theme, routing, internationalization, dialogs, and Snackbar support out of the box."}, {"id": "urn:uuid:76e8239a-2d47-42a0-ad3e-09b7319957c4", "title": "v0.2.0 of rdf_canonicalization", "url": "https://pub.dev/packages/rdf_canonicalization", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T10:04:45.571Z", "summary": "A Dart library for RDF graph canonicalization and isomorphism testing, implementing the W3C RDF Dataset Canonicalization specification.\n\nChangelog excerpt:\n### Fixed\n\n- **Test Suite Compliance**: Fixed critical bugs to achieve full compliance with the official W3C RDF canonicalization test suite\n- **Specification Alignment**: Re-implemented n-degree hashing algorithm to more closely align with the W3C specification\n- **Implementation Structure**: Improved canonicalization implementation with better quad handling and processing\n\n### Changed\n\n- **Code Quality**: Introduced typedefs to improve code readability and maintainability\n- **Test Infrastructu[...]"}, {"id": "urn:uuid:4bedfb42-2e0f-4c4d-a801-e6f8dbb4cd03", "title": "v0.1.0 of photo_viewer", "url": "https://pub.dev/packages/photo_viewer", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T10:03:17.646Z", "summary": "Flutter image viewer library with multiple display modes. Features include: pinch-zoom, double-tap zoom, vertical dismiss, page navigation, custom overlays, and hero animations.\n\nChangelog excerpt:\n### Improvements\n\n- Check whether the url is local image path\n- Expose some GestureDetector properties in PhotoViewerImage\n- Resolve conflict between viewer zoom and horizontal swipes\n- Fix the warnings in Flutter 3.35.4 (Replaced Matrix4.translate and scale with translateByDouble and scaleByDouble)"}, {"id": "urn:uuid:8068dad2-241d-4b14-b9e5-6339a7311ae1", "title": "v1.1.0 of keyboard_utils_plugin", "url": "https://pub.dev/packages/keyboard_utils_plugin", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T09:53:10.917Z", "summary": "fork from https://github.com/IsaiasSantana/keyboard_utils\n\nChangelog excerpt:\n- Flutter swift plugin fix"}, {"id": "urn:uuid:b0c532af-c83f-4897-9723-6b2a15545240", "title": "v0.0.4 of l_value_extensions", "url": "https://pub.dev/packages/l_value_extensions", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T09:53:06.195Z", "summary": "值扩展\n\nChangelog excerpt:\n- LMapExtension key 类型由 String 改为 dynamic"}, {"id": "urn:uuid:834307e8-ed35-4a2e-88f1-2c09fdd6e35b", "title": "v0.1.14 of alghwalbi_core_app", "url": "https://pub.dev/packages/alghwalbi_core_app", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T09:52:43.577Z", "summary": "alghwalbi_core_app package is primarily intended for personal use, but feel free to use it in your projects."}, {"id": "urn:uuid:26ca4075-3e9d-4fd4-9f30-74e51363e019", "title": "v1.5.1 of taboola_sdk_beta", "url": "https://pub.dev/packages/taboola_sdk_beta", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T09:43:35.833Z", "summary": "Taboola's Flutter Core SDK. This SDK provides access to Taboola's Widget and Feed integrations for Flutter applications.\n\nChangelog excerpt:\n- fix android issue test"}, {"id": "urn:uuid:7a4bda11-a26e-4ea8-9368-d3731dcbcc6b", "title": "v0.7.1 of video_player_avplay", "url": "https://pub.dev/packages/video_player_avplay", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T09:39:28.364Z", "summary": "Flutter plugin for displaying inline video on Tizen TV devices.\n\nChangelog excerpt:\n- Fix an assertion issue when getting AD information."}, {"id": "urn:uuid:dc2976b7-2f38-48f0-8f5d-346226b6d8e0", "title": "v1.1.2 of text_field_validator", "url": "https://pub.dev/packages/text_field_validator", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T09:33:30.909Z", "summary": "A flutter package for both Android and iOS which provides the validation functionalities for the input fields.\n\nChangelog excerpt:\n- updated document"}, {"id": "urn:uuid:6a367ba8-af1c-4571-bf14-4a012c4d5b51", "title": "v1.1.1 of text_field_validator", "url": "https://pub.dev/packages/text_field_validator", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T09:12:57.680Z", "summary": "A flutter package for both Android and iOS which provides the validation functionalities for the input fields.\n\nChangelog excerpt:\n- added adhar, pan, dl, ifsc, gst input formatter"}, {"id": "urn:uuid:630caa19-72a5-42ea-9ce0-563c2bfa7f2b", "title": "v0.0.4 of turn_bartender", "url": "https://pub.dev/packages/turn_bartender", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T09:11:45.943Z", "summary": "加打印份数的参数"}, {"id": "urn:uuid:f9be82de-8619-49a2-94e2-a3c7a376aa1a", "title": "v1.0.1 of flutter_unify", "url": "https://pub.dev/packages/flutter_unify", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T09:05:08.626Z", "summary": "The ultimate unified API for cross-platform Flutter development. One API, all platforms - legendary developer experience with reactive streams, pluggable adapters, and best-in-class DX.\n\nChangelog excerpt:\n### 🏆 160/160 Pub Points Achievement - Production Ready\n\nThis release focuses on achieving the maximum pub.dev score of 160/160 points through comprehensive code quality improvements, enhanced documentation, and production-ready optimizations.\n\n### 🔧 Fixed - Code Quality & Analysis\n\n#### Static Analysis Improvements\n\n- **Removed Unused Imports**   - Eliminated unused `dart:convert`import from `unified_networking.dart`\n  - Removed unnecessary `dart:typed_data`import from `media.dart`\n  - Cleane[...]"}, {"id": "urn:uuid:ede5dd24-b32e-4947-883c-daac679c5d3a", "title": "v0.0.34 of delta_trace_db", "url": "https://pub.dev/packages/delta_trace_db", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T09:00:54.331Z", "summary": "The NoSQL in-memory database with class-based functionality and detailed operation history tracking.\n\nChangelog excerpt:\n- In search queries, sortObj is no longer required when using offset, startAfter, or endBefore. If not specified, the queries will be processed in the order they were added to the database.\n- The getAll query now supports offset, startAfter, endBefore, and limit, making it easier to implement paging within a collection.\n- Improved QueryBuilder and RawQueryBuilder descriptions."}, {"id": "urn:uuid:0e6cb89d-523a-4082-9749-a7f177941f1c", "title": "v2.2.19 of dart_json_mapper", "url": "https://pub.dev/packages/dart_json_mapper", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T09:00:19.676Z", "summary": "This package allows programmers to annotate Dart objects in order to serialize / deserialize them from / to JSON.\n\n\nChangelog excerpt:\n- #230, Bug: @JsonProperty(flatten: true) fails on deserialization"}, {"id": "urn:uuid:b6b6f0bc-b3a1-4bde-ae2b-dbe055064083", "title": "v1.4.5 of junny_dev_kit", "url": "https://pub.dev/packages/junny_dev_kit", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T08:55:07.165Z", "summary": "JunnySoft开发工具包.\n\nChangelog excerpt:\n### 🔧 菜单系统增强\n\n-  **菜单数据加载器优化**：重构菜单数据加载逻辑，新增批量处理和错误处理机制 • 影响位置：\n\n   - `lib/src/menus/widgets/menu_data_loader.dart`\n \n-  **菜单处理器改进**：优化工作台数据处理器和菜单点击处理器，提升性能和稳定性 • 影响位置：\n\n   - `lib/src/menus/processors/workbench_data_processor.dart`\n  - `lib/src/menus/processors/menu_tap_handler.dart`\n \n-  **菜单组件增强**：改进菜单处理器工厂和通用菜单组件的功能和用户体验 • 影响位置：\n\n   - `lib/src/menus/widgets/menu_handler_factory.dart`\n  - `lib/src/menus/widgets/universal_menu_widget.dart`\n \n-  **菜单配置优化**：更新菜单配置组和页面组件的配置管理 • 影响位置：\n\n   - `lib/s[...]"}, {"id": "urn:uuid:80c6b6ae-6cc2-48ab-8bf9-b10d4e712ab1", "title": "v1.0.0 of keyboard_utils_plugin", "url": "https://pub.dev/packages/keyboard_utils_plugin", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T08:48:33.235Z", "summary": "fork from https://github.com/IsaiasSantana/keyboard_utils\n\nChangelog excerpt:\n- fork keyboard_utils_fork 1.0.1"}, {"id": "urn:uuid:12b26be5-fa51-4a75-8d4a-e1b9e73ceaed", "title": "v0.0.1 of styless_snackbar", "url": "https://pub.dev/packages/styless_snackbar", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T08:47:43.251Z", "summary": "A modern Flutter snackbar/toast package with styles and animations.\n\nChangelog excerpt:\n- Initial release 🎉"}, {"id": "urn:uuid:1b91c177-b370-4193-9c0a-5df2fe43fa4c", "title": "v1.0.6-dev.1 of adseye_ad_plugin", "url": "https://pub.dev/packages/adseye_ad_plugin", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T08:44:45.531Z", "summary": "Adseye Flutter Plugin Project."}, {"id": "urn:uuid:3b53b20b-8f82-4bea-b288-e188bb87fa8b", "title": "v1.0.1 of text_field_validator", "url": "https://pub.dev/packages/text_field_validator", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T08:40:42.766Z", "summary": "A flutter package for both Android and iOS which provides the validation functionalities for the input fields.\n\nChangelog excerpt:\n- uasge document update"}, {"id": "urn:uuid:17042e10-b57b-4c66-9363-21b436c572f8", "title": "v2.2.18 of dart_json_mapper", "url": "https://pub.dev/packages/dart_json_mapper", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T08:36:24.408Z", "summary": "This package allows programmers to annotate Dart objects in order to serialize / deserialize them from / to JSON.\n\n\nChangelog excerpt:\n- #130, (feat) Introduce `rawJson`attribute to `@JsonProperty`meta"}, {"id": "urn:uuid:dc5b02db-931e-48c4-bc59-10884468e0be", "title": "v0.9.19 of dart_periphery", "url": "https://pub.dev/packages/dart_periphery", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T08:30:39.301Z", "summary": "dart_periphery is a Dart port of the native c-periphery library for Linux Peripheral I/O (GPIO, LED, PWM, SPI, I2C, MMIO, ADC and Serial peripheral I/O).\n\nChangelog excerpt:\n- Add GPS [Air530](https://github.com/pezi/dart_periphery/blob/ps/0.9.19/example/serial_air530.dart)\n- Update Dart Version to `3.9.3`"}, {"id": "urn:uuid:8f0173ed-c159-455c-b71b-8dcd4c8ff665", "title": "v6.8.6 of flutter_inapp_purchase", "url": "https://pub.dev/packages/flutter_inapp_purchase", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T08:28:23.116Z", "summary": "In App Purchase plugin for flutter. This project has been forked by react-native-iap and we are willing to share same experience with that on react-native.\n\nChangelog excerpt:\n- feat: fetchProducts with ProductQueryType.All #568"}, {"id": "urn:uuid:9a01d5cc-56bd-4e9d-bf14-4d608ac20a56", "title": "v1.0.0 of text_field_validator", "url": "https://pub.dev/packages/text_field_validator", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T08:27:33.561Z", "summary": "A flutter package for both Android and iOS which provides the validation functionalities for the input fields.\n\nChangelog excerpt:\n- added inputformatter feature in this release\n- supports any kind of input format."}, {"id": "urn:uuid:a8705c9e-b2ea-439d-97af-06ca6d761e03", "title": "v0.0.5 of dsa_kit", "url": "https://pub.dev/packages/dsa_kit", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T17:21:44.145Z", "summary": "A scalable DSA utilities kit for Dart. Includes Heaps, DeQueues, and more.\n\nChangelog excerpt:\n### Added\n\n- Added `INT`extension providing convenient constants:   - `INT.infinity`- Maximum positive integer value\n  - `INT.negativeInfinity`- Minimum negative integer value\n \n- Updated documentation and examples for INT extensions"}, {"id": "https://medium.com/p/c58ef72e3766", "title": "What’s new in Flutter 3.35", "url": "https://blog.flutter.dev/whats-new-in-flutter-3-35-c58ef72e3766?source=rss----4da7dfd21a33---4", "source": "flutter_blog", "section": "Headlines", "publishedAt": "2025-08-14T18:23:26.000Z"}, {"id": "https://medium.com/p/9a8c94564635", "title": "Unleash new AI capabilities for Flutter in Firebase Studio", "url": "https://blog.flutter.dev/unleash-new-ai-capabilities-for-flutter-in-firebase-studio-9a8c94564635?source=rss----4da7dfd21a33---4", "source": "flutter_blog", "section": "Headlines", "publishedAt": "2025-07-23T05:02:36.000Z"}, {"id": "https://medium.com/p/2edcc8107b49", "title": "Supercharge Your Dart & Flutter Development Experience with the Dart MCP Server", "url": "https://blog.flutter.dev/supercharge-your-dart-flutter-development-experience-with-the-dart-mcp-server-2edcc8107b49?source=rss----4da7dfd21a33---4", "source": "flutter_blog", "section": "Headlines", "publishedAt": "2025-07-23T05:02:33.000Z"}, {"id": "https://medium.com/p/4863aa4f84a4", "title": "Dart & Flutter momentum at Google I/O 2025", "url": "https://blog.flutter.dev/dart-flutter-momentum-at-google-i-o-2025-4863aa4f84a4?source=rss----4da7dfd21a33---4", "source": "flutter_blog", "section": "Headlines", "publishedAt": "2025-05-21T18:00:57.000Z"}, {"id": "https://medium.com/p/4bf7d4579d9a", "title": "Flutter’s path towards seamless interop", "url": "https://blog.flutter.dev/flutters-path-towards-seamless-interop-4bf7d4579d9a?source=rss----4da7dfd21a33---4", "source": "flutter_blog", "section": "Headlines", "publishedAt": "2025-05-20T18:03:41.000Z"}, {"id": "https://medium.com/p/915dfec98274", "title": "Gemini in Android Studio now speaks fluent Flutter!", "url": "https://blog.flutter.dev/gemini-in-android-studio-now-speaks-fluent-flutter-915dfec98274?source=rss----4da7dfd21a33---4", "source": "flutter_blog", "section": "Headlines", "publishedAt": "2025-05-20T18:03:09.000Z"}, {"id": "https://medium.com/p/40c1086bab6e", "title": "What’s new in Flutter 3.32", "url": "https://blog.flutter.dev/whats-new-in-flutter-3-32-40c1086bab6e?source=rss----4da7dfd21a33---4", "source": "flutter_blog", "section": "Headlines", "publishedAt": "2025-05-20T18:02:25.000Z"}, {"id": "https://medium.com/p/ad46b38b1adb", "title": "Learn how to build agentic apps with Flutter, Angular, Firebase, and Vertex AI", "url": "https://blog.flutter.dev/learn-how-to-build-agentic-apps-with-flutter-angular-firebase-and-vertex-ai-ad46b38b1adb?source=rss----4da7dfd21a33---4", "source": "flutter_blog", "section": "Headlines", "publishedAt": "2025-04-24T16:23:03.000Z"}, {"id": "https://medium.com/p/f127882b117f", "title": "Flutter 2025 roadmap update", "url": "https://blog.flutter.dev/flutter-2025-roadmap-f127882b117f?source=rss----4da7dfd21a33---4", "source": "flutter_blog", "section": "Headlines", "publishedAt": "2025-04-02T20:27:24.000Z"}, {"id": "https://medium.com/p/f90c380c2317", "title": "What’s new in Flutter 3.29", "url": "https://blog.flutter.dev/whats-new-in-flutter-3-29-f90c380c2317?source=rss----4da7dfd21a33---4", "source": "flutter_blog", "section": "Headlines", "publishedAt": "2025-02-12T19:09:57.000Z"}, {"id": "https://medium.com/p/ba49e8f38298", "title": "Announcing Dart 3.9", "url": "https://blog.dart.dev/announcing-dart-3-9-ba49e8f38298?source=rss----23738d481ce8---4", "source": "dart_blog", "section": "Headlines", "publishedAt": "2025-08-14T18:17:15.000Z"}, {"id": "https://medium.com/p/724eaaec9f47", "title": "Announcing Dart 3.8", "url": "https://blog.dart.dev/announcing-dart-3-8-724eaaec9f47?source=rss----23738d481ce8---4", "source": "dart_blog", "section": "Headlines", "publishedAt": "2025-05-20T18:08:39.000Z"}, {"id": "https://medium.com/p/ca962729cee6", "title": "Gemini for DartPad", "url": "https://blog.dart.dev/gemini-for-dartpad-ca962729cee6?source=rss----23738d481ce8---4", "source": "dart_blog", "section": "Headlines", "publishedAt": "2025-04-08T16:06:49.000Z"}, {"id": "https://medium.com/p/b6373bdb7a08", "title": "Dart in Google Summer of Code 2025", "url": "https://blog.dart.dev/dart-in-google-summer-of-code-2025-b6373bdb7a08?source=rss----23738d481ce8---4", "source": "dart_blog", "section": "Headlines", "publishedAt": "2025-03-27T11:25:19.000Z"}, {"id": "https://medium.com/p/bf864a1b195c", "title": "Announcing Dart 3.7", "url": "https://blog.dart.dev/announcing-dart-3-7-bf864a1b195c?source=rss----23738d481ce8---4", "source": "dart_blog", "section": "Headlines", "publishedAt": "2025-02-12T19:10:27.000Z"}, {"id": "https://medium.com/p/06d3037d4f12", "title": "An update on Dart macros & data serialization", "url": "https://blog.dart.dev/an-update-on-dart-macros-data-serialization-06d3037d4f12?source=rss----23738d481ce8---4", "source": "dart_blog", "section": "Headlines", "publishedAt": "2025-01-29T21:23:22.000Z"}, {"id": "https://medium.com/p/778dd7a80983", "title": "Announcing Dart 3.6", "url": "https://blog.dart.dev/announcing-dart-3-6-778dd7a80983?source=rss----23738d481ce8---4", "source": "dart_blog", "section": "Headlines", "publishedAt": "2024-12-11T20:20:13.000Z"}, {"id": "https://medium.com/p/ae925357d2d7", "title": "Google Summer of Code 2024 Results", "url": "https://blog.dart.dev/google-summer-of-code-2024-results-ae925357d2d7?source=rss----23738d481ce8---4", "source": "dart_blog", "section": "Headlines", "publishedAt": "2024-10-14T17:23:41.000Z"}, {"id": "https://medium.com/p/6ca36259fa2f", "title": "Announcing Dart 3.5, and an update on the Dart roadmap", "url": "https://blog.dart.dev/dart-3-5-6ca36259fa2f?source=rss----23738d481ce8---4", "source": "dart_blog", "section": "Headlines", "publishedAt": "2024-08-06T18:02:40.000Z"}, {"id": "https://medium.com/p/bd8d23b4462a", "title": "Announcing Dart 3.4", "url": "https://blog.dart.dev/dart-3-4-bd8d23b4462a?source=rss----23738d481ce8---4", "source": "dart_blog", "section": "Headlines", "publishedAt": "2024-05-14T20:36:15.000Z"}, {"id": "urn:uuid:00855e65-98cd-46d1-af4e-404f9a2e8e18", "title": "v0.1.6 of jaspr_markdown", "url": "https://pub.dev/packages/jaspr_markdown", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T18:09:31.899Z", "summary": "A Markdown renderer for Jaspr. It lets you use Jaspr components in Markdown. You can import components like MDX.\n\nChangelog excerpt:\n`jaspr`upgraded to `0.21.3`"}, {"id": "1112042", "title": "LLM Data Engineer", "url": "https://remoteOK.com/remote-jobs/remote-llm-data-engineer-sanctuary-computer-1112042", "source": "remoteok", "section": "Jobs", "publishedAt": "2025-09-12T11:44:56.000Z", "summary": "Sanctuary Computer – dev, senior, backend, exec, javascript, ruby, python, node, saas, data science, data entry", "meta": {"company": "Sanctuary Computer", "tags": ["dev", "senior", "backend", "exec", "javascript", "ruby", "python", "node", "saas", "data science", "data entry"]}}, {"id": "urn:uuid:afcfb676-d8d4-4d97-9e0a-19396b863ccd", "title": "v1.3.1 of flutter_reactive_ble_backend", "url": "https://pub.dev/packages/flutter_reactive_ble_backend", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T18:55:47.310Z", "summary": "Flutter reactive BLE backend\n\nChangelog excerpt:\n- Upgrade dependencies"}, {"id": "urn:uuid:bb64dcb5-2a8c-41f1-8c1b-9ef959cf3f74", "title": "v1.3.1 of flutter_web_bluetooth_backend", "url": "https://pub.dev/packages/flutter_web_bluetooth_backend", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T18:51:46.966Z", "summary": "Flutter Web Bluetooth backend\n\nChangelog excerpt:\n- Upgrade dependencies"}, {"id": "urn:uuid:474e1a63-5aeb-493b-9d9e-2df9df291f3b", "title": "v1.0.3 of flutter_bunny_video_player", "url": "https://pub.dev/packages/flutter_bunny_video_player", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T18:50:45.523Z", "summary": "Flutter Bunny Video Player\n\nChangelog excerpt:\n- Added support for token-based authentication\n- Added `token`and `expire`parameters to BunnyPlayerView widget\n- Enhanced security for secured video access\n- Updated documentation with token authentication examples"}, {"id": "urn:uuid:1f436de3-a759-46df-8952-9e25811a4487", "title": "v0.5.1 of universal_ble_backend", "url": "https://pub.dev/packages/universal_ble_backend", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T18:48:00.113Z", "summary": "Universal BLE backend\n\nChangelog excerpt:\n- Upgrade dependencies"}, {"id": "urn:uuid:d07c7779-e3f0-4ecd-a008-ee43f18ec04b", "title": "v0.4.1 of win_ble_backend", "url": "https://pub.dev/packages/win_ble_backend", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T18:41:25.280Z", "summary": "Win BLE backend\n\nChangelog excerpt:\n- Upgrade dependencies"}, {"id": "urn:uuid:ca3d7bad-e672-4c3a-b44a-86a61d7e7152", "title": "v0.1.0 of gemini_dart", "url": "https://pub.dev/packages/gemini_dart", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T18:33:29.041Z", "summary": "A comprehensive Dart package for integrating Google's Gemini AI models with support for text, image, and video processing.\n\nChangelog excerpt:\n### Added\n\n- Initial release of Gemini Dart package\n- Multi-modal AI integration support\n- Text, image, and video content processing\n- Flutter widgets for easy integration\n- Streaming response capabilities\n- Comprehensive error handling\n- Type-safe models and responses\n- Cross-platform support (iOS, Android, Web, Desktop)\n- Performance optimizations and caching\n- Extensive documentation and examples\n\n### Features\n\n- `GeminiClient`- Main client for API interactions\n- `TextContent`, `ImageContent`[...]"}, {"id": "urn:uuid:fe027ec6-b9be-45f1-801d-14a08d98cea3", "title": "v1.3.1 of ble_backend", "url": "https://pub.dev/packages/ble_backend", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-28T18:33:19.423Z", "summary": "BLE backend interfaces\n\nChangelog excerpt:\n- Upgrade dependencies"}, {"id": "tag:github.com,2008:Repository/31792824/3.35.5", "title": "Changelog entry for 3.35.5 (#176103)", "url": "https://github.com/flutter/flutter/releases/tag/3.35.5", "source": "flutter_releases", "section": "Releases", "publishedAt": "2025-09-26T19:05:09.000Z", "summary": "Changelog update for stable hotfix. These are the cherry picks:\n#176030\n#176048"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-264.0.dev", "title": "3.10.0-264.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-264.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-10-01T00:02:35.000Z", "summary": "3.10.0-264.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-263.0.dev", "title": "3.10.0-263.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-263.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-30T20:03:21.000Z", "summary": "3.10.0-263.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-262.0.dev", "title": "3.10.0-262.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-262.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-30T16:09:43.000Z", "summary": "3.10.0-262.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-261.0.dev", "title": "3.10.0-261.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-261.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-30T12:09:04.000Z", "summary": "3.10.0-261.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-260.0.dev", "title": "3.10.0-260.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-260.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-30T08:03:09.000Z", "summary": "3.10.0-260.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-259.0.dev", "title": "3.10.0-259.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-259.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-30T04:03:24.000Z", "summary": "3.10.0-259.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-258.0.dev", "title": "3.10.0-258.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-258.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-30T00:03:17.000Z", "summary": "3.10.0-258.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.9.4", "title": "3.9.4", "url": "https://github.com/dart-lang/sdk/releases/tag/3.9.4", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-30T19:57:56.000Z", "summary": "3.9.4"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-257.0.dev", "title": "3.10.0-257.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-257.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-29T20:03:31.000Z", "summary": "3.10.0-257.0.dev"}, {"id": "tag:github.com,2008:Repository/35726310/3.10.0-256.0.dev", "title": "3.10.0-256.0.dev", "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-256.0.dev", "source": "dart_releases", "section": "Releases", "publishedAt": "2025-09-29T16:09:26.000Z", "summary": "3.10.0-256.0.dev"}, {"id": "urn:uuid:c31428a3-f85d-4b4c-88c5-62d64497e393", "title": "v1.5.4 of convenient_test", "url": "https://pub.dev/packages/convenient_test", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T03:35:07.524Z", "summary": "Write and debug tests easily, with full action history, time travel, screenshots, rapid re-execution, video records, interactivity, isolation and more\n\nChangelog excerpt:\n- Update package APIs #426 (thanks @ahmdt)"}, {"id": "urn:uuid:ac11d71f-9d28-4cd9-a65f-91b640bbb05d", "title": "v1.5.4 of convenient_test_common", "url": "https://pub.dev/packages/convenient_test_common", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T03:34:58.344Z", "summary": "Write and debug tests easily, with full action history, time travel, screenshots, rapid re-execution, video records, interactivity, isolation and more\n\nChangelog excerpt:\n- Update package APIs #426 (thanks @ahmdt)"}, {"id": "urn:uuid:36f9ccdf-5ab6-4137-8e43-be200cffdf54", "title": "v1.5.4 of convenient_test_common_dart", "url": "https://pub.dev/packages/convenient_test_common_dart", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T03:34:48.268Z", "summary": "Write and debug tests easily, with full action history, time travel, screenshots, rapid re-execution, video records, interactivity, isolation and more\n\nChangelog excerpt:\n- Update package APIs #426 (thanks @ahmdt)"}, {"id": "urn:uuid:a45a8721-8f3a-4d5a-a575-c7244adb7414", "title": "v1.3.2 of flutter_linkid_mmp", "url": "https://pub.dev/packages/flutter_linkid_mmp", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T03:26:09.838Z", "summary": "Airflex.io is a Mobile Marketing Platform that offers tools for analyzing and optimizing user experiences on mobile and web platforms. It features real-time data analytics, audience segmentation, frau [...]"}, {"id": "urn:uuid:5d128441-ea90-4118-9bef-9236639469d0", "title": "v0.2.0 of snore_detection", "url": "https://pub.dev/packages/snore_detection", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T03:25:21.175Z", "summary": "Real-time and file-based snoring detection using TensorFlow Lite. Simple API for audio classification with configurable thresholds.\n\nChangelog excerpt:\n- Update package dependencies to latest compatible releases (path_provider ^2.1.5, permission_handler ^12.0.1, fftea ^1.5.0)\n- Adopt flutter_lints 6.0.0 for updated lint rules"}, {"id": "urn:uuid:2bf4236f-2b00-485e-a932-5adc1e66c19d", "title": "v1.3.1 of flutter_linkid_mmp", "url": "https://pub.dev/packages/flutter_linkid_mmp", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T03:13:18.713Z", "summary": "Airflex.io is a Mobile Marketing Platform that offers tools for analyzing and optimizing user experiences on mobile and web platforms. It features real-time data analytics, audience segmentation, frau [...]"}, {"id": "urn:uuid:23d2be60-3b07-408a-a2e5-140b0835223e", "title": "v0.1.0 of nebux_design_system", "url": "https://pub.dev/packages/nebux_design_system", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T03:03:52.745Z", "summary": "A comprehensive Flutter design system package that provides consistent theming, typography, spacing, and UI components for Nebux applications.\n\nChangelog excerpt:\n- **NEW FEATURE**: Enhanced Country model with `minLength`and `maxLength`properties for improved phone number validation   - Added `minLength`property to Country model for minimum phone number length validation\n  - Added `maxLength`property to Country model for maximum phone number length validation\n  - Updated country JSON data to include length validation information\n  - Improved country picker functionality with better phone number validation support\n  - Enhanced user experience with more acc[...]"}, {"id": "urn:uuid:*************-4d56-896c-0b23f75cb592", "title": "v0.0.1 of flutter_fluid_markdown", "url": "https://pub.dev/packages/flutter_fluid_markdown", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T03:03:42.935Z", "summary": "A Flutter plugin to render Markdown with a fluid and beautiful user interface. This is a placeholder package.\n\nChangelog excerpt:\n- Initial release. This is a placeholder package."}, {"id": "urn:uuid:4af1e4d0-ef1c-48f5-8249-917ad0a24385", "title": "v3.4.50 of layrz_models", "url": "https://pub.dev/packages/layrz_models", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T02:58:41.111Z", "summary": "Layrz API models for Dart/Flutter. This package contains the models used by the Layrz API.\n\nChangelog excerpt:\n- Added list `analyticsGridStructure`in `AtsMonitorInput`"}, {"id": "urn:uuid:f88c9132-4478-4ceb-9a25-0b93799f36ad", "title": "v1.2.0 of flutter_axios", "url": "https://pub.dev/packages/flutter_axios", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T02:58:21.050Z", "summary": "A promise-based HTTP client for Flutter inspired by Axios. Provides interceptors, request/response transformation, error handling, and automatic JSON conversion.\n\nChangelog excerpt:\n### Added\n\n- **🌊 流式功能支持**- 全新的流式 HTTP 处理能力   - `getStream()`- 流式响应，逐行处理大数据\n  - `postStream()`- 流式 POST 请求\n  - `downloadStream()`- 带进度跟踪的流式下载\n  - `connectSSE()`- Server-Sent Events 实时事件流\n  - `connectWebSocket()`- WebSocket 双向实时通信\n \n\n### Enhanced\n\n- **📥 渐进式下载**- 大文件下载进度跟踪   - 实时下载速度计算\n  - 剩余时间估算\n  - 可配置的缓冲区大小\n  - 自动重连机制\n \n\n### New Types\n\n- **流式响应类型**   - `StreamedAxiosResponse`- 流式响应包装器\n  - `SSEEvent`- Server-Sent Events 事件\n  - `WebSocketMessage`- WebSocket 消息\n  - `DownloadProgress`- 下载进度信息\n [...]"}, {"id": "urn:uuid:7df72cda-c304-4301-a077-e3bb69852b4b", "title": "v1.1.7 of dead_code_analyzer", "url": "https://pub.dev/packages/dead_code_analyzer", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T02:54:41.218Z", "summary": "A tool to analyze and identify dead/unused code in Dart and Flutter projects.\n\nChangelog excerpt:\n- Bump version to 1.1.6"}, {"id": "urn:uuid:4112e5cb-9623-4250-85e3-a961f26bb0c8", "title": "v1.3.0 of flutter_linkid_mmp", "url": "https://pub.dev/packages/flutter_linkid_mmp", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T02:39:28.449Z", "summary": "Airflex.io is a Mobile Marketing Platform that offers tools for analyzing and optimizing user experiences on mobile and web platforms. It features real-time data analytics, audience segmentation, frau [...]"}, {"id": "urn:uuid:3af6d5f0-0e7d-4595-bca1-5b5e8a99a24d", "title": "v0.1.2 of pure_ui", "url": "https://pub.dev/packages/pure_ui", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T02:19:07.475Z", "summary": "'A pure Dart implementation of Canvas API compatible with dart:ui.'\n\nChangelog excerpt:\n- Add exportImage function."}, {"id": "urn:uuid:3a6827c9-dc3a-47ab-8b43-4368d791da58", "title": "v0.3.2 of unorm_dart", "url": "https://pub.dev/packages/unorm_dart", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T02:14:30.542Z", "summary": "Unicode 17.0 Normalization - NFC, NFD, NFKC, NFKD. This is a Dart port of [walling/unorm](https://github.com/walling/unorm).\n\nChangelog excerpt:\n- Update Unicode data to version 17 ([#41](https://github.com/yshrsmz/unorm-dart/pull/41))\n- Update dependency dart to v3.9.4 ([#40](https://github.com/yshrsmz/unorm-dart/pull/40))"}, {"id": "urn:uuid:4e08ca1f-652d-4fdb-b2e5-ea3ebdb72d71", "title": "v1.0.4 of five_pointed_star", "url": "https://pub.dev/packages/five_pointed_star", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T02:13:49.625Z", "summary": "Simple five pointed star control.\n\nChangelog excerpt:\n- 完善example示例应用，添加多种配置演示\n- 更新README.md文档，添加详细的API文档和使用说明\n- 修复代码质量问题，所有字段添加final修饰符\n- 优化构造函数，添加const关键字提升性能\n- 修复测试用例，确保所有测试通过\n- 代码分析通过，无任何警告或错误"}, {"id": "urn:uuid:320f18a2-b8c6-4b51-828d-1ccf84b26e82", "title": "v0.4.4 of tailwindcss_build", "url": "https://pub.dev/packages/tailwindcss_build", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T02:13:36.888Z", "summary": "A comprehensive Flutter package that brings the power and convenience of Tailwind CSS utility classes to Flutter development."}, {"id": "urn:uuid:bdafa4ac-fa37-43e6-92b3-c847f9346984", "title": "v0.0.7-fix of app_security_lock", "url": "https://pub.dev/packages/app_security_lock", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T02:06:12.356Z", "summary": "A comprehensive Flutter plugin for app security features including screen lock detection, background timeout, and lifecycle monitoring.\n\nChangelog excerpt:\n添加安卓缺失的括号"}, {"id": "urn:uuid:079e7fb8-9616-445d-a2eb-060628d493e7", "title": "v3.0.0 of tbdc_flutter_plugin", "url": "https://pub.dev/packages/tbdc_flutter_plugin", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T02:01:10.032Z", "summary": "Plugin of TBDC Company with helper classes and UI Components for Flutter\n\nChangelog excerpt:\n- Rebuilt `TBDCPhoneInput`as a controlled widget driven by `value`+ `onChanged`.\n- Added the `TBDCPhoneInputValue`payload exposing country, dial code, formatted text, and validation state.\n- Introduced `initialCountry`, `enabled`, and `focusNode`configuration options.\n- Removed the legacy `initialValue`, `controller`, and `onValueChanged`parameters.\n- Improved validation, masking, and country synchronization across locales."}, {"id": "urn:uuid:151e1d57-cbd0-444d-b46d-d03045f181f4", "title": "v1.1.0 of dart_untis_mobile", "url": "https://pub.dev/packages/dart_untis_mobile", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T00:59:40.957Z", "summary": "A pure dart library implementing the Untis Mobile API (https://www.untis.at/produkte/webuntis/untis-mobile-app).\n\nChangelog excerpt:\n- Added working GitHub publish workflow\n- Added http override to example project\n- Fixed #1 by adding an \"EXAM\" PeriodState and a default case\n- The \"exam\" field is now accessible via UntisExam\n- getExam() now delivers much more information as UntisExam now contains much more fields (when getExam() is used)\n- Fixed bug that caused every request for times in october on the 10th day of month to fail\n- You can now actually use an arbitrary time range (the start date just needs to be before the end [...]"}, {"id": "urn:uuid:9647ecc9-ecec-44c0-ba79-43bd2c090bbe", "title": "v0.3.6 of creta_news", "url": "https://pub.dev/packages/creta_news", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T00:53:39.668Z", "summary": "Beautiful Headline News widget using Google News RSS for Flutter (Web, Windows, Android).\n\nChangelog excerpt:\n- Change: Enforce minimum `requestTimeout`of 3 seconds   - Requests with timeout < 3s are clamped to 3s internally"}, {"id": "urn:uuid:d30a822c-83ac-45a3-a19d-92170ae30334", "title": "v0.1.5 of one_uikit", "url": "https://pub.dev/packages/one_uikit", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T00:45:49.332Z", "summary": "A comprehensive Flutter UI Kit with reusable components, icons, and utilities for 1APP project"}, {"id": "urn:uuid:3427d58f-cb50-4f63-b749-70a2d2bfbda1", "title": "v0.6.0 of mlkit_scanner", "url": "https://pub.dev/packages/mlkit_scanner", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T00:30:14.656Z", "summary": "A Flutter plugin to detect barcodes, text, faces, and objects using Google MLKit API for iOS and Android\n\nChangelog excerpt:\n[fix]\n\n- Update Google ML Kit dependencies to support 16KB page alignment"}, {"id": "urn:uuid:a39f08bd-04ff-4bea-99ab-16e87f4d8445", "title": "v0.3.5 of creta_news", "url": "https://pub.dev/packages/creta_news", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T00:23:28.217Z", "summary": "Beautiful Headline News widget using Google News RSS for Flutter (Web, Windows, Android).\n\nChangelog excerpt:\n- Feature: Add `outlineColor`and `outlineWidth`to `CretaHeadlineNews`   - Applies outline effect to headline title when provided"}, {"id": "urn:uuid:6a37309d-4df9-4445-8806-991797bb4944", "title": "v9.0.15 of phone_numbers_parser", "url": "https://pub.dev/packages/phone_numbers_parser", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T00:23:02.893Z", "summary": "Dart library for parsing phone numbers. Inspired by Google's libphonenumber and PhoneNumberKit for ios.\n\nChangelog excerpt:\n- Upgrade metadata"}, {"id": "urn:uuid:d2af7eb0-41dd-46c8-95ed-50c78b758b7b", "title": "v0.5.1 of physical", "url": "https://pub.dev/packages/physical", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T00:15:51.280Z", "summary": "A lightweight Dart library for dealing with physical quantities, units, and conversions.\n\nChangelog excerpt:\n- Fixed prefixed unit scaling [2c393b1](https://github.com/nebkat/dart-physical/commit/2c393b1a9836ca5ea5aae435c866167feceda6b4)\n- Reorganized examples"}, {"id": "urn:uuid:203c094b-06b6-42fc-b2f1-f0acebc69383", "title": "v1.0.5 of flutter_unify", "url": "https://pub.dev/packages/flutter_unify", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-10-01T00:00:04.009Z", "summary": "Unified API for cross-platform Flutter development. One API for all platforms with reactive streams and pluggable adapters.\n\nChangelog excerpt:\n### Maintenance\n\n- Bumped version to 1.0.5 for analyzer cleanup (0 issues) and import path compliance.\n- Removed test package dependency usage (placeholder test file retained) to keep analysis clean for pub points.\n- Replaced relative internal imports with package imports in library files (media_core, analytics adapters) to satisfy lint.\n\n### Next\n\n- Reintroduce proper tests using minimal dependency set.\n- Add validated screenshots with correct dimensions and size."}, {"id": "urn:uuid:40ab9bd8-f73e-4c95-a756-128e62e43be7", "title": "v1.2.4 of bluesky", "url": "https://pub.dev/packages/bluesky", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T23:45:45.387Z", "summary": "The most famous and powerful Dart/Flutter library for Bluesky Social.\n\nChangelog excerpt:\n- **FIX**: Fixed type compatibility issues with AtUri parameters   - Corrected service method calls to properly handle AtUri objects instead of strings\n  - Fixed moderation test data to use string URIs instead of AtUri objects where appropriate\n  - Improved type safety in API parameter handling and mock data generation"}, {"id": "urn:uuid:d9aa8f15-ebd0-4c35-b6cb-19a2e2b46573", "title": "v1.2.2 of atproto", "url": "https://pub.dev/packages/atproto", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T23:45:37.446Z", "summary": "The most famous and powerful Dart/Flutter library for AT Protocol.\n\nChangelog excerpt:\n- **FIX**: Fixed type compatibility issues with AtUri parameters   - Corrected service method calls to properly handle AtUri objects instead of strings\n  - Improved type safety in API parameter handling"}, {"id": "urn:uuid:5350bd14-5936-4d28-98bb-b3c5803ce6c2", "title": "v0.5.0 of physical", "url": "https://pub.dev/packages/physical", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T23:41:38.138Z", "summary": "A lightweight Dart library for dealing with physical quantities, units, and conversions.\n\nChangelog excerpt:\n- Various improvements."}, {"id": "urn:uuid:fb23a6b5-c922-4393-8b47-05b97924b032", "title": "v1.5.0 of ai_providers", "url": "https://pub.dev/packages/ai_providers", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T23:27:25.140Z", "summary": "Unified AI provider integration for Flutter - OpenAI, Google AI, xAI, Android native TTS with consistent API.\n\nChangelog excerpt:\n### 🔄 Breaking Changes - Sistema Seed Eliminado\n\n- **AiImageParams.seed → sourceImageBase64**: Campo `seed`completamente removido y reemplazado por `sourceImageBase64`para edición de imágenes\n- **ProviderResponse.seed eliminado**: Removido campo `seed`de respuestas de providers\n- **AiImage.seed eliminado**: Modelo AiImage ya no incluye campo `seed`\n- **AIProviderManager sin seed**: Eliminadas todas las referencias y validaciones de seed\n\n### ✨ Nuevas Características\n\n- **🖼️ Sistema sourceImage[...]"}, {"id": "urn:uuid:5ca91126-6901-41b0-80ea-085a43e4cbe4", "title": "v0.21.1 of provide_it", "url": "https://pub.dev/packages/provide_it", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T23:11:27.612Z", "summary": "Provider-like state binding, management, and injection using only context extensions.\n\nChangelog excerpt:\n- Fix missing `dependOnBind`on `Bind.activate`."}, {"id": "urn:uuid:9df20488-529a-4e5e-b5b0-1ed90e97a2ea", "title": "v1.0.2 of bromelia_cli", "url": "https://pub.dev/packages/bromelia_cli", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T23:06:54.763Z", "summary": "Bromelia CLI is a command-line tool for generating Flutter projects.\n\nChangelog excerpt:\n- Fix template processing."}, {"id": "urn:uuid:37de1d87-a6d6-4863-81eb-bd3fd8149c9b", "title": "v0.2.0 of resources_used_lints", "url": "https://pub.dev/packages/resources_used_lints", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T23:05:46.558Z", "summary": "Lint rules for documentation of resources used in CSE 340.\n\nChangelog excerpt:\n- Updated dependency versions."}, {"id": "urn:uuid:c432060b-b04e-4a90-8032-04086459df61", "title": "v2.0.0-rc.3 of serinus", "url": "https://pub.dev/packages/serinus", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T22:58:08.901Z", "summary": "Serinus is a framework written in Dart for building efficient and scalable server-side applications.\n\nChangelog excerpt:\n- feat: improve body parsing to allow for more flexibility in the implementation of custom body types.\n- feat: add utility methods to the RequestContext to simplify the extraction of typed parameters from the request.\n- fix: fix check on WebSocketGateway to prevent wrongful exceptions on sending data."}, {"id": "urn:uuid:6cc17a2d-32f0-4ac1-a487-be8b87558354", "title": "v0.2.0 of assetxf", "url": "https://pub.dev/packages/assetxf", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T22:55:21.898Z", "summary": "provides annotations and references for assetx"}, {"id": "urn:uuid:e89d8461-ae2f-44ae-a47d-7ceee667de87", "title": "v0.2.0 of assetx", "url": "https://pub.dev/packages/assetx", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T22:54:26.191Z", "summary": "A Flutter asset management tool that generates Dart code for easy asset access."}, {"id": "urn:uuid:f2423d4a-0616-47b0-8a88-3667d0770fe0", "title": "v0.0.3 of flutter_midi_16kb", "url": "https://pub.dev/packages/flutter_midi_16kb", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T22:43:26.129Z", "summary": "A Flutter plugin providing low-latency MIDI soundfont (SF2) playback  using TinySoundFont and Oboe with 16KB page-size alignment for Android.\n\n\nChangelog excerpt:\n- Corrected README entries"}, {"id": "urn:uuid:9f79fae5-10d4-4d39-bf3a-ac6fc2e5499c", "title": "v2.0.2 of tbdc_flutter_plugin", "url": "https://pub.dev/packages/tbdc_flutter_plugin", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T22:42:17.767Z", "summary": "Plugin of TBDC Company with helper classes and UI Components for Flutter\n\nChangelog excerpt:\n- Fix late error"}, {"id": "urn:uuid:b7e3008b-b1f9-4d8b-a1e2-dcd912cab656", "title": "v0.23.0 of maplibre_gl", "url": "https://pub.dev/packages/maplibre_gl", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T22:37:17.174Z", "summary": "A Flutter plugin for integrating MapLibre Maps inside a Flutter application on Android, iOS and web platforms.\n\nChangelog excerpt:\n> **Note**: This release has breaking changes.\n\n\nThis release aligns the plugin with the latest MapLibre Native (Android 11.9.0 / iOS 6.14.0), introduces runtime style switching APIs, hover interaction callbacks, and several annotation interaction improvements. It also contains a small breaking change for feature interaction callbacks.\n\nA big thank you to everyone who contributed to this update!\n\n### Breaking Changes\n\n- `onFeatureDrag`/ `onFeatureTapped`callback signatures now provide an `Annota[...]"}, {"id": "urn:uuid:aa5f6397-597e-4c6e-9e02-ede35581d8a8", "title": "v0.23.0 of maplibre_gl_web", "url": "https://pub.dev/packages/maplibre_gl_web", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T22:36:59.077Z", "summary": "Web platform implementation of maplibre_gl. This package is only intended to be used by the maplibre_gl package.\n\nChangelog excerpt:\n> Note: This release has breaking changes.\n\n\nsee top-level CHANGELOG.md\n\n## newer releases\n\nsee top-level CHANGELOG.md\n\n## 0.15.1, May 24, 2022\n\nsee top-level CHANGELOG.md\n\n## 0.15.0, Oct 26, 2021\n\nsee top-level CHANGELOG.md\n\n## 0.14.0, Oct 14, 2021\n\n## 0.13.0, Oct 6, 2021\n\n🎉 The first release of flutter-maplibre-gl with the complete transition to MapLibre libraries. 🎉\n\n### Changes cherry-picked/ported from tobrun/flutter-mapbox-gl:0.12.0\n\n- Dependencies: updated image package [#598](https://g[...]"}, {"id": "urn:uuid:ddc73e15-020b-46d8-8f47-8c1cab5f3743", "title": "v0.23.0 of maplibre_gl_platform_interface", "url": "https://pub.dev/packages/maplibre_gl_platform_interface", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T22:36:42.490Z", "summary": "A common platform interface for the maplibre_gl plugin. This package is only intended to be used by the maplibre_gl package.\n\nChangelog excerpt:\n> Note: This release has breaking changes.\n\n\nsee top-level CHANGELOG.md\n\n## newer releases\n\nsee top-level CHANGELOG.md\n\n## 0.15.1, May 24, 2022\n\nsee top-level CHANGELOG.md\n\n## 0.15.0, Oct 26, 2021\n\nsee top-level CHANGELOG.md\n\n## 0.14.0, Oct 14, 2021\n\n### Breaking changes:\n\n- Replace example styles [#25](https://github.com/m0nac0/flutter-maplibre-gl/pull/25)(also see [#21](https://github.com/m0nac0/flutter-maplibre-gl/issues/21))   - The built-in constants for specific styles were removed. You ca[...]"}, {"id": "urn:uuid:92d2315d-8e56-41b3-866a-2c1783571b16", "title": "v1.0.0 of proportional_design", "url": "https://pub.dev/packages/proportional_design", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T22:16:00.688Z", "summary": "A Flutter package for responsive design with proportional scaling, Material Design 3 breakpoints, and adaptive strategies for phones, tablets, and desktops.\n\nChangelog excerpt:\n### 🎉 Major Update - Versão Melhorada\n\n**Recursos:**\n\n✅ **Detecção Inteligente de Dispositivos**\n\n- Breakpoints Material Design 3 (compact, medium, expanded)\n- Detecção correta de tablets usando `shortestSide`e `longestSide`\n- Enum `DeviceType`(phone, tablet, desktop, foldable)\n- Extension `DeviceDetectorExtension`para fácil acesso\n\n✅ **Sistema de Cache de Performance**\n\n- Cache automático de cálculos proporcionais\n- Redução de ~70% em cálculos repetitivos\n- Hit rate esperado > 80%\n- Controle d[...]"}, {"id": "urn:uuid:38405873-2d9b-401d-b8fd-cf150275fb26", "title": "v0.10.1 of openai_core", "url": "https://pub.dev/packages/openai_core", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T22:15:48.489Z", "summary": "openai support for dart including the responses api, the realtime api, and more.\n\nChangelog excerpt:\nFixed file search completion event fromJson"}, {"id": "urn:uuid:3a44070b-2845-40e6-8745-911d0e635195", "title": "v1.0.1 of bromelia_cli", "url": "https://pub.dev/packages/bromelia_cli", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T22:14:08.425Z", "summary": "Bromelia CLI is a command-line tool for generating Flutter projects.\n\nChangelog excerpt:\n- Update ReadMe."}, {"id": "urn:uuid:fdc010dd-3aed-4c56-b7b8-76b24e64b241", "title": "v1.0.0 of bromelia_cli", "url": "https://pub.dev/packages/bromelia_cli", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T22:09:02.163Z", "summary": "Bromelia CLI is a command-line tool for generating Flutter projects.\n\nChangelog excerpt:\n- Initial version."}, {"id": "urn:uuid:cf56feef-6c5e-4a46-ae7d-f3a8528d314d", "title": "v0.6.0 of trailbase", "url": "https://pub.dev/packages/trailbase", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T22:08:29.289Z", "summary": "Official client library for TrailBase."}, {"id": "urn:uuid:3239b652-daf8-480a-9c95-7a6e8068ce01", "title": "v0.4.1 of torch_control", "url": "https://pub.dev/packages/torch_control", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T22:05:00.101Z", "summary": "A flutter plugin for controlling torch/flashlight/lamp with simple and clean API supporting Android and iOS.\n\nChangelog excerpt:\n- Fix: Dart version upper bound"}, {"id": "urn:uuid:6a980a71-52a0-46f8-ae85-942148bf9658", "title": "v1.0.2 of flutter_easy_swagger_generator", "url": "https://pub.dev/packages/flutter_easy_swagger_generator", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T22:03:40.998Z", "summary": "A Flutter package for generating Dart models and API clients from Swagger/OpenAPI specifications.\n\nChangelog excerpt:\n-  **State Management Selection Enhanced**Users can now choose the state management type during code generation:\n\n\n-  **BLoC**\n\n\n-  **Provider**\n\n\n-  **Riverpod**\n\n\n-  **swaggerGenerator and InjectionGenerator updated to handle multiple state management types.**\n\n\n-  **Dependency injection files now conditionally import and register the selected state management modules for each feature.**\n\n\n-  **Generated code is fully compatible with any combination of state management patterns.**\n\n\n-  **Defau[...]"}, {"id": "urn:uuid:086e83b0-50ef-4361-bd6f-0553e8f2a818", "title": "v0.0.1 of qrcode_telpo_ln_reader", "url": "https://pub.dev/packages/qrcode_telpo_ln_reader", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T21:59:36.310Z", "summary": "Plugin Flutter pour scanner des QR codes avec le lecteur Telpo LN (T10/TPS550)\n\nChangelog excerpt:\n- TODO: Describe initial release."}, {"id": "urn:uuid:77e397a3-d2bb-448d-8e37-44f882b372ce", "title": "v0.0.5 of api_gen", "url": "https://pub.dev/packages/api_gen", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T21:56:06.924Z", "summary": "A simple Dart code generator that creates model classes (with fromJson/toJson) from a JSON schema, saving time on boilerplate in Flutter/Dart projects.\n\nChangelog excerpt:\n- added `generate`command to the package's CLI.\n- improved [README](https://github.com/mrrda1969/api_gen?tab=readme-ov-file#)file."}, {"id": "urn:uuid:23da795d-5a8c-49d1-a176-9c7243096048", "title": "v1.0.16 of synq_manager", "url": "https://pub.dev/packages/synq_manager", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T21:47:34.931Z", "summary": "A powerful synchronization manager for Flutter apps with secure local storage, real-time state management, and background cloud sync capabilities.\n\nChangelog excerpt:\n### Fixed\n\n🎯 **onInit Callback Issue**: Fixed critical timing issue where onInit callbacks were not being triggered\n\n- Implemented `_waitUntilReady()`helper method in `SynqListeners`to ensure manager is fully initialized before setting up listeners\n- Fixed race condition where `connected`event was being emitted before `onInit`listeners were registered\n- Enhanced all listener methods (onEvent, onCreate, onUpdate, onDelete, onError) to wait for manager readiness\n- Improved `onInit`reliability by [...]"}, {"id": "urn:uuid:7954d9f6-b95a-45b7-9cc6-62eb66a8275d", "title": "v0.9.0 of gcloud", "url": "https://pub.dev/packages/gcloud", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T21:36:37.857Z", "summary": "High level idiomatic Dart API for Google Cloud Storage, Pub-Sub and Datastore.\n\nChangelog excerpt:\n- Support `orderingKey`on pub/sub's `Message`type.\n- Support the latest version `^15.0.0`of the `googleapis`package."}, {"id": "urn:uuid:6a8b8ee5-f5a8-442e-aa6f-a355a7f5491d", "title": "v1.0.15 of synq_manager", "url": "https://pub.dev/packages/synq_manager", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T21:26:22.521Z", "summary": "A powerful synchronization manager for Flutter apps with secure local storage, real-time state management, and background cloud sync capabilities.\n\nChangelog excerpt:\n### Fixed\n\n🔧 **User Account Migration Loop**: Fixed critical bug causing infinite sync loops during offline data upload\n\n- Fixed missing user ID persistence in `_uploadLocalData()`function that caused endless account migration cycles\n- Added `cloudUserId`parameter to `_uploadLocalData()`method to ensure user ID is properly stored after successful uploads\n- Enhanced account conflict resolution to persist user ID in all scenarios (`keepLocalData`action)\n- Improved account scenario detection to pr[...]"}, {"id": "urn:uuid:d1cb7864-1944-4926-ba36-13708ceed8ff", "title": "v1.1.0 of local_auth_platform_interface", "url": "https://pub.dev/packages/local_auth_platform_interface", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T20:46:03.887Z", "summary": "A common platform interface for the local_auth plugin.\n\nChangelog excerpt:\n- Adds `LocalAuthException`to allow for consistent, structured exceptions across platform implementations.\n- Updates minimum supported SDK version to Flutter 3.29/Dart 3.7."}, {"id": "urn:uuid:075a0c94-aba7-4a3b-82a7-17c62dc7cf33", "title": "v1.0.14 of synq_manager", "url": "https://pub.dev/packages/synq_manager", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T20:41:28.744Z", "summary": "A powerful synchronization manager for Flutter apps with secure local storage, real-time state management, and background cloud sync capabilities.\n\nChangelog excerpt:\n### Fixed\n\n🔄 **Infinite Push Loop**: Fixed critical bug where successful push operations caused infinite sync loops\n\n- Added timestamp buffer (1 second) in `_scanForUntrackedChanges()`to prevent recently synced items from being detected as new changes\n- Enhanced `_persistSyncTimestamp()`to clear pending changes after successful timestamp persistence\n- Improved `_pushToCloud()`to immediately remove pushed keys from pending changes upon successful completion\n- Modified sync strategy to exclude su[...]"}, {"id": "urn:uuid:44e8a251-f12b-4e5d-87c7-ea1f37708238", "title": "v1.0.10 of limit", "url": "https://pub.dev/packages/limit", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T20:36:57.244Z", "summary": "Persistent across sessions, no manual storage needed. Easily manage cooldowns and rate limits with one-line.\n\nChangelog excerpt:\n- Fixed lint issues in the `RateLimiter`constructor\n- Updated README"}, {"id": "urn:uuid:ed0fed67-f658-4427-8154-bbb69c0f55e4", "title": "v1.1.2 of flutter_anycam", "url": "https://pub.dev/packages/flutter_anycam", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T20:28:41.890Z", "summary": "Plugin Flutter para captura e análise de frames de múltiplos tipos de câmera (frontal, traseira, USB e RTSP)\n\nChangelog excerpt:\nTextos e traduções"}, {"id": "urn:uuid:fe24a7dc-b812-4eee-8da9-edc1ad6cf9a8", "title": "v0.0.1 of amplitude_engagement_flutter", "url": "https://pub.dev/packages/amplitude_engagement_flutter", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T20:18:04.831Z", "summary": "Official Amplitude Engagement Flutter SDK, supporting Android, iOS\n\nChangelog excerpt:\n- TODO: Describe initial release."}, {"id": "urn:uuid:9a1fb5bb-edda-44dd-9459-c7929757a396", "title": "v0.10.10+8 of camera_android", "url": "https://pub.dev/packages/camera_android", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T20:11:33.519Z", "summary": "Android implementation of the camera plugin.\n\nChangelog excerpt:\n- Restores compileSdk version to flutter.compileSdkVersion."}, {"id": "urn:uuid:95bd48f7-9159-42aa-9088-fccab0ff06a9", "title": "v0.1.0 of responsive_scaler", "url": "https://pub.dev/packages/responsive_scaler", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T20:06:36.770Z", "summary": "A Flutter package that offers automatic, boilerplate-free responsive scaling for text, icons, and spacing across different screen sizes.\n\nChangelog excerpt:\n- Make the scaling logic more robust\n- Remove AppTextStyles dependency, Redundant since the default TextTheme is already responsive\n- Update `scaled(baseSize)`to `scale(baseSize, minValue, maxValue)`for more developer control\n- Add `baseSize.scale(minValue, maxValue)`helper function for easier scaling of arbitrary values\n- Update the Readme documentation"}, {"id": "urn:uuid:31d89a47-1f26-49c7-8403-c2a2f26309ca", "title": "v1.0.13 of synq_manager", "url": "https://pub.dev/packages/synq_manager", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T19:51:13.358Z", "summary": "A powerful synchronization manager for Flutter apps with secure local storage, real-time state management, and background cloud sync capabilities.\n\nChangelog excerpt:\n### Changed\n\n🔄 **Code Structure Refactoring**: Improved code organization and API structure\n\n- Extracted cloud callback types to separate `CloudCallbacks`model file\n- Moved `SyncResult`and `SyncStats`classes to dedicated model files for better maintainability\n- Enhanced type safety with proper CloudFetchResponse structure including `cloudUserId`field\n- Updated example app to demonstrate new CloudFetchResponse format\n- Improved export structure in main library file for better API access\n\n### Add[...]"}, {"id": "urn:uuid:c814bedf-7bab-449c-91da-d37bb25977b4", "title": "v1.0.0 of conekta_component", "url": "https://pub.dev/packages/conekta_component", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T19:46:53.003Z", "summary": "A Flutter library for securely collecting and validating card payment information using Conekta.\n\nChangelog excerpt:\n- Final release"}, {"id": "urn:uuid:c8da9f37-5eea-46f4-8da7-8669d4946e4a", "title": "v1.0.1 of iterable_dropdown", "url": "https://pub.dev/packages/iterable_dropdown", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T19:35:13.967Z", "summary": "A Flutter dropdown that works on iterable instead of lists. It also has multi select functionality\n\nChangelog excerpt:\n- Updated the screen recording."}, {"id": "urn:uuid:96586ae9-5fb6-462e-b23e-afd657af2655", "title": "v1.57.3 of logrocket_flutter", "url": "https://pub.dev/packages/logrocket_flutter", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T19:34:21.633Z", "summary": "LogRocket SDK Plugin for Flutter. LogRocket sessions provide a comprehensive understanding of how users engage with your app.\n\nChangelog excerpt:\n- native platform updates"}, {"id": "urn:uuid:140cc212-93a8-4c53-a41f-35eeee4c82cf", "title": "v0.0.1 of googlelens", "url": "https://pub.dev/packages/googlelens", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T19:18:20.554Z", "summary": "A Flutter package to upload an image to Google Lens via WebView and scrape basic info.\n\nChangelog excerpt:\n- Initial release of `googlelens`.\n- Upload an image to Google Lens via WebView.\n- Injects image into a hidden form and posts to Google Lens.\n- Scrapes basic title/description from the Lens result page.\n- Includes a `LensUploadView`widget and a `MultiSearchWebView`for text queries."}, {"id": "urn:uuid:70d3c4ce-b315-4932-af4f-1f8f5c7e855d", "title": "v1.0.5 of flutter_multi_screenshot", "url": "https://pub.dev/packages/flutter_multi_screenshot", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T19:17:40.417Z", "summary": "A Flutter plugin for capturing screenshots on Linux.\n\nChangelog excerpt:\n- Lowered minimum Flutter SDK requirement from >=3.10.0 to >=3.7.0\n- Improved accessibility for projects using earlier Flutter versions\n- Maintains compatibility with modern Flutter features"}, {"id": "urn:uuid:d083b855-7e07-42a4-8940-d5aae8b4961d", "title": "v1.0.0 of iterable_dropdown", "url": "https://pub.dev/packages/iterable_dropdown", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T19:16:27.467Z", "summary": "A Flutter dropdown that works on iterable instead of lists. It also has multi select functionality\n\nChangelog excerpt:\n- Initial release."}, {"id": "urn:uuid:0ec61df8-edfc-4f07-a2c6-058a7fb912d5", "title": "v3.1.0 of timezone_to_country", "url": "https://pub.dev/packages/timezone_to_country", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T19:03:06.703Z", "summary": "Extension method for translation Time Zone Id to ISO 3166-1 alpha-2 code (e.g. 'Asia/Seoul' to 'KR')\n\nChangelog excerpt:\n- feat(timezone): set IANA time zone version to 2025b\n- chore(deps): bump flutter_timezone to 5.0.0"}, {"id": "urn:uuid:639cae14-2d97-4288-bd32-d7f7b2a94ad3", "title": "v0.0.2 of flutter_midi_16kb", "url": "https://pub.dev/packages/flutter_midi_16kb", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T18:57:06.456Z", "summary": "A Flutter plugin providing low-latency MIDI soundfont (SF2) playback  using TinySoundFont and Oboe with 16KB page-size alignment for Android.\n\n\nChangelog excerpt:\n- Transferred package to empyrealworks.com publisher.\n- Fixed: All native libraries are now properly 16 KB page-size aligned (NDK r27).\n- Improved stability when building release APKs/AABs."}, {"id": "urn:uuid:c635ebce-c9fb-4b6c-a608-27e07023cbb9", "title": "v0.1.18 of alghwalbi_core_app", "url": "https://pub.dev/packages/alghwalbi_core_app", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T18:51:22.720Z", "summary": "alghwalbi_core_app package is primarily intended for personal use, but feel free to use it in your projects."}, {"id": "urn:uuid:3123664e-5b2b-4b01-bed1-baf31a712467", "title": "v0.21.0 of horda_client", "url": "https://pub.dev/packages/horda_client", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T18:34:20.963Z", "summary": "Connect your Flutter app to backends built with the Horda platform.\n\nChangelog excerpt:\n- **BREAKING CHANGE**: entity queries now require an entity name\n- **FEAT**: entity name is now part of view key\n- **FEAT**: use horda_core 0.14.0"}, {"id": "urn:uuid:4eb50a8e-7649-45e3-97e3-5b6b233afccc", "title": "v1.0.4 of flutter_multi_screenshot", "url": "https://pub.dev/packages/flutter_multi_screenshot", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T18:34:19.135Z", "summary": "A Flutter plugin for capturing screenshots on Linux.\n\nChangelog excerpt:\n- Updated Flutter SDK constraints to support latest versions (>=3.10.0)\n- Updated flutter_lints to ^5.0.0 for better code quality\n- Fixed test compatibility issues\n- Improved dependency resolution for modern Flutter projects"}, {"id": "urn:uuid:59eaccde-56ed-4960-9807-864b212e810f", "title": "v12.0.1 of senior_platform_authentication_ui", "url": "https://pub.dev/packages/senior_platform_authentication_ui", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T18:31:09.480Z", "summary": "A package that make it easy to implement the Senior X authentication for Flutter. Built to be used with the bloc state management package.\n\nChangelog excerpt:\n[30/09/2025]\n\n### Alteração de dependências\n\n- [#ARQMOB-72] - Atualizado a versão da biblioteca senior_desing_system e token"}, {"id": "urn:uuid:697d9402-ea64-4e66-a638-111f56df4cf1", "title": "v1.1.6 of dead_code_analyzer", "url": "https://pub.dev/packages/dead_code_analyzer", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T18:28:10.885Z", "summary": "A tool to analyze and identify dead/unused code in Dart and Flutter projects.\n\nChangelog excerpt:\n- Bump version to 1.1.6\n- Set default output format to txt and update docs"}, {"id": "urn:uuid:ea63dd55-d29a-40e1-a133-1c80e2e4dcb9", "title": "v0.0.5 of draft_builder", "url": "https://pub.dev/packages/draft_builder", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T18:22:41.486Z", "summary": "The build runner for the draft package.\n\nChangelog excerpt:\n- Fix positional params code generation (thanks @tomwyr!)"}, {"id": "urn:uuid:dbd89e3c-9c2c-4e86-9a53-8fd989dd0c20", "title": "v1.0.0 of fase4_system_design", "url": "https://pub.dev/packages/fase4_system_design", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T18:21:10.758Z", "summary": "Un paquete integral de sistema de diseño Flutter basado en principios de Atomic Design.\n\nChangelog excerpt:\nVersión preparada para distribución en la tienda. Esta versión 1.0.0 incluye la API pública estabilizada del sistema de diseño y todos los componentes listos para su consumo:\n\n- Wrapper de tema: `Pragma4DesignSystem`.\n- Átomos: botones, texto, iconos, avatar, badge, divider, input, loader, chip, switch.\n- Moléculas: search field, card component (con soporte de imagen y placeholder), list item (con decoración por defecto y `showDecoration`), image with overlay, toggle group, stat card.\n- Organism[...]"}, {"id": "urn:uuid:e03ed7ba-50f6-4729-8236-c35429eaa3ba", "title": "v1.1.5 of dead_code_analyzer", "url": "https://pub.dev/packages/dead_code_analyzer", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T18:18:53.320Z", "summary": "A tool to analyze and identify dead/unused code in Dart and Flutter projects.\n\nChangelog excerpt:\n- Added parallel processing for faster analysis.\n- Improved import handling and refactored code structure.\n- Introduced PDF report output option with \"Open in VS Code\" functionality."}, {"id": "urn:uuid:3ab4d66f-ef82-4103-8701-6ed8d7b44b1d", "title": "v0.14.0 of horda_server", "url": "https://pub.dev/packages/horda_server", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T18:14:01.821Z", "summary": "Stateful serverless backend for Flutter apps\n\nChangelog excerpt:\n- **FEAT**: update horda_core to 0.14.0"}, {"id": "urn:uuid:68b40e07-5acd-4ead-b093-************", "title": "v1.1.1 of flutter_anycam", "url": "https://pub.dev/packages/flutter_anycam", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T18:13:49.385Z", "summary": "Plugin Flutter para captura e análise de frames de múltiplos tipos de câmera (frontal, traseira, USB e RTSP)\n\nChangelog excerpt:\nCorreção da proporção do preview no iOS"}, {"id": "urn:uuid:52d9f1a1-6845-41a6-a534-8a6e43e9a083", "title": "v0.1.2+3 of flutter_pactus_grpc", "url": "https://pub.dev/packages/flutter_pactus_grpc", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T18:10:07.186Z", "summary": "gRPC client for Pactus blockchain. Generated gRPC code for Pactus blockchain services.\n\nChangelog excerpt:\n### [Document]\n\n- Fix README and update documentation [#3](https://github.com/esmaeil-ahmadipour/flutter_pactus_grpc/pull/4)"}, {"id": "urn:uuid:7018dea2-b544-45cb-b7e1-d01a97879da9", "title": "v0.1.3 of step_bar", "url": "https://pub.dev/packages/step_bar", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T18:00:32.857Z", "summary": "A customizable, responsive step progress bar for multi-step flows in Flutter.\n\nChangelog excerpt:\n- **Layout improvements**: Complete restructure of StepBar widget layout   - Icons and labels now wrapped in individual columns for better organization\n  - Connector lines positioned underneath step columns using Stack layout\n  - Connectors now properly cross through the center of step circles\n  - Connectors are hidden behind opaque step circles for clean visual appearance\n  - Connector bounds adjusted to only appear between circles (not extending beyond first/last steps)\n \n- **Visual enhancemen[...]"}, {"id": "urn:uuid:13306148-db63-4e8b-858b-6929d1b25b59", "title": "v4.12.2 of ditto_live", "url": "https://pub.dev/packages/ditto_live", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T17:49:43.088Z", "summary": "The Ditto Flutter SDK is an edge sync platform allowing devices to synchronize data with or without an internet connection. For more info, go to https://ditto.com/link/docs\n\nChangelog excerpt:\n- Release Notes are located at [https://docs.ditto.live/sdk/latest/release-notes/flutter](https://docs.ditto.live/sdk/latest/release-notes/flutter)"}, {"id": "urn:uuid:5bd0ee63-eba9-430f-b739-8af422a9e329", "title": "v0.1.2 of pushfire_sdk", "url": "https://pub.dev/packages/pushfire_sdk", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T17:49:02.150Z", "summary": "A lightweight push notification tracking SDK for Firebase."}, {"id": "urn:uuid:36518d9b-79e7-43a9-8692-6c1393265eff", "title": "v0.1.1 of pushfire_sdk", "url": "https://pub.dev/packages/pushfire_sdk", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T17:47:28.103Z", "summary": "A lightweight push notification tracking SDK for Firebase.\n\nChangelog excerpt:\n### Added\n\n- **Authentication Provider Integration**: Automatic subscriber management through authentication providers   - `AuthProvider`enum with support for `firebase`, `supabase`, and `none`options\n  - `authProvider`parameter in `PushFireConfig`for configuring authentication integration\n  - Automatic subscriber login/logout based on authentication state changes\n \n\n### Enhanced\n\n-  **Firebase Authentication Integration**:\n\n   - Automatic listening for `authStateChanges()`from `FirebaseAuth.ins[...]"}, {"id": "urn:uuid:f9630723-ebb0-4a02-933f-8f31c39c68b5", "title": "v1.0.0+1 of flutter_pack", "url": "https://pub.dev/packages/flutter_pack", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T17:47:15.339Z", "summary": "A Flutter utility package that provides database helpers, model/repository generators, string/date extensions, and shared preferences management to speed up app development.\n\nChangelog excerpt:\n- Initial release (1.0.0+1)   - include database_manager_package that provides database manager functionality for ROS\n  - include database_manager_package_gui that provides GUI for database manager functionality for ROS\n  - include database_manager_package_msgs that provides messages for database manager functionality for ROS\n  - include database_manager_package_server that provides server for database manager functionality for ROS\n  - include database_manager_package_tools that provides tools f[...]"}, {"id": "urn:uuid:45c277c3-fcc8-4f67-8ca8-09cbb041a6f5", "title": "v5.0.0 of plotline_engage", "url": "https://pub.dev/packages/plotline_engage", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T17:35:17.279Z", "summary": "Flutter plugin for seamless integration of Plotline, a platform empowering growth and marketing teams to improve feature adoption and activation with in-app messages\n\nChangelog excerpt:\n- Updated Android SDK to 4.5.3"}, {"id": "urn:uuid:28b688a0-ce7e-4b33-b34c-f800a4db5292", "title": "v3.7.0 of apple_product_name", "url": "https://pub.dev/packages/apple_product_name", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T17:32:10.683Z", "summary": "Library for translating Apple machine identifiers into Apple product names (e.g. 'iPhone17,1' to 'iPhone 16 Pro')\n\nChangelog excerpt:\n### Features\n\n- mark productName and productNameOrNull as deprecated in IosProductName and MacOsProductName extensions ([607b632](https://github.com/kyle-seongwoo-jun/flutter_apple_product_name/commit/607b63278ab6356ed7c3ab73aa2d33b996756014))"}, {"id": "urn:uuid:2deae453-79fb-4674-8117-50af4a0dc22e", "title": "v0.7.19 of voo_data_grid", "url": "https://pub.dev/packages/voo_data_grid", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T17:24:36.582Z", "summary": "A powerful and flexible data grid widget for Flutter with sorting, filtering, pagination, and remote data support\n\nChangelog excerpt:\n- **Made VooUnsetValue Public for State Extension**   - Renamed `_Unset`to `VooUnsetValue`and made it public\n  - Fixes type errors when extending `VooDataGridState`and overriding `copyWith`\n  - Resolves \"The argument type 'Object?' can't be assigned to the parameter type 'String?'\" errors\n  - Users can now properly implement nullable parameter handling in custom state classes\n  - Added comprehensive documentation with usage example\n  - Example usage when extending VooDataGridState: ```\n`@overrid[...]"}, {"id": "urn:uuid:2f7f592f-dff6-4b6c-a425-112a9f91b52f", "title": "v2.4.1 of oref", "url": "https://pub.dev/packages/oref", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T17:17:18.172Z", "summary": "A reactive state management library for Flutter that adds magic to any Widget with signals, computed values, and effects powered by alien_signals.\n\nChangelog excerpt:\n- Revert signal interface to use `call()`syntax"}, {"id": "urn:uuid:f777dc87-5527-4260-a070-903c4371f4fd", "title": "v1.0.6 of pharos_youtube_player_flutter", "url": "https://pub.dev/packages/pharos_youtube_player_flutter", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T17:09:21.962Z", "summary": "Flutter plugin for playing or streaming inline YouTube videos using the official iFrame player API. This plugin supports both Android and iOS."}, {"id": "urn:uuid:4e1f60fb-d384-4b6b-9326-b2498a48e79b", "title": "v0.7.17 of voo_data_grid", "url": "https://pub.dev/packages/voo_data_grid", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T17:07:27.303Z", "summary": "A powerful and flexible data grid widget for Flutter with sorting, filtering, pagination, and remote data support\n\nChangelog excerpt:\n- **Fixed VooDataGridState.copyWith for Nullable Fields**   - Fixed `copyWith`method to properly handle nullable fields (error, primaryFilterField, primaryFilter)\n  - Uses sentinel value pattern to distinguish between \"parameter not provided\" and \"parameter explicitly set to null\"\n  - Added convenient `clearError()`method to clear error state without lint warnings\n  - Now `state.copyWith()`keeps current error while `state.copyWith(error: null)`or `state.clearError()`clears it\n  - Fixes lint warn[...]"}, {"id": "urn:uuid:387185ad-0fd3-43af-b7ee-1b228b783b67", "title": "v4.0.5 of moneyhash_payment", "url": "https://pub.dev/packages/moneyhash_payment", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T17:02:55.994Z", "summary": "MoneyHash is a Super-API infrastructure for payment orchestration and revenue operations in emerging markets.\n\nChangelog excerpt:\n- Fix MoneyHash supported pod architectures"}, {"id": "urn:uuid:dd09567f-4c88-4b2a-a284-575f00a8c6b0", "title": "v0.0.4 of abk_flutter_utils", "url": "https://pub.dev/packages/abk_flutter_utils", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T17:02:32.887Z", "summary": "A Flutter package providing utilities for networking, storage, logging, and optional Firebase initialization.\n\nChangelog excerpt:\n- Added Dio interceptors\n- Added Storage helpers\n- Added Firebase-safe initialization\n- Added Logging utilities"}, {"id": "urn:uuid:47c602b6-15d0-4cdc-ba27-ebcbde292572", "title": "v1.4.7 of otp_pin_field_custom", "url": "https://pub.dev/packages/otp_pin_field_custom", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T17:01:53.596Z", "summary": "A flutter package which will help you to generate pin code fields with beautiful design and animations. Can be useful for OTP or pin code inputs.\n\nChangelog excerpt:\n- **Changelog & Readme:**   - Revised the changelog to document the latest changes.\n  - Updated Readme file."}, {"id": "urn:uuid:9a0f0b9c-de71-4c9a-bd3a-6b86f0ae2b90", "title": "v1.0.1 of alien_signals", "url": "https://pub.dev/packages/alien_signals", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T17:00:13.639Z", "summary": "Alien Signals is a reactive state management library that brings the power of signals to Dart and Flutter applications.\n\nChangelog excerpt:\nStatus: Unreleased (WIP)\n\n- Change signal interface to use `call()`instead of `.value`getter/setter"}, {"id": "urn:uuid:bd9e4eea-2cfd-469d-b529-67bb58ed752e", "title": "v0.11.0-beta.1 of seald_sdk_flutter", "url": "https://pub.dev/packages/seald_sdk_flutter", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T16:58:53.236Z", "summary": "Seald SDK for Flutter: simple end-to-end encryption for your app"}, {"id": "urn:uuid:b8e7ed23-df1f-493d-981b-a46607b1aba9", "title": "v2.1.0 of fl_downloader", "url": "https://pub.dev/packages/fl_downloader", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T16:58:22.458Z", "summary": "A plugin to download files using the native capabilities. URLSession on iOS, DownloadManager on Android and BITS on Windows.\n\nChangelog excerpt:\n- Added missing `NSPrivacyCollectedDataTypes`key on PrivacyInfo (thx to [@SullyMatthieu](https://github.com/SullyMatthieu))\n- Fixed android MIME type detection when the file name contains spaces (thx to [@monoblaine](https://github.com/monoblaine))\n- Migrate android build scripts from Groovy to Kotlin DSL\n- Bumped iOS min version to 13.0 to match latest flutter min iOS version support\n- Bumped android min version to 24 to match latest flutter min android version support"}, {"id": "urn:uuid:271adb7a-3125-4cc6-b614-758c3b937895", "title": "v1.2.6 of stts", "url": "https://pub.dev/packages/stts", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T16:51:37.815Z", "summary": "Speech-to-Text and Text-to-Speech plugin. Offline first.\n\nChangelog excerpt:\n- fix(Android): Check if on device recognition is available before creating."}, {"id": "urn:uuid:098ac60b-e3f7-43cc-915a-78468ca8790c", "title": "v0.7.16 of voo_data_grid", "url": "https://pub.dev/packages/voo_data_grid", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T16:49:24.858Z", "summary": "A powerful and flexible data grid widget for Flutter with sorting, filtering, pagination, and remote data support\n\nChangelog excerpt:\n- **Error Callback Functionality**   - Added `onError`callback parameter to `VooDataGrid`, `VooDataGridStateless`, and `DataGridCore`\n  - Callback is triggered when error state transitions from null to non-null or when error message changes\n  - Prevents loading indicator from persisting indefinitely when errors occur\n  - Works alongside existing `errorBuilder`for comprehensive error handling\n  - Allows users to show snackbars, log errors, or take custom actions when errors occur\n  - Implemented [...]"}, {"id": "urn:uuid:75b04415-0bcc-422d-bef0-e0cea74a53fb", "title": "v1.4.6 of otp_pin_field_custom", "url": "https://pub.dev/packages/otp_pin_field_custom", "source": "pub_dev", "section": "Ecosystem", "publishedAt": "2025-09-30T16:46:02.883Z", "summary": "A flutter package which will help you to generate pin code fields with beautiful design and animations. Can be useful for OTP or pin code inputs.\n\nChangelog excerpt:\n- **Changelog & Readme:**   - Revised the changelog to document the latest changes.\n  - Updated Readme file."}]