import express from 'express';
import multer from 'multer';
import { uuidv4, z } from 'zod';
import cors from 'cors';
import * as fs from 'fs/promises';
import * as path from 'path';
import { handleStream } from '../utils/streamHandler';
import { runWithContext, getContextValue } from '../utils/requestContext';
import { EnhancedStreamHandler } from '../utils/enhancedStreamHandler';
import { sessionEventBus } from '../utils/sessionEventBus';
import type { AgentEvent } from '../types/agentEvents';
import { sessionStreams } from '../utils/sessionStreams';
import { interruptionEventSchema, type InterruptionEventInput } from '../types/interruption';
import { interruptBus } from '../utils/interruptBus';
import { config, isGeminiModel } from '../utils/config';
import { simpleChat } from './simpleChat';
import { convertUIMessagesToAgentFormat } from '../utils/messageConverter';
import {
  isRateLimited,
  getRateLimitDelay,
  updateRateLimit,
  validateAndPruneMessages,
  getRateLimitStates
} from '../utils/tokenLimiter';
import { initializeProjectFeeder } from '../tools/contextualExploration';
import { rateLimitProtection } from '../utils/rateLimitProtection';
import { ContextualProjectFeeder } from '../utils/contextualProjectFeeder';
import { memoryRoutes } from './memoryRoutes';
import { orchestrator } from '../index';
import { mcpRoutes } from './mcpRoutes';
import reminderRoutes from './reminderRoutes';
import recoveryRoutes from '././recoveryRoutes';
import syncRoutes from './syncRoutes';
import { computerUseRoutes } from './computerUseRoutes';
import voiceRoutes from './voiceRoutes';
import connectorRoutes from './connectorRoutes';
import authRoutes from './routes/auth';
import filesRouter from './routes/files';
import { getUploadsDir } from './utils/uploads';
// Removed OpenAI SDK dependencies - functionality will be restored with Vercel AI SDK
// import { initializeReminderAgent } from '../agents/ReminderAgent';
import { recoverySystem } from '@/recovery/RecoveryIntegration';
// import { initializeTaskOrchestratorRecovery } from '../agents/TaskOrchestrator';
import session from 'express-session';
import passport from '../config/passport';
import { memoryManager } from '../memory/MemoryManager';
import { refreshConversationSummary } from '../services/conversationSummaryService';
import { runMigrations } from '../db/migrate';

const app = express();
const PORT = process.env.PORT ? parseInt(process.env.PORT) : 3001; // API server port (Vite dev server uses 3000)




// Configure CORS for private network access
const defaultCorsPatterns: RegExp[] = [
  /^https?:\/\/localhost(?::\d+)?$/,
  /^https?:\/\/127\.0\.0\.1(?::\d+)?$/,
  /^https?:\/\/10\.\d{1,3}\.\d{1,3}\.\d{1,3}(?::\d+)?$/,
  /^https?:\/\/192\.168\.\d{1,3}\.\d{1,3}(?::\d+)?$/,
  /^https?:\/\/172\.(1[6-9]|2\d|3[01])\.\d{1,3}\.\d{1,3}(?::\d+)?$/,
];

type OriginMatcher = (origin: string) => boolean;

const additionalOriginMatchers: OriginMatcher[] = (process.env.CORS_ALLOWED_ORIGINS || '')
  .split(',')
  .map(origin => origin.trim())
  .filter(origin => origin.length > 0)
  .map(origin => {
    if (origin.startsWith('regex:')) {
      try {
        const pattern = new RegExp(origin.slice('regex:'.length));
        return (value: string) => pattern.test(value);
      } catch (error) {
        console.warn(`Invalid CORS regex provided: ${origin}`, error);
        return () => false;
      }
    }
    return (value: string) => value === origin;
  });

const isAllowedOrigin = (origin: string): boolean => {
  if (defaultCorsPatterns.some(pattern => pattern.test(origin))) return true;
  if (additionalOriginMatchers.some(matcher => matcher(origin))) return true;
  return false;
};

const corsOptions = {
  origin(origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) {
    // Allow requests with no origin (mobile apps, same-origin SSR, etc.)
    if (!origin) return callback(null, true);

    if (isAllowedOrigin(origin)) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked origin: ${origin}`);
      callback(new Error('Not allowed by CORS'), false);
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
};

app.use(cors(corsOptions));
app.use(express.json({ limit: '10mb' })); // Increase limit for large requests

// Session management
app.use(
  session({
    secret: process.env.SESSION_SECRET || 'your_secret_key_dev',
    resave: true, // Changed to true to ensure session saves
    saveUninitialized: true,
    cookie: {
      secure: false, // Allow cookies over HTTP in development
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    },
  })
);

// Passport middleware
app.use(passport.initialize());
app.use(passport.session());

// Helper functions for intelligent model selection
function determineTaskType(userMessage: string, messageCount: number): 'orchestration' | 'analysis' | 'coding' | 'simple' {
  const content = userMessage.toLowerCase();

  // Complex orchestration indicators
  if (messageCount > 5 ||
      content.includes('multiple') ||
      content.includes('analyze entire') ||
      content.includes('across all') ||
      content.includes('comprehensive') ||
      content.match(/\b(refactor|modernize|audit|review)\s+(the|my|all)\s+(codebase|project|application)\b/)) {
    return 'orchestration';
  }

  // Analysis tasks - expanded to catch exploratory questions
  if (content.includes('analyze') ||
      content.includes('understand') ||
      content.includes('examine') ||
      content.includes('investigate') ||
      content.includes('research') ||
      content.includes('tell me about') ||
      content.includes('what can you tell') ||
      content.includes('what is this') ||
      content.includes('describe') ||
      content.includes('explain') ||
      content.includes('overview') ||
      content.includes('what does') ||
      content.includes('how does') ||
      content.includes('how works') ||
      (content.includes('project') && (content.includes('about') || content.includes('this'))) ||
      (content.includes('codebase') && (content.includes('about') || content.includes('this')))) {
    return 'analysis';
  }

  // Coding tasks
  if (content.includes('code') ||
      content.includes('implement') ||
      content.includes('write') ||
      content.includes('create') ||
      content.includes('build') ||
      content.includes('develop')) {
    return 'coding';
  }

  return 'simple';
}


// Add security headers for private network usage
app.use((_req, res, next) => {
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  next();
});

// Memory API routes
app.use('/api/memory', memoryRoutes);

// MCP API routes
app.use('/api/mcp', mcpRoutes);

// Reminder API routes
app.use('/api/reminders', reminderRoutes);

// Recovery API routes
app.use('/api/recovery', recoveryRoutes);

// Device synchronization routes
app.use('/api/sync', syncRoutes);

// Computer Use Task Management routes - fully restored
app.use('/api/computer-use', computerUseRoutes);
// Voice agent routes (realtime token + delegation)
app.use('/api/voice', voiceRoutes);

// Connector and OAuth routes
app.use('/api', connectorRoutes);

// OAuth authentication routes for Google Workspace
app.use('/api/auth', authRoutes);


// Add the following lines after the authRoutes

// Serve uploaded files statically and upload endpoint
const uploadsDir = getUploadsDir();
void (async () => { try { await fs.mkdir(uploadsDir, { recursive: true }); } catch {} })();
app.use('/uploads', express.static(uploadsDir));

const storage = multer.diskStorage({
  destination: (_req, _file, cb) => cb(null, uploadsDir),
  filename: (_req, file, cb) => {
    const unique = uuidv4();
    const ext = path.extname(file.originalname || '');
    cb(null, `${unique}${ext}`);
  }
});
const upload = multer({ storage, fileFilter: (req, file, cb) => { const allowedMimes = ['image/jpeg', 'image/png', 'application/pdf', 'text/plain']; if (allowedMimes.includes(file.mimetype)) { cb(null, true); } else { cb(new Error('Invalid file type.')); } } });

app.post('/api/upload', upload.single('file'), (req, res) => {
  const f = req.file as Express.Multer.File | undefined;
  if (!f) return res.status(400).json({ success: false, error: 'No file uploaded' });
  const url = `/uploads/${f.filename}`;
  return res.json({ success: true, files: [{ url, name: f.originalname, type: f.mimetype, size: f.size }] });
});

// Session event replay stream (used for UI resync after reload)
app.get('/api/session/events', (req, res) => {
  const sessionId = String(req.query.sessionId || '').trim();
  if (!sessionId) {
    res.status(400).json({ error: 'sessionId is required' });
    return;
  }

  const sinceRaw = req.query.since;
  let sinceTs: number | undefined;
  if (typeof sinceRaw === 'string' && sinceRaw.length > 0) {
    const parsed = Number.isNaN(Number(sinceRaw)) ? Date.parse(sinceRaw) : Number(sinceRaw);
    if (Number.isFinite(parsed)) {
      sinceTs = parsed;
    }
  }

  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.setHeader('X-Accel-Buffering', 'no');
  res.write('retry: 5000\n\n');

  const sendEvent = (event: AgentEvent) => {
    try {
      res.write(`data: ${JSON.stringify(event)}\n\n`);
    } catch (error) {
      console.warn('Failed to write session event to SSE client:', error);
    }
  };

  try {
    const backlog = sessionEventBus.getEvents(sessionId, sinceTs);
    for (const event of backlog) {
      sendEvent(event);
    }
  } catch (error) {
    console.warn('Failed to load session event backlog:', error);
  }

  const unsubscribe = sessionEventBus.subscribe(sessionId, sendEvent);

  const heartbeat = setInterval(() => {
    try { res.write(`: ping ${Date.now()}\n\n`); } catch {}
  }, 25000);

  req.on('close', () => {
    clearInterval(heartbeat);
    unsubscribe();
    try { res.end(); } catch {}
  });
});

// Project context endpoints
app.post('/api/analyze-project', async (req, res) => {
  try {
    const analyzeProjectRequestSchema = z.object({
      path: z.string().min(1, "Project path is required"),
    });

    const validationResult = analyzeProjectRequestSchema.safeParse(req.body);

    if (!validationResult.success) {
      return res.status(400).json({
        error: 'Invalid request data',
        details: validationResult.error.issues,
      });
    }

    const { path: projectPath } = validationResult.data;

    const absolutePath = path.resolve(projectPath);
    const analysis: any = {
      path: absolutePath,
      name: path.basename(absolutePath),
    };

    // Check for package.json
    try {
      const packageJsonPath = path.join(absolutePath, 'package.json');
      const packageJsonContent = await fs.readFile(packageJsonPath, 'utf-8');
      const packageJson = JSON.parse(packageJsonContent);

      analysis.packageJson = packageJson;
      analysis.dependencies = Object.keys(packageJson.dependencies || {});
      analysis.devDependencies = Object.keys(packageJson.devDependencies || {});
      analysis.scripts = Object.keys(packageJson.scripts || {});
    } catch {
      // No package.json found
    }

    // Check for git repository
    try {
      await fs.access(path.join(absolutePath, '.git'));
      analysis.gitRepo = true;
    } catch {
      analysis.gitRepo = false;
    }

    // Detect project type
    const files = await fs.readdir(absolutePath);
    if (files.includes('package.json')) {
      analysis.type = 'node';
      if (analysis.dependencies?.includes('react')) {
        analysis.type = 'react';
      }
    } else if (files.includes('Cargo.toml')) {
      analysis.type = 'rust';
    } else if (files.includes('go.mod')) {
      analysis.type = 'go';
    } else if (files.includes('requirements.txt') || files.includes('pyproject.toml')) {
      analysis.type = 'python';
    } else {
      analysis.type = 'generic';
    }

    res.json(analysis);
  } catch (error) {
    console.error('Project analysis error:', error);
    res.status(500).json({
      error: 'Failed to analyze project',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.post('/api/list-directory', async (req, res) => {
  try {
    const { dirPath, includeHidden = false, recursive = false } = req.body;

    if (!dirPath) {
      return res.status(400).json({ error: 'Directory path is required' });
    }

    const absolutePath = path.resolve(dirPath);

    if (!recursive) {
      const entries = await fs.readdir(absolutePath, { withFileTypes: true });
      const files = entries
        .filter(entry => includeHidden || !entry.name.startsWith('.'))
        .map(entry => ({
          name: entry.name,
          type: entry.isDirectory() ? 'directory' : 'file',
          path: path.join(absolutePath, entry.name),
        }));

      res.json({
        success: true,
        path: absolutePath,
        files,
      });
    } else {
      // Recursive listing (limited depth for performance)
      const walk = async (dir: string, depth = 0): Promise<string[]> => {
        if (depth > 3) return []; // Limit recursion depth

        const entries = await fs.readdir(dir, { withFileTypes: true });
        const files = await Promise.all(
          entries
            .filter(entry => includeHidden || !entry.name.startsWith('.'))
            .filter(entry => entry.name !== 'node_modules') // Skip node_modules
            .map(async entry => {
              const fullPath = path.join(dir, entry.name);
              if (entry.isDirectory()) {
                return walk(fullPath, depth + 1);
              }
              return fullPath;
            })
        );
        return files.flat();
      };

      const allFiles = await walk(absolutePath);
      res.json({
        success: true,
        path: absolutePath,
        files: allFiles,
      });
    }
  } catch (error) {
    console.error('Directory listing error:', error);
    res.status(500).json({
      error: 'Failed to list directory',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Health check endpoint
app.get('/api/health', (_req, res) => {
  res.json({ status: 'ok', version: '1.0.0' });
});

// Network info endpoint for getting actual network IP
app.get('/api/network-info', (_req, res) => {
  try {
    const os = require('os');
    const networkInterfaces = os.networkInterfaces();
    let localIP = null;

    // Find the first non-internal IPv4 address
    for (const interfaceName in networkInterfaces) {
      const interfaces = networkInterfaces[interfaceName];
      if (interfaces) {
        for (const iface of interfaces) {
          if (iface.family === 'IPv4' && !iface.internal) {
            localIP = iface.address;
            break;
          }
        }
      }
      if (localIP) break;
    }

    const networkUrl = localIP ? `http://${localIP}:3002` : null;

    res.json({
      networkUrl,
      localIP,
      available: !!localIP
    });
  } catch (error) {
    console.error('Network info error:', error);
    res.status(500).json({
      error: 'Failed to get network info',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Rate limit status endpoint
app.get('/api/rate-limit-status', (req, res) => {
  try {
    const model = req.query.model as string || config.openai.defaultModel;
    const isLimited = isRateLimited(model);
    const delaySeconds = getRateLimitDelay(model) / 1000;

    // Get rate limit state for token usage info
    const rateLimitStates = getRateLimitStates();
    const rateLimitState = rateLimitStates.get(model);
    const now = Date.now();
    const recentRequests = rateLimitState?.requests?.filter(
      (req: any) => now - req.timestamp < 60000
    ) || [];

    const tokenUsage = recentRequests.reduce(
      (sum: number, req: any) => sum + req.tokens, 0
    );

    res.json({
      model,
      isLimited,
      delaySeconds: Math.ceil(delaySeconds),
      requestsInLastMinute: recentRequests.length,
      tokenUsage
    });
  } catch (error) {
    console.error('Rate limit status error:', error);
    res.status(500).json({
      error: 'Failed to get rate limit status',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Unified agent status endpoint
app.get('/api/agent-status', async (_req, res) => {
  try {
    const { getDanteStatus } = await import('../index');
    const status = getDanteStatus();

    res.json({
      success: true,
      status,
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('Failed to get agent status:', error);
    res.status(500).json({
      error: 'Failed to get agent status',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Model switching endpoint
app.post('/api/switch-model', async (req, res) => {
  try {
    const { model, reason, sessionId } = req.body;

    if (!model) {
      return res.status(400).json({ error: 'Model parameter is required' });
    }

    const { switchModel } = await import('../index');
    const result = await switchModel(model, reason, sessionId);

    res.json({
      success: result.success,
      result,
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('Model switch failed:', error);
    res.status(500).json({
      error: 'Model switch failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Model recommendation endpoint
app.post('/api/model-recommendation', async (req, res) => {
  try {
    const { input, taskType } = req.body;

    if (!input) {
      return res.status(400).json({ error: 'Input parameter is required' });
    }

    const { getModelRecommendation } = await import('../index');
    const recommendation = getModelRecommendation(input, taskType);

    res.json({
      success: true,
      recommendation,
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('Failed to get model recommendation:', error);
    res.status(500).json({
      error: 'Failed to get model recommendation',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Basic request logging to help diagnose remote chat issues (captures origin + method)
app.use('/api/chat', (req, _res, next) => {
  try {
    const origin = typeof req.headers.origin === 'string' ? req.headers.origin : 'no-origin';
    const forwarded = req.headers['x-forwarded-for'];
    const clientIp = Array.isArray(forwarded) ? forwarded[0] : (typeof forwarded === 'string' ? forwarded : req.ip);
    console.log(`[chat] ${req.method} origin=${origin} ip=${clientIp ?? 'unknown'}`);
  } catch (logErr) {
    console.warn('Failed to log /api/chat request metadata:', logErr);
  }
  next();
});

// Main chat endpoint with unified agent system

// Define Zod schema for chat request body
const chatRequestSchema = z.object({
  messages: z.array(z.object({
    role: z.string(),
    content: z.string(),
    attachments: z
      .array(
        z.object({
          filename: z.string(),
          url: z.string().optional(),
          dataUrl: z.string().optional(),
        })
      )
      .optional(),
  })).min(1, "Messages array cannot be empty"),
  model: z.string().optional(),
  useMCP: z.boolean().optional(),
  projectContext: z.object({
    path: z.string(),
    name: z.string().optional(),
    type: z.string().optional(),
    metadata: z.object({
      dependencies: z.array(z.string()).optional(),
      scripts: z.array(z.string()).optional(),
      gitRepo: z.boolean().optional(),
    }).optional(),
  }).optional(),
  userId: z.string().optional(),
});

app.post('/api/chat', async (req, res) => {
  try {
    // Validate request body against schema
    const validationResult = chatRequestSchema.safeParse(req.body);

    if (!validationResult.success) {
      return res.status(400).json({
        error: 'Invalid request data',
        details: validationResult.error.issues,
      });
    }

    const { messages, model, useMCP, projectContext, userId } = validationResult.data;
    const projectWorkingDir = projectContext?.path ? path.resolve(projectContext.path) : undefined;

    if (!messages || !Array.isArray(messages)) {
      return res.status(400).json({ error: 'Invalid messages format' });
    }

    // Create session ID if not provided
    const sessionId = req.headers['x-session-id'] as string || `session_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

    // Extract user ID from request (header or body)
    const requestUserId = (req.headers['x-user-id'] as string) || userId || sessionId;

    // Determine task type from user message
    const userMessage = messages.length > 0 ? messages[messages.length - 1]?.content || '' : '';
    const taskType = determineTaskType(userMessage, messages.length);

    console.log(`🎯 Unified Agent Request - Task: ${taskType}, Model: ${model || 'auto'}, Session: ${sessionId}`);

    // Set headers for streaming
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Agent-Type', 'unified');
    res.setHeader('X-Session-ID', sessionId);

    // Convert messages to proper format for unified agent
    const convertedMessages = convertUIMessagesToAgentFormat(messages);

    // Add project context if provided
    if (projectContext && projectContext.path) {
      const contextMessage = {
        role: 'system',
        content: `Current project context:\n- Working Directory: ${projectContext.path}\n- Project Name: ${projectContext.name || 'Unknown'}\n- Project Type: ${projectContext.type || 'Unknown'}\n${projectContext.metadata?.dependencies ? `- Dependencies: ${projectContext.metadata.dependencies.join(', ')}` : ''}\n${projectContext.metadata?.scripts ? `- Available Scripts: ${projectContext.metadata.scripts.join(', ')}` : ''}\n${projectContext.metadata?.gitRepo ? '- Git Repository: Yes' : ''}\n\nPlease consider this project context when providing assistance.`
      };
      convertedMessages.unshift(contextMessage);
    }

    // If latest user message has attachments, prepend a short system note so agents know to use file tools
    try {
      const lastMsg = messages[messages.length - 1] as any;
      const atts = Array.isArray(lastMsg?.attachments) ? lastMsg.attachments : [];
      if (atts.length > 0) {
        const list = atts
          .map((a: any) => {
            const filename = a?.filename || 'unnamed';
            const url = typeof a?.url === 'string' && a.url.length > 0 ? a.url : null;
            if (url) {
              return `${filename} (${url})`;
            }
            if (a?.dataUrl) {
              return `${filename} (embedded data)`;
            }
            return filename;
          })
          .filter(Boolean)
          .slice(0, 5)
          .join(', ');
        convertedMessages.unshift({
          role: 'system',
          content: `The user attached ${atts.length} file(s)${list ? `: ${list}` : ''}. Uploaded files are stored under /uploads/<fileId.ext> with normalized filenames—use those /uploads URLs with the file tools instead of the original names.`,
        } as any);
      }
    } catch (err) {
      console.warn('Failed to process attachments for system message:', err);
    }

    // Inject dynamic tool availability to encourage tool use (Calendar/Gmail/Drive)
    try {
      const { connectorService } = await import('../services/connectorService');

      // Use request-specific user ID instead of default user
      // This ensures proper multi-user isolation
      let servicesLine = '';
      let calendarUser: string | null = null;

      // Check if the current user has any connected services
      const userHasConnections = await connectorService.hasUserConnections(requestUserId).catch(() => false);

      if (userHasConnections) {
        calendarUser = requestUserId;
        const connected = connectorService.getConnectedServices(requestUserId);
        const svc = new Set(connected);
        servicesLine = `Connected Google Workspace services for current user: ` +
          [
            svc.has('connector_gmail') ? 'Gmail' : null,
            svc.has('connector_googlecalendar') ? 'Calendar' : null,
            svc.has('connector_googledrive') ? 'Drive' : null,
          ].filter(Boolean).join(', ');
      }

      const toolInventory: string[] = [];
      toolInventory.push('- Auth: ensure_workspace_auth(service=gmail|calendar|drive)');
      toolInventory.push('- Calendar: search_calendar_events, get_today_calendar_events, get_week_calendar_events, read_calendar_event, get_upcoming_meetings');
      toolInventory.push('- Gmail: search_gmail, get_recent_gmail_emails, read_gmail_email, batch_read_gmail_emails');
      toolInventory.push('- Drive: search_google_drive, get_recent_drive_documents, fetch_drive_file, list_shared_drives, search_drive_by_type');

      const availabilityMsg = {
        role: 'system',
        content: `Tool Availability:\n${servicesLine || 'No authenticated services detected for current user. If a tool reports not authenticated, return its authUrl to the user.'}\nCurrent User ID: ${requestUserId}\n\nUse the following tools when appropriate:\n${toolInventory.join('\n')}\n\nImportant:\n- For email/calendar/drive intents, first call ensure_workspace_auth for the relevant service; if connected=false, surface its authUrl and stop.\n- For calendar queries ("calendar", "events", "meetings"), call Calendar tools first. Do NOT claim lack of access.\n- If a tool reports not authenticated, surface its authUrl so the user can connect.\n- Prefer tool results over assumptions; summarize tool output for the user.\n- All tool access is isolated to the current user's authenticated services only.`
      } as any;
      convertedMessages.unshift(availabilityMsg);
    } catch (availErr) {
      console.warn('Could not inject dynamic tool availability:', availErr);
    }

    // Check if client wants enhanced events (via query param or header)
    const wantsEvents = req.query.events === 'true' || req.headers['x-stream-events'] === 'true';
    // Optional: force a specific worker delegation (targeted retry)
    const forceWorkerHeader = (req.headers['x-force-worker'] as string | undefined)?.toString();
    const forceWorker = forceWorkerHeader && forceWorkerHeader.trim().length > 0 ? forceWorkerHeader.trim() : undefined;

    try {
      // Import unified agent system
      const { runDante } = await import('../index');

      // Set model-specific headers
      res.setHeader('X-Model-Type', model ? 'Requested' : 'Auto-Selected');

      if (wantsEvents) {
        // Use enhanced stream handler for detailed events
        const enhancedHandler = new EnhancedStreamHandler(res, { sessionId });
        console.log(`🫀 [SSE] connected (session: ${sessionId})`);
        // Register stream handler for this session so interrupts can stop it fast
        try { sessionStreams.register(sessionId, enhancedHandler); } catch {}
        // Wire interruption listeners for immediate acknowledgement and stopping
        const onCancel = (ev: any) => { if (ev?.sessionId === sessionId) { try { enhancedHandler.quickComplete('Cancelled'); } catch {} } };
        const onPause = (ev: any) => { if (ev?.sessionId === sessionId) { try { enhancedHandler.quickComplete('Paused'); } catch {} } };
        const onSteer = (ev: any) => { if (ev?.sessionId === sessionId) { try { enhancedHandler.quickComplete('Update applied'); } catch {} } };
        const onReplace = (ev: any) => { if (ev?.sessionId === sessionId) { try { enhancedHandler.quickComplete('Next step replaced'); } catch {} } };
        interruptBus.on('cancel', onCancel);
        interruptBus.on('pause', onPause);
        interruptBus.on('steer', onSteer);
        interruptBus.on('replace-next', onReplace);

        // Emit an immediate heartbeat + thinking marker so UI shows activity instantly
        try { enhancedHandler.heartbeat('sse_connected'); } catch {}
        try { enhancedHandler.startThinking('Routing to the best agent…'); } catch {}
        const doCleanup = () => {
          try { sessionStreams.unregister(sessionId); } catch {}
          try {
            interruptBus.off('cancel', onCancel);
            interruptBus.off('pause', onPause);
            interruptBus.off('steer', onSteer);
            interruptBus.off('replace-next', onReplace);
          } catch {}
        };

        // Pass step-level events from Vercel AI SDK to SSE
        const onStepFinish = ({ toolCalls, toolResults, text, finishReason, usage, model: stepModel }: any) => {
          try {
            if (Array.isArray(toolCalls)) {
              for (const tc of toolCalls) {
                const name = (tc.toolName || tc.name || tc.tool_name || tc.function?.name || 'unknown_tool') as string;
                const args = tc.input || tc.args || tc.function?.arguments || tc.parameters || {};
                if (name && name !== 'unknown_tool') {
                  enhancedHandler.toolCall(name, args);
                }
              }
            }
            if (Array.isArray(toolResults)) {
              for (const tr of toolResults) {
                const name = (tr.toolName || tr.name || tr.tool_name || tr.function?.name || 'unknown_tool') as string;
                const output = tr.output ?? tr.result ?? tr.content ?? tr.data ?? null;
                if (name && name !== 'unknown_tool') {
                  enhancedHandler.toolResult(name, output);
                  // Bridge: when TaskOrchestrator ran via delegate_to_agents tool,
                  // propagate plan/delegation events to the UI.
                  const streamed = getContextValue<boolean>('orchestration_streamed');
                  if (name === 'delegate_to_agents' && output && !streamed) {
                    try {
                      const obj = typeof output === 'string' ? JSON.parse(output) : output;
                      const plan = obj?.plan;
                      if (plan && Array.isArray(plan.steps)) {
                        enhancedHandler.planCreated({ summary: plan.summary, steps: plan.steps });
                      }
                      const steps = obj?.stepResults;
                      if (Array.isArray(steps)) {
                        for (const s of steps) {
                          const agentDisp = (s.agent || '').replace(/Agent$/,'').replace(/([a-z])([A-Z])/g,'$1 $2').trim() || 'Agent';
                          enhancedHandler.delegationStart({ stepId: s.stepId, agent: agentDisp, title: s.title, description: s.description });
                          enhancedHandler.delegationEnd({ stepId: s.stepId, agent: agentDisp, success: !!s.success });
                        }
                      }
                      // Surface synthesized text if provided by orchestration output
                      const synthesizedText = typeof obj?.finalMessage === 'string' ? obj.finalMessage : (typeof obj?.text === 'string' ? obj.text : undefined);
                      if (synthesizedText && synthesizedText.trim().length > 0) {
                        enhancedHandler.sendMessage(synthesizedText);
                      }
                    } catch (e) {
                      console.warn('Failed to surface plan/delegation from delegate_to_agents output:', e);
                    }
                  } else if (name === 'auto_follow_up_workflow' && output) {
                    // Surface AutoFollowUpManager results with friendly messages + plan/delegation
                    try {
                      const obj = typeof output === 'string' ? JSON.parse(output) : output;
                      if (typeof obj?.docSummaryText === 'string' && obj.docSummaryText.trim().length > 0) {
                        enhancedHandler.sendMessage(obj.docSummaryText);
                      }
                      if (typeof obj?.codebaseSummaryText === 'string' && obj.codebaseSummaryText.trim().length > 0) {
                        enhancedHandler.sendMessage(obj.codebaseSummaryText);
                      }
                      const plan = obj?.plan;
                      if (plan && Array.isArray(plan.steps)) {
                        enhancedHandler.planCreated({ summary: plan.summary, steps: plan.steps });
                      }
                      const steps = obj?.stepResults;
                      if (Array.isArray(steps)) {
                        for (const s of steps) {
                          const agentDisp = (s.agent || '').replace(/Agent$/,'').replace(/([a-z])([A-Z])/g,'$1 $2').trim() || 'Agent';
                          enhancedHandler.delegationStart({ stepId: s.stepId, agent: agentDisp, title: s.title, description: s.description });
                          enhancedHandler.delegationEnd({ stepId: s.stepId, agent: agentDisp, success: !!s.success });
                        }
                      }
                      const fm = obj?.finalMessage;
                      if (typeof fm === 'string' && fm.trim().length > 0) {
                        enhancedHandler.sendMessage(fm);
                      }
                    } catch (e) {
                      console.warn('Failed to surface AutoFollowUp results:', e);
                    }
                  }
                }
              }
            }

            // Emit token usage if provided by the step
            try {
              if (usage) {
                const input = usage.inputTokens ?? usage.input ?? usage.promptTokens ?? undefined;
                const output = usage.outputTokens ?? usage.output ?? usage.completionTokens ?? undefined;
                const total = usage.totalTokens ?? usage.total ?? ((input || 0) + (output || 0));
                const modelUsed = stepModel || usage.model || model || undefined;
                enhancedHandler.tokenUpdate({ input, output, total }, modelUsed);
              }
            } catch (e) {
              console.warn('onStepFinish: failed to emit token usage:', e);
            }
          } catch (e) {
            console.warn('onStepFinish event handling error:', e);
          }
        };

        const onAgentSelected = ({ agent, confidence }: { agent: string; confidence: number }) => {
          try {
            enhancedHandler.agentSwitch(agent);
            // Also emit a trace heartbeat for responsiveness
            // The agentSwitch already sends a heartbeat and agent_switch event
          } catch (e) {
            console.warn('onAgentSelected SSE emission error:', e);
          }
        };
        const onDelegationStart = (payload: { stepId: string; agent: string; title?: string; description?: string }) => {
          try { enhancedHandler.delegationStart(payload); } catch (e) { console.warn('onDelegationStart SSE error:', e); }
        };
        const onDelegationEnd = (payload: { stepId: string; agent: string; success: boolean }) => {
          try { enhancedHandler.delegationEnd(payload); } catch (e) { console.warn('onDelegationEnd SSE error:', e); }
        };
        const onPlanCreated = (payload: { summary?: string; steps: any[] }) => {
          try { enhancedHandler.planCreated(payload); } catch (e) { console.warn('onPlanCreated SSE error:', e); }
          // Persist plan to session for follow-ups like "proceed"
          try {
            const plan = { summary: payload?.summary, steps: Array.isArray(payload?.steps) ? payload.steps : [] };
            orchestrator.setSessionPlan(sessionId, plan);
          } catch (persistErr) {
            console.warn('onPlanCreated persist error:', persistErr);
          }
        };

        const requestContextStore: Record<string, any> = {
          sessionId,
          // Expose orchestration callbacks so tools like delegate_to_agents
          // can forward streaming plan/delegation events to SSE in real-time.
          onPlanCreated,
          onDelegationStart,
          onDelegationEnd,
          // Stream inner tool events from delegated agents
          onInnerToolCall: (name: string, args: any) => {
            try { enhancedHandler.toolCall(name, args); } catch {}
          },
          onInnerToolResult: (name: string, output: any) => {
            try { enhancedHandler.toolResult(name, output); } catch {}
          },
          // Surface a compact plan issues summary in the chat
          onPlanIssues: (issues: Array<{ stepId: string; agent: string; title?: string; reason?: string }>) => {
            try {
              if (!Array.isArray(issues) || issues.length === 0) return;
              const lines = issues.slice(0, 10).map(i => `- [${i.agent}] ${i.title || i.stepId}: ${i.reason || 'blocked'}`);
              const more = issues.length > 10 ? `\n…and ${issues.length - 10} more` : '';
              const msg = `Plan issues detected:\n${lines.join('\n')}${more}`;
              enhancedHandler.sendMessage(msg);
            } catch {}
          },
        };
        if (projectWorkingDir) requestContextStore.cwd = projectWorkingDir;

        await runWithContext(requestContextStore, async () => {
          // Try to provide contextual file list for orchestration heuristics
          let runContext: any = undefined;
          try {
            // Use project feeder only when available and likely helpful
            const likelyLargeScope = taskType === 'orchestration' || /\b(codebase|repository|entire|whole|comprehensive|analyze|tests?)\b/i.test(userMessage || '');
            if (projectFeeder && likelyLargeScope) {
              const ctxResult = await projectFeeder.getContextualProjectInfo(userMessage, messages, model || 'gpt-5');
              if (ctxResult?.context?.relevantFiles?.length) {
                // Pass selected files to orchestrator to trigger file-based chunking
                runContext = { files: ctxResult.context.relevantFiles };
              }
            }
          } catch (e) {
            console.warn('Contextual project feeder failed; proceeding without files:', e);
          }
          // Execute once with event callbacks for richer events
          const streamWithEvents = await runDante(convertedMessages, {
            stream: true,
            model: model,
            useMCP: useMCP !== false,
            taskType: taskType as any,
            sessionId,
            userId: requestUserId,
            useMemory: true,
            context: runContext,
            forceWorker,
            onStepFinish,
            onAgentSelected,
            onDelegationStart,
            onDelegationEnd,
            onPlanCreated,
            workingDirectory: projectWorkingDir,
          });
          // Handle first run but do not finalize the SSE yet; we may auto-continue
          const firstRun = await enhancedHandler.handleAgentStream(streamWithEvents as any, {
            finalize: false,
            onRateLimit: async (reason: string) => {
              try {
                // Build a compact continuation instruction for the fallback run
                const continuationInstruction = {
                  role: 'system',
                  content: `Autonomous continuation required:\nPrevious model hit a rate limit mid-run (${reason || 'rate limit'}). Continue without repeating prior steps. Use the current filesystem state and working directory.`
                } as any;

                const fallbackModel = 'gemini-2.5-flash';

                // Execute a fallback streaming run using a lighter model
                return await runDante([ ...convertedMessages, continuationInstruction ], {
                  stream: true,
                  model: fallbackModel,
                  useMCP: useMCP !== false,
                  taskType: taskType as any,
                  sessionId,
                  userId: requestUserId,
                  useMemory: true,
                  forceWorker,
                  onStepFinish,
                  onAgentSelected,
                  onDelegationStart,
                  onDelegationEnd,
                  onPlanCreated,
                  workingDirectory: projectWorkingDir,
                });
              } catch (fallbackErr) {
                console.warn('Enhanced stream fallback run failed:', fallbackErr);
                return null as any;
              }
            }
          });

          // Heuristic: if the model executed tools but produced little/no text,
          // auto-continue with a follow-up prompt to complete the task.
          try {
            const hadTools = Array.isArray(firstRun?.events) && firstRun.events.some((e: any) => e.type === 'tool_result');
            const producedMessage = Array.isArray(firstRun?.events) && firstRun.events.some((e: any) => e.type === 'message' && typeof e.data?.message === 'string' && !/I finished running tools and have results/i.test(e.data.message));
            const summaryOnly = typeof firstRun?.finalOutput === 'string' && /I finished running tools and have results/i.test(firstRun.finalOutput);

            // Check if agent called the 'answer' tool (indicates intentional completion)
            const calledAnswerTool = Array.isArray(firstRun?.events) && firstRun.events.some((e: any) => {
              if (e.type !== 'tool_result' && e.type !== 'tool_call') return false;
              const toolName = e.data?.tool || e.data?.name || '';
              return String(toolName).toLowerCase().includes('answer');
            });

            // Check if the agent was CodeGenerationAgent (which always uses answer tool for completion)
            const wasCodeGenAgent = Array.isArray(firstRun?.events) && firstRun.events.some((e: any) =>
              e.type === 'agent_switch' &&
              (e.data?.agent === 'CodeGenerationAgent' || String(e.data?.agent || '').includes('CodeGeneration'))
            );

            // Debug: log all tool names to help diagnose detection issues
            const allToolNames = Array.isArray(firstRun?.events)
              ? firstRun.events
                  .filter((e: any) => e.type === 'tool_result' || e.type === 'tool_call')
                  .map((e: any) => e.data?.tool || e.data?.name || 'unknown')
                  .join(', ')
              : 'none';

            if (hadTools && (!producedMessage || summaryOnly) && !calledAnswerTool && !wasCodeGenAgent) {
              console.log('🔄 [Auto-Continuation] Triggering continuation: hadTools=true, producedMessage=' + producedMessage + ', summaryOnly=' + summaryOnly + ', calledAnswerTool=false, wasCodeGenAgent=false');
              console.log('🔍 [Auto-Continuation] Tools detected: ' + allToolNames);

              // Build a compact continuation message from recent tool events
              const toolLines: string[] = [];
              for (const ev of firstRun.events) {
                if (ev.type === 'tool_result') {
                  const tool = ev.data?.tool || 'tool';
                  let obs = '';
                  try {
                    const r = ev.data?.result;
                    if (typeof r === 'string') {
                      const obj = JSON.parse(r);
                      if (obj && typeof obj === 'object') {
                        const keys = Object.keys(obj).slice(0, 4).join(', ');
                        obs = keys ? `(keys: ${keys})` : '';
                      }
                    }
                  } catch {}
                  toolLines.push(`- ${tool} ${obs}`);
                }
              }
              const continuationInstruction = {
                role: 'system',
                content: `Autonomous continuation required:\nThe previous run executed tools but did not produce a final answer. Continue working until you complete the user's request or you need a specific user decision.\nGuidelines:\n- Use additional tools as needed.\n- Prefer precise, actionable answers.\n- If tools are insufficient, ask a clear follow-up question.\nRecent tools used:\n${toolLines.join('\n')}`
              } as any;

              const streamContinuation = await runDante([ ...convertedMessages, continuationInstruction ], {
                stream: true,
                model: model,
                useMCP: useMCP !== false,
                taskType: taskType as any,
                sessionId,
                userId: requestUserId,
                useMemory: true,
                forceWorker,
                onStepFinish,
                onAgentSelected,
                onDelegationStart,
                onDelegationEnd,
                onPlanCreated,
                workingDirectory: projectWorkingDir,
              });
              await enhancedHandler.handleAgentStream(streamContinuation as any); // finalize on second run
              doCleanup();
              setImmediate(() => {
                refreshConversationSummary(sessionId).catch(err => {
                  console.warn('Failed to refresh conversation summary (bg):', err);
                });
              });
            } else {
              // Finalize if first run already produced adequate content
              console.log('✅ [Auto-Continuation] Skipping continuation: hadTools=' + hadTools + ', producedMessage=' + producedMessage + ', summaryOnly=' + summaryOnly + ', calledAnswerTool=' + calledAnswerTool + ', wasCodeGenAgent=' + wasCodeGenAgent);
              console.log('🔍 [Auto-Continuation] Tools detected: ' + allToolNames);
              enhancedHandler.complete(firstRun?.finalOutput || 'Task completed');
              doCleanup();
              setImmediate(() => {
                refreshConversationSummary(sessionId).catch(err => {
                  console.warn('Failed to refresh conversation summary (bg):', err);
                });
              });
            }
          } catch (continuationError) {
            console.warn('Auto-continuation failed:', continuationError);
            enhancedHandler.complete(firstRun?.finalOutput || 'Task completed');
            doCleanup();
            setImmediate(() => {
              refreshConversationSummary(sessionId).catch(err => {
                console.warn('Failed to refresh conversation summary (bg):', err);
              });
            });
          }
        });
      } else {
        // Use regular stream handler for simple text streaming
        try {
          const stream = await runDante(convertedMessages, {
            stream: true,
            model: model,
            useMCP: useMCP !== false,
            taskType: taskType as any,
            sessionId,
            userId: requestUserId,
            useMemory: true,
            forceWorker,
            workingDirectory: projectWorkingDir,
          });

          await handleStream(stream as any, {
            onText: (text) => {
              res.write(text);
            },
            onTraceEvent: (event) => {
              // Forward trace events as SSE events for real-time UI updates
              if (wantsEvents) {
                try {
                  res.write(`data: ${JSON.stringify({ type: 'trace_event', ...event })}\n\n`);
                } catch (e) {
                  console.error('Error sending trace event:', e);
                }
              }
            },
            onComplete: (result) => {
              // Send completion data
              if (result.reasoning || result.parsedOutput) {
                res.write('\n\n---REASONING---\n');
                if (result.reasoning) {
                  res.write(result.reasoning);
                } else if (result.parsedOutput?.reasoning) {
                  res.write(result.parsedOutput.reasoning);
                }
              }
              // Emit token usage if available in the simple stream result
              try {
                if (result?.usage && wantsEvents) {
                  const input = result.usage.inputTokens ?? result.usage.input ?? result.usage.promptTokens ?? undefined;
                  const output = result.usage.outputTokens ?? result.usage.output ?? result.usage.completionTokens ?? undefined;
                  const total = result.usage.totalTokens ?? result.usage.total ?? ((input || 0) + (output || 0));
                  const payload = { type: 'token_update', timestamp: new Date().toISOString(), data: { tokens: { used: total, model: model || 'auto', isOutput: true } } };
                  res.write(`data: ${JSON.stringify(payload)}\n\n`);
                }
              } catch {}
              res.end();
              setImmediate(() => {
                refreshConversationSummary(sessionId).catch(err => {
                  console.warn('Failed to refresh conversation summary (bg):', err);
                });
              });
            },
            showProgress: false,
            captureReasoning: true,
          });
        } catch (streamErr: any) {
          // Detect rate limit and attempt a streaming fallback with a handoff note
          const msg = (streamErr?.message || String(streamErr || '')) as string;
          const lower = msg.toLowerCase();
          const looksRateLimit = lower.includes('rate limit') || lower.includes('rate_limit') || lower.includes('429') || lower.includes('tokens per') || lower.includes('tpm');
          if (looksRateLimit) {
            try {
              const continuationInstruction = {
                role: 'system',
                content: `Autonomous continuation required:\nPrevious model hit a rate limit mid-run (${msg}). Continue without repeating prior steps. Use the current filesystem state and working directory.`
              } as any;

              const fallbackModel = 'gemini-2.5-flash';
              const fallbackStream = await runDante([ ...convertedMessages, continuationInstruction ], {
                stream: true,
                model: fallbackModel,
                useMCP: useMCP !== false,
                taskType: taskType as any,
                sessionId,
                userId: requestUserId,
                useMemory: true,
                forceWorker,
                workingDirectory: projectWorkingDir,
              });

              await handleStream(fallbackStream as any, {
                onText: (text) => res.write(text),
                onTraceEvent: (event) => {
                  if (wantsEvents) {
                    try { res.write(`data: ${JSON.stringify({ type: 'trace_event', ...event })}\n\n`); } catch (e) { console.error('Error sending trace event:', e); }
                  }
                },
                onComplete: (result) => {
                  try {
                    if (result?.usage && wantsEvents) {
                      const input = result.usage.inputTokens ?? result.usage.input ?? result.usage.promptTokens ?? undefined;
                      const output = result.usage.outputTokens ?? result.usage.output ?? result.usage.completionTokens ?? undefined;
                      const total = result.usage.totalTokens ?? result.usage.total ?? ((input || 0) + (output || 0));
                      const payload = { type: 'token_update', timestamp: new Date().toISOString(), data: { tokens: { used: total, model: 'gemini-2.5-flash', isOutput: true } } };
                      res.write(`data: ${JSON.stringify(payload)}\n\n`);
                    }
                  } catch {}
                  res.end();
                },
                showProgress: false,
                captureReasoning: true,
              });
              return; // handled
            } catch (fallbackErr) {
              console.warn('Simple stream fallback run failed:', fallbackErr);
            }
          }
          // Non-rate-limit or fallback failed: surface error and end
          try {
            res.write(`data: ${JSON.stringify({ type: 'error', message: 'Stream interrupted' })}\n\n`);
            res.end();
          } catch (writeError) {
            console.error('Failed to write error to response:', writeError);
          }
          // Re-throw to be handled by outer catch if needed
          throw streamErr;
        }
      }

    } catch (agentError: any) {
      console.error('Agents SDK error, falling back to simple chat:', agentError);
      console.error('Unified agent error:', agentError);

      // Check if it's a rate limit error
      if (agentError?.code === 'rate_limit_exceeded' || agentError?.message?.includes('429')) {
        // Estimate token usage for rate limit tracking
        const estimatedTokens = convertedMessages.reduce((total, msg) => {
          const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
          return total + Math.ceil(content.length / 4); // Rough token estimation
        }, 0);

        // Update rate limit state more aggressively
        updateRateLimit(model || 'gpt-5', estimatedTokens * 2);

        // Try with emergency token limit (10k for emergency cases)
        const emergencyMaxTokens = 10000;
        console.log(`Rate limit hit, retrying with emergency token limit: ${emergencyMaxTokens}`);

        try {
          // Use a sensible emergency fallback model
          const emergencyModel = model && isGeminiModel(model) ? 'models/gemini-2.5-flash-lite' : 'gpt-5';

          const { messages: emergencyMessages } = validateAndPruneMessages(
            messages,
            emergencyModel,
            '',
            emergencyMaxTokens
          );

          // Fallback to simple chat with heavily truncated messages
          await simpleChat(emergencyMessages, res, emergencyModel);
          return;
        } catch (fallbackError) {
          console.error('Emergency fallback failed:', fallbackError);
        }
      }

      // Try fallback to simple chat for other errors
      try {
        console.log(`Attempting fallback to simple chat with model: ${model}...`);
        await simpleChat(messages, res, model);
        return;
      } catch (fallbackError) {
        console.error('Simple chat fallback failed:', fallbackError);
      }

      // Final attempt - send error response if headers not sent
      try {
        if (!res.headersSent) {
          res.status(500).json({
            error: 'Unified agent error',
            message: agentError?.message || 'Unknown error',
            fallbackAvailable: false
          });
        }
      } catch (responseError) {
        console.error('Failed to send error response:', responseError);
      }
    }
  } catch (error) {
    console.error('Chat error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Interruption endpoint: pause/cancel/steer/replace-next
app.post('/api/interruptions', (req, res) => {
  try {
    const parse = interruptionEventSchema.safeParse(req.body as InterruptionEventInput);
    if (!parse.success) {
      return res.status(400).json({ success: false, error: 'Invalid interruption event', details: parse.error.issues });
    }
    const evt = parse.data;
    const status = interruptBus.handle(evt);
    // Send immediate ack and status
    console.log(`[INTERRUPTION] type=${evt.type} session=${evt.sessionId} step=${evt.stepId || ''} status=${status}`);
    // Best-effort: notify the active stream handler for this session
    const handler = sessionStreams.get(evt.sessionId);
    if (handler) {
      try {
        // Emit a trace event so UI reflects status in real time
        (handler as any).heartbeat?.(`interruption_${evt.type}_${status}`);
      } catch {}
      if (evt.type === 'cancel') {
        try { handler.quickComplete('Cancelled'); } catch {}
      }
      if (evt.type === 'pause') {
        try { handler.quickComplete('Paused'); } catch {}
      }
      if (evt.type === 'steer') {
        try { handler.quickComplete('Update applied'); } catch {}
      }
      if (evt.type === 'replace-next') {
        try { handler.quickComplete('Next step replaced'); } catch {}
      }
    }
    return res.json({ success: true, status });
  } catch (e) {
    console.error('Interruption endpoint error:', e);
    return res.status(500).json({ success: false, error: 'Failed to handle interruption' });
  }
});

// Endpoint to request cancellation of the current run
app.post('/api/run/cancel', (req, res) => {
  try {
    const { sessionId, agent } = req.body || {};
    if (!sessionId) {
      return res.status(400).json({ error: 'sessionId is required' });
    }
    const { cancelBus } = require('../utils/cancelBus');
    cancelBus.requestCancel(sessionId, agent);
    res.json({ success: true, cancelled: { sessionId, agent } });
  } catch (e) {
    console.error('Cancel endpoint error:', e);
    res.status(500).json({ error: 'Failed to cancel' });
  }
});

// Interruption clear endpoint (undo steering/replace-next)
app.post('/api/interruptions/clear', (req, res) => {
  try {
    const { sessionId } = req.body || {};
    if (!sessionId) return res.status(400).json({ success: false, error: 'sessionId is required' });
    interruptBus.clear(sessionId);
    return res.json({ success: true, status: 'cleared' });
  } catch (e) {
    console.error('Interruption clear error:', e);
    return res.status(500).json({ success: false, error: 'Failed to clear interruptions' });
  }
});

// Interruption resume endpoint
app.post('/api/interruptions/resume', (req, res) => {
  try {
    const { sessionId } = req.body || {};
    if (!sessionId) return res.status(400).json({ success: false, error: 'sessionId is required' });
    interruptBus.resume(sessionId);
    return res.json({ success: true, status: 'resumed' });
  } catch (e) {
    console.error('Interruption resume error:', e);
    return res.status(500).json({ success: false, error: 'Failed to resume' });
  }
});

// Interruption status endpoint
app.get('/api/interruptions/status', (req, res) => {
  try {
    const sessionId = String(req.query.sessionId || '');
    if (!sessionId) return res.status(400).json({ success: false, error: 'sessionId is required' });
    const rec = interruptBus.getRecord(sessionId);
    if (!rec) return res.json({ success: true, status: 'unknown' });
    return res.json({ success: true, status: rec.status, lastAppliedPayload: rec.lastAppliedPayload, history: rec.history });
  } catch (e) {
    console.error('Interruption status error:', e);
    return res.status(500).json({ success: false, error: 'Failed to get status' });
  }
});

// Initialize project feeder with current working directory
const projectRoot = process.cwd();
initializeProjectFeeder(projectRoot);
const projectFeeder = new ContextualProjectFeeder(projectRoot);
rateLimitProtection.setProjectFeeder(projectFeeder);
console.log(`Initialized contextual project feeder for: ${projectRoot}`);

// Initialize recovery system
recoverySystem.start().catch((error: any) => {
  console.error('Failed to initialize recovery system:', error);
});

// Initialize reminder system with Vercel AI SDK
import('../agents/ReminderAgent').then(({ reminderAgent: _reminderAgent }) => {
  console.log('✅ Reminder Agent restored and initialized');
}).catch(error => {
  console.error('Failed to initialize Reminder Agent:', error);
});

// Unified orchestrator already initialized via DanteOrchestrator

// Initialize planning agent with Vercel AI SDK
import('../agents/PlanningAgent').then(({ planningAgent: _planningAgent }) => {
  console.log('✅ Planning Agent restored and initialized');
}).catch(error => {
  console.error('Failed to initialize Planning Agent:', error);
});

// Initialize worker agents with Vercel AI SDK
import('../agents/workers').then(({ chunkProcessorWorker: _chunkProcessorWorker, fileAnalysisWorker: _fileAnalysisWorker, synthesisWorker: _synthesisWorker }) => {
  console.log('✅ Worker Agents restored and initialized');
  console.log('  - Chunk Processor Worker: Ready');
  console.log('  - File Analysis Worker: Ready');
  console.log('  - Synthesis Worker: Ready');
}).catch(error => {
  console.error('Failed to initialize Worker Agents:', error);
});

// Initialize OAuth connections
void (async () => {
  try {
    const { connectorService } = await import('../services/connectorService');
    await connectorService.initializeConnections();
  } catch (error) {
    console.error('Failed to initialize OAuth connections:', error);
  }
})();

// Lightweight, non-blocking memory subsystem initialization
void (async () => {
  try {
    console.log('🧠 Initializing memory subsystem...');
    await memoryManager.initialize();
    const vs: any = memoryManager.getVectorStore();
    if (vs && typeof vs.getStats === 'function') {
      try {
        const vstats = await vs.getStats();
        const collections = Array.isArray(vstats?.collections) ? vstats.collections.length : undefined;
        console.log(`🧠 Vector store ready: ${vstats?.totalVectors ?? 0} vectors${collections != null ? ` across ${collections} collections` : ''}`);
      } catch (e) {
        console.warn('Vector store stats unavailable:', e);
      }
    }
  } catch (e) {
    console.warn('Memory subsystem initialization failed:', e);
  }
})();

const HOST = process.env.HOST || '0.0.0.0';

async function startServer(): Promise<void> {
  return new Promise((resolve, reject) => {
    const server = app.listen(PORT, HOST, () => {
      console.log(`Dante API server running on http://${HOST}:${PORT}`);
      console.log(`Health check: http://${HOST}:${PORT}/api/health`);

      // Show network-accessible URL if not localhost
      if (HOST === '0.0.0.0') {
        const os = require('os');
        const networkInterfaces = os.networkInterfaces();
        let localIP = 'localhost';

        // Find the first non-internal IPv4 address
        for (const interfaceName in networkInterfaces) {
          const interfaces = networkInterfaces[interfaceName];
          if (interfaces) {
            for (const iface of interfaces) {
              if (iface.family === 'IPv4' && !iface.internal) {
                localIP = iface.address;
                break;
              }
            }
          }
          if (localIP !== 'localhost') break;
        }

        if (localIP !== 'localhost') {
          console.log(`Network accessible at: http://${localIP}:${PORT}`);
          console.log(`Share this URL with devices on your private network!`);
        }
      }

      console.log(`Contextual project feeder active for intelligent context management`);
      resolve();
    });

    server.on('error', (err: any) => {
      console.error('Server failed to start:', err);
      reject(err);
    });
  });
}

// Ensure DB schema is ready before serving
void (async () => {
  try {
    await runMigrations();
    await startServer();
  } catch (err) {
    console.error('Failed to run DB migrations. Exiting.', err);
    process.exit(1);
  }
})();
