import Database from 'better-sqlite3';
import * as fs from 'fs';
import * as path from 'path';

// Define the path to the SQLite database file
const DB_PATH = path.join(__dirname, '../../data/briefing.db');
const JSON_DB_PATH = path.join(__dirname, '../../data/db.json'); // Assuming this is where the old JSON DB is

let db: Database.Database;

interface Item {
    id: string;
    title: string;
    content: string;
    // Add other fields from your existing Item structure
}

function initializeDatabase() {
    if (!db) {
        // Ensure the directory exists
        const dbDir = path.dirname(DB_PATH);
        if (!fs.existsSync(dbDir)) {
            fs.mkdirSync(dbDir, { recursive: true });
        }
        db = new Database(DB_PATH);
        db.pragma('journal_mode = WAL'); // Recommended for better performance and concurrency

        // Create the items table if it doesn't exist
        db.exec(`
            CREATE TABLE IF NOT EXISTS items (
                id TEXT PRIMARY KEY,
                title TEXT,
                content TEXT
                -- Add other columns as per your Item interface
            );
        `);
    }
}

export function getDbInstance(): Database.Database {
    initializeDatabase();
    return db;
}

export function upsertItem(item: Item): boolean {
    const db = getDbInstance();
    const stmt = db.prepare(`
        INSERT INTO items (id, title, content)
        VALUES (?, ?, ?)
        ON CONFLICT(id) DO UPDATE SET
            title = EXCLUDED.title,
            content = EXCLUDED.content;
    `);
    const info = stmt.run(item.id, item.title, item.content);
    return info.changes > 0; // true if inserted/updated
}

export function migrateJsonToSqlite() {
    initializeDatabase();
    const db = getDbInstance();

    if (!fs.existsSync(JSON_DB_PATH)) {
        console.log(`No JSON database found at ${JSON_DB_PATH}. Skipping migration.`);
        return;
    }

    try {
        const jsonData = fs.readFileSync(JSON_DB_PATH, 'utf-8');
        const oldDbData = JSON.parse(jsonData);
        const itemsToMigrate: Item[] = oldDbData.items || [];

        if (itemsToMigrate.length === 0) {
            console.log('No items to migrate from JSON database.');
            return;
        }

        const insertMany = db.transaction((items) => {
            for (const item of items) {
                upsertItem(item);
            }
        });

        insertMany(itemsToMigrate);
        console.log(`Migrated ${itemsToMigrate.length} items from JSON to SQLite.`);

    } catch (error) {
        console.error('Error during JSON to SQLite migration:', error);
    }
}

// Optional: call migration on startup
// migrateJsonToSqlite();
