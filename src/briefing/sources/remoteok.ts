export interface RemoteOkMeta {
  // RemoteOK returns a metadata object as the first element sometimes
  legal?: string;
  ok?: boolean;
  // Other unknown fields may be present
  [key: string]: unknown;
}

export interface RemoteOkJob {
  id: number;
  date?: string;
  company?: string;
  company_logo?: string;
  position?: string; // Some rows use "position"
  title?: string; // Others use "title"
  description?: string;
  url: string;
  location?: string;
  tags?: string[];
  salary?: string;
  apply_url?: string;
  // Other unknown fields may be present on jobs as well
  [key: string]: unknown;
}

export type RemoteOkApiResponse = Array<RemoteOkMeta | RemoteOkJob>;

const REMOTEOK_API_URL = "https://remoteok.com/api";
const DEFAULT_TIMEOUT_MS = 8000; // 8 seconds

function isNonEmptyString(v: unknown): v is string {
  return typeof v === "string" && v.trim().length > 0;
}

function isStringArray(v: unknown): v is string[] {
  return Array.isArray(v) && v.every((x) => typeof x === "string");
}

function isRemoteOkJob(row: unknown): row is RemoteOkJob {
  if (!row || typeof row !== "object") return false;
  const o = row as Record<string, unknown>;
  // Heuristic for job-like rows
  const hasId = typeof o.id === "number" && Number.isFinite(o.id);
  const hasUrl = isNonEmptyString(o.url);
  const hasTitleOrPosition = isNonEmptyString(o.title) || isNonEmptyString(o.position);
  return Boolean(hasId && hasUrl && hasTitleOrPosition);
}

function includesKeyword(job: RemoteOkJob, keyword: string): boolean {
  const needle = keyword.toLowerCase();
  const haystack = [
    job.position,
    job.title,
    job.description,
    job.company,
    job.location,
    job.salary,
  ]
    .filter(Boolean)
    .join(" ")
    .toLowerCase();

  const tagText = (isStringArray(job.tags) ? job.tags : []).join(" ").toLowerCase();

  return haystack.includes(needle) || tagText.includes(needle);
}

/**
 * Fetch and filter jobs from RemoteOK API.
 * - Applies an 8s timeout using AbortController
 * - Requests JSON via Accept header
 * - Filters to job-like rows (must have numeric id, url, and title/position)
 * - Applies keyword relevance filtering (matches title/position/description/company/location/tags)
 * - Logs warnings and returns [] on any failures
 *
 * Example:
 *   const jobs = await fetchRemoteOk("flutter");
 *   console.log(jobs.length);
 */
export async function fetchRemoteOk(keyword = "flutter"): Promise<RemoteOkJob[]> {
  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), DEFAULT_TIMEOUT_MS);

  try {
    const res = await fetch(REMOTEOK_API_URL, {
      headers: {
        "User-Agent": "DanteGPT-Briefing/1.0",
        Accept: "application/json",
      },
      signal: controller.signal,
    });

    if (!res.ok) {
      console.warn(`[RemoteOK] HTTP ${res.status} when fetching ${REMOTEOK_API_URL}`);
      return [];
    }

    let data: unknown;
    try {
      data = await res.json();
    } catch (e) {
      console.warn(`[RemoteOK] Failed to parse JSON: ${(e as Error).message}`);
      return [];
    }

    if (!Array.isArray(data)) {
      console.warn("[RemoteOK] Unexpected response shape (not an array)");
      return [];
    }

    // Filter to job-like rows and then by keyword relevance
    const jobs = (data as RemoteOkApiResponse)
      .filter(isRemoteOkJob)
      .filter((job) => includesKeyword(job, keyword));

    return jobs;
  } catch (err) {
    if ((err as any)?.name === "AbortError") {
      console.warn(`[RemoteOK] Request timed out after ${DEFAULT_TIMEOUT_MS}ms`);
    } else {
      console.warn(`[RemoteOK] Fetch failed: ${(err as Error).message}`);
    }
    return [];
  } finally {
    clearTimeout(timeout);
  }
}
