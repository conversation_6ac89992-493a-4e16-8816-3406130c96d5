import Parser from "rss-parser";

const UA = process.env.BRIEFING_UA || "DanteGPT-Briefing/1.0 (+https://example.com)";
const parser = new Parser({ headers: { "User-Agent": UA } });

async function fetchWithTimeout(url: string, timeout = 8000, signal?: AbortSignal) {
  const controller = new AbortController();
  const combinedSignal = signal
    ? (() => {
        // combine signals: if either aborts, abort
        const ac = new AbortController();
        signal.addEventListener("abort", () => ac.abort());
        controller.signal.addEventListener("abort", () => ac.abort());
        return ac.signal;
      })()
    : controller.signal;
  const id = setTimeout(() => controller.abort(), timeout);
  try {
    const res = await fetch(url, { headers: { "User-Agent": UA, Accept: "application/rss+xml, application/xml, text/xml" }, signal: combinedSignal });
    clearTimeout(id);
    return res;
  } finally {
    clearTimeout(id);
  }
}

export async function fetchRss(url: string) {
  const attempts = 2;
  const timeout = Number(process.env.BRIEFING_RSS_TIMEOUT_MS || 8000);

  for (let attempt = 0; attempt < attempts; attempt++) {
    try {
      const res = await fetchWithTimeout(url, timeout);
      if (!res.ok) throw new Error(`HTTP ${res.status} for ${url}`);
      const text = await res.text();
      const feed = await parser.parseString(text);
      return feed;
    } catch (err: any) {
      console.warn(`fetchRss attempt ${attempt + 1} failed for ${url}: ${err?.message || err}`);
      if (attempt < attempts - 1) {
        const backoff = 300 * Math.pow(2, attempt);
        await new Promise((r) => setTimeout(r, backoff));
        continue;
      }
      // return a safe empty feed shape expected by downstream code
      return { items: [] } as any;
    }
  }
  return { items: [] } as any;
}
