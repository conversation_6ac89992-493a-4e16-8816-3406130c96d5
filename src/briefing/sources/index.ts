import { Source<PERSON>ey, Section, Item } from "../types";
import { fetchRss } from "./rss";
import { fetchGitHubReleases } from "./githubAtom";
import { fetchFilteredRedditRss } from "./redditRss";
import { fetchRemoteOk } from "./remoteok";
import { normalizeRssItems, normalizeRemoteOk } from "../normalize";

// Normalize URLs for deduplication across sources
// - Lowercase hostname
// - Remove fragments and tracking query params (utm_*, gclid, fbclid, etc.)
// - Remove default ports
// - Trim trailing slashes from non-root paths
function normalizeUrlForDedup(raw: string): string {
  try {
    const u = new URL(raw);
    u.hash = "";
    u.username = "";
    u.password = "";

    // canonicalize hostname casing
    u.hostname = u.hostname.toLowerCase();

    // strip default ports
    if ((u.protocol === "http:" && u.port === "80") || (u.protocol === "https:" && u.port === "443")) {
      u.port = "";
    }

    // remove tracking params
    const removeKeys = [
      "gclid",
      "fbclid",
      "igshid",
      "mc_cid",
      "mc_eid",
      "ref",
      "ref_src",
      "referrer",
      "source",
    ];
    const keysToDelete: string[] = [];
    u.searchParams.forEach((_, key) => {
      const lower = key.toLowerCase();
      if (lower.startsWith("utm_") || removeKeys.includes(lower)) {
        keysToDelete.push(key);
      }
    });
    for (const key of keysToDelete) {
      u.searchParams.delete(key);
    }

    // remove trailing slash for non-root paths
    if (u.pathname !== "/") {
      u.pathname = u.pathname.replace(/\/+$/g, "");
    }

    return u.toString();
  } catch {
    return (raw || "").trim();
  }
}

export async function loadAllSources(): Promise<Item[]> {
  const [
    flutterBlogRes,
    dartBlogRes,
    flutterRelRes,
    dartRelRes,
    pubDevRes,
    redditJobsRes,
    remoteOkRes,
  ] = await Promise.allSettled([
    fetchRss("https://medium.com/feed/flutter"),
    fetchRss("https://medium.com/feed/dartlang"),
    fetchGitHubReleases("https://github.com/flutter/flutter/releases.atom"),
    fetchGitHubReleases("https://github.com/dart-lang/sdk/releases.atom"),
    fetchRss("https://pub.dev/feed.atom"),
    fetchFilteredRedditRss(
      "https://www.reddit.com/r/flutterdev/.rss",
      ["hiring", "job", "contract", "remote"]
    ),
    fetchRemoteOk("flutter"),
  ]);

  // Gracefully handle individual source failures without aborting ingestion
  const flutterBlog =
    flutterBlogRes.status === "fulfilled"
      ? flutterBlogRes.value
      : (console.warn(
          "[sources] Failed to fetch Flutter Blog RSS:",
          (flutterBlogRes as PromiseRejectedResult).reason
        ),
        []);

  const dartBlog =
    dartBlogRes.status === "fulfilled"
      ? dartBlogRes.value
      : (console.warn(
          "[sources] Failed to fetch Dart Blog RSS:",
          (dartBlogRes as PromiseRejectedResult).reason
        ),
        []);

  const flutterRel =
    flutterRelRes.status === "fulfilled"
      ? flutterRelRes.value
      : (console.warn(
          "[sources] Failed to fetch Flutter Releases (GitHub Atom):",
          (flutterRelRes as PromiseRejectedResult).reason
        ),
        []);

  const dartRel =
    dartRelRes.status === "fulfilled"
      ? dartRelRes.value
      : (console.warn(
          "[sources] Failed to fetch Dart Releases (GitHub Atom):",
          (dartRelRes as PromiseRejectedResult).reason
        ),
        []);

  const pubDev =
    pubDevRes.status === "fulfilled"
      ? pubDevRes.value
      : (console.warn(
          "[sources] Failed to fetch pub.dev feed:",
          (pubDevRes as PromiseRejectedResult).reason
        ),
        []);

  const redditJobs =
    redditJobsRes.status === "fulfilled"
      ? redditJobsRes.value
      : (console.warn(
          "[sources] Failed to fetch r/flutterdev jobs RSS:",
          (redditJobsRes as PromiseRejectedResult).reason
        ),
        []);

  const remoteOk =
    remoteOkRes.status === "fulfilled"
      ? remoteOkRes.value
      : (console.warn(
          "[sources] Failed to fetch RemoteOK jobs:",
          (remoteOkRes as PromiseRejectedResult).reason
        ),
        []);

  const items: Item[] = [
    ...normalizeRssItems(
      flutterBlog,
      SourceKey.FlutterBlog,
      Section.Headlines
    ),
    ...normalizeRssItems(dartBlog, SourceKey.DartBlog, Section.Headlines),
    ...normalizeRssItems(
      flutterRel,
      SourceKey.FlutterReleases,
      Section.Releases
    ),
    ...normalizeRssItems(dartRel, SourceKey.DartReleases, Section.Releases),
    ...normalizeRssItems(pubDev, SourceKey.PubDev, Section.Ecosystem),
    ...normalizeRssItems(
      redditJobs,
      SourceKey.RedditFlutterDev,
      Section.Jobs
    ),
    ...normalizeRemoteOk(remoteOk),
  ];

  // Basic sanity filter
  const sane = items.filter((i) => i.id && i.url && i.title);

  // In-batch de-duplication by normalized URL: keep the newest by publishedAt
  const latestByUrl = new Map<string, Item>();
  for (const it of sane) {
    const key = normalizeUrlForDedup(it.url);
    const existing = latestByUrl.get(key);
    if (!existing || new Date(it.publishedAt).getTime() > new Date(existing.publishedAt).getTime()) {
      latestByUrl.set(key, it);
    }
  }

  // Sort by publishedAt desc
  const deduped = Array.from(latestByUrl.values()).sort(
    (a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
  );

  return deduped;
}
