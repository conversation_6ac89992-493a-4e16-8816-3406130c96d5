import { Database } from "bun:sqlite";
import path from "node:path";
import fs from "node:fs";
import { URL } from "node:url";
import { Item } from "./types";

const DB_PATH = process.env.BRIEFING_DB_PATH || path.join(process.cwd(), "data", "briefing.sqlite");

let dbInstance: Database | null = null;

export function ensureDb(): Database {
  if (dbInstance) {
    return dbInstance;
  }
  fs.mkdirSync(path.dirname(DB_PATH), { recursive: true });
  const db = new Database(DB_PATH);
  dbInstance = db;

  // Initialize schema and prepared statements so consumers can call functions
  // without needing to call initDb/initializeStatements explicitly.
  try {
    initDb(db);
  } catch (err) {
    console.error('[briefing] Failed to initialize DB schema/statements in ensureDb:', err);
    throw err;
  }

  return db;
}

// Normalize a URL string for deduplication and consistency.
// Rules:
// - Trim leading/trailing whitespace
// - Collapse internal whitespace (including newlines/tabs) to a single space
// - Lowercase scheme (protocol) and hostname only
// - Remove a single trailing slash from the path, unless the path is just "/"
export function normalizeUrl(input: string): string {
  if (typeof input !== "string") return "";

  // Trim and collapse any internal whitespace to a single space
  let s = input.trim().replace(/\s+/g, " ");
  if (s.length === 0) return s;

  try {
    // The WHATWG URL will percent-encode spaces where appropriate
    const u = new URL(s);

    // Lowercase scheme/protocol and hostname
    u.protocol = u.protocol.toLowerCase();
    u.hostname = u.hostname.toLowerCase();

    // Remove exactly one trailing slash from pathname unless root
    if (u.pathname.length > 1 && u.pathname.endsWith("/")) {
      u.pathname = u.pathname.slice(0, -1);
    }

    return u.toString();
  } catch {
    // Fallback: attempt partial normalization without full URL parsing
    // Lowercase scheme and host if a scheme delimiter is present
    const maybeUrl = s.length > 1 && s.endsWith("/") ? s.slice(0, -1) : s;
    const m = maybeUrl.match(/^([A-Za-z][A-Za-z0-9+.-]*):\/\/([^\/#?\s]+)([\s\S]*)$/);
    if (m) {
      const scheme = m[1].toLowerCase();
      const host = m[2].toLowerCase();
      const rest = m[3] || "";
      return `${scheme}://${host}${rest}`;
    }
    return maybeUrl;
  }
}

export function initDb(db: Database) {
  db.run(`
    CREATE TABLE IF NOT EXISTS items (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      url TEXT NOT NULL,
      source TEXT NOT NULL,
      section TEXT NOT NULL,
      publishedAt INTEGER NOT NULL,
      summary TEXT,
      meta TEXT
    );
  `);
  db.run(`
    CREATE TABLE IF NOT EXISTS runs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      startedAt INTEGER NOT NULL,
      finishedAt INTEGER,
      fetched INTEGER DEFAULT 0,
      validated INTEGER DEFAULT 0,
      inserted INTEGER DEFAULT 0,
      validationFailed INTEGER DEFAULT 0,
      upsertFailed INTEGER DEFAULT 0
    );
  `);
  // Ensure required indexes exist for performance and data integrity
  db.run('CREATE INDEX IF NOT EXISTS idx_items_publishedAt ON items (publishedAt DESC)');

  // Deduplicate rows by URL before creating a unique index on url.
  // This removes any rows with duplicate url values while keeping the first row per url (by rowid).
  try {
    const dupRow = db.query<any, []>(
      "SELECT COUNT(*) as count FROM items WHERE rowid NOT IN (SELECT MIN(rowid) FROM items GROUP BY url)"
    ).get();
    const duplicates = dupRow && typeof dupRow.count === 'number' ? dupRow.count : Number(dupRow?.count ?? 0);

    if (duplicates > 0) {
      // Delete duplicate rows, keep the row with the smallest rowid for each url
      db.run("DELETE FROM items WHERE rowid NOT IN (SELECT MIN(rowid) FROM items GROUP BY url)");
      console.log(`[briefing] Deduplicated items by url; deleted ${duplicates} duplicate row(s).`);
    } else {
      console.log('[briefing] No duplicate items by url found.');
    }
  } catch (err) {
    // If anything goes wrong during deduplication, surface a helpful message but continue to attempt index creation.
    console.error('[briefing] Error during deduplication:', err);
  }

  // Create unique index on url after deduplication to enforce uniqueness going forward
  db.run('CREATE UNIQUE INDEX IF NOT EXISTS idx_items_url ON items (url)');

  // Prepare statements for consumers; safe to call multiple times
  try {
    initializeStatements(db);
  } catch (err) {
    console.error('[briefing] Failed to initialize prepared statements:', err);
    throw err;
  }
}

let _upsertItemStmt: ReturnType<Database['prepare']> | null = null;
let _getItemByIdQuery: ReturnType<Database['query']> | null = null;
let _getItemByUrlQuery: ReturnType<Database['query']> | null = null;
let _listItemsSinceQuery: ReturnType<Database['query']> | null = null;
let _startRunStmt: ReturnType<Database['prepare']> | null = null;
let _finishRunStmt: ReturnType<Database['prepare']> | null = null;

export function initializeStatements(db: Database) {
  _upsertItemStmt = db.prepare(`
    INSERT INTO items (id, title, url, source, section, publishedAt, summary, meta)
    VALUES ($id, $title, $url, $source, $section, $publishedAt, $summary, $meta)
    ON CONFLICT(url) DO UPDATE SET
      title=excluded.title,
      source=excluded.source,
      section=excluded.section,
      publishedAt=excluded.publishedAt,
      summary=excluded.summary,
      meta=excluded.meta
    WHERE
      excluded.publishedAt > items.publishedAt
      OR (
        excluded.publishedAt = items.publishedAt AND (
          excluded.title <> items.title OR
          excluded.source <> items.source OR
          excluded.section <> items.section OR
          COALESCE(excluded.summary, '') <> COALESCE(items.summary, '') OR
          COALESCE(excluded.meta, '') <> COALESCE(items.meta, '')
        )
      )
  `);
  _getItemByIdQuery = db.query<{ id: string }, []>("SELECT id FROM items WHERE id = $id");
  _listItemsSinceQuery = db.query<Omit<Item, 'publishedAt' | 'meta'> & { publishedAt: number; meta: string | null }, [number]>(
    `SELECT * FROM items WHERE publishedAt >= $sinceMs ORDER BY publishedAt DESC`
  );
  _startRunStmt = db.prepare(`INSERT INTO runs (startedAt) VALUES ($startedAt)`);
  _finishRunStmt = db.prepare(`
    UPDATE runs SET finishedAt=$finishedAt, fetched=$fetched, validated=$validated,
    inserted=$inserted, validationFailed=$validationFailed, upsertFailed=$upsertFailed
    WHERE id=$id
  `);
}

export function upsertItem(db: Database, item: Item): { inserted: boolean; updated: boolean } {
  const result = bulkUpsertItems(db, [item]);
  return { inserted: result.inserted > 0, updated: result.updated > 0 };
}

export function bulkUpsertItems(db: Database, items: Item[]): { inserted: number; updated: number } {
  if (!_upsertItemStmt || !_getItemByIdQuery) throw new Error("Database statements not initialized. Call initializeStatements(db) first.");

  let inserted = 0;
  let updated = 0;

  const transaction = db.transaction((itemsToUpsert: Item[]) => {
    for (const item of itemsToUpsert) {
      const existing = _getItemByIdQuery!.get({
        $id: item.id,
      });
      const res = _upsertItemStmt!.run({
        $id: item.id,
        $title: item.title,
        $url: item.url,
        $source: item.source,
        $section: item.section,
        $publishedAt: item.publishedAt.getTime(),
        $summary: item.summary || null,
        $meta: item.meta ? JSON.stringify(item.meta) : null,
      });
      const itemInserted = res.changes > 0 && !existing;
      const itemUpdated = res.changes > 0 && !!existing;
      if (itemInserted) {
        inserted++;
      }
      if (itemUpdated) {
        updated++;
      }
    }
  });

  transaction(items);
  return { inserted, updated };
}

export function listItemsSince(dbOrSinceMs: Database | number, maybeSinceMs?: number): Item[] {
  // Support two calling conventions:
  //  - listItemsSince(db, sinceMs)
  //  - listItemsSince(sinceMs)  -> will use ensureDb()
  let db: Database;
  let sinceMs: number;
  if (typeof dbOrSinceMs === 'number') {
    db = ensureDb();
    sinceMs = dbOrSinceMs;
  } else {
    db = dbOrSinceMs;
    sinceMs = maybeSinceMs as number;
  }

  if (!_listItemsSinceQuery) throw new Error("Database statements not initialized. Call initializeStatements(db) first.");

  const rows = _listItemsSinceQuery.all({ $sinceMs: sinceMs }) as Array<Omit<Item, 'publishedAt' | 'meta'> & { publishedAt: number; meta: string | null }>;

  return rows.map(r => ({
    id: r.id,
    title: r.title,
    url: r.url,
    source: r.source,
    section: r.section,
    publishedAt: new Date(r.publishedAt),
    summary: r.summary || undefined,
    meta: r.meta ? JSON.parse(r.meta) : undefined,
  }));
}

export function startRun(db: Database): number {
  if (!_startRunStmt) throw new Error("Database statements not initialized. Call initializeStatements(db) first.");

  const res = _startRunStmt.run({ $startedAt: Date.now() });
  return res.lastInsertRowid as number;
}

export function finishRun(
  db: Database,
  runId: number,
  stats: { fetched: number; validated: number; inserted: number; validationFailed: number; upsertFailed: number }
) {
  if (!_finishRunStmt) throw new Error("Database statements not initialized. Call initializeStatements(db) first.");

  _finishRunStmt.run({
    $finishedAt: Date.now(),
    $fetched: stats.fetched,
    $validated: stats.validated,
    $inserted: stats.inserted,
    $validationFailed: stats.validationFailed,
    $upsertFailed: stats.upsertFailed,
    $id: runId,
  });
}
