const css = `
  body { font-family: -apple-system, <PERSON><PERSON><PERSON>, Robot<PERSON>, Aria<PERSON>, sans-serif; color: #111; line-height: 1.5; }
  h1 { font-size: 24px; margin: 0 0 16px; color: #111; }
  h2 { font-size: 18px; margin: 24px 0 10px; color: #111; border-bottom: 1px solid #eee; padding-bottom: 5px; }
  a { color: #0b63f6; text-decoration: none; }
  a:hover { text-decoration: underline; }
  .section { margin-bottom: 20px; }
  .item { margin-bottom: 10px; }
  .item-title { font-weight: 600; }
  .item-meta { color: #555; font-size: 13px; margin-top: 2px; }
  hr { border: 0; border-top: 1px solid #eee; margin: 24px 0; }
  .footer { font-size: 12px; color: #888; text-align: center; margin-top: 30px; }
  .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background-color: #fff; }
  .header { text-align: center; margin-bottom: 20px; }
  .logo { max-width: 150px; height: auto; }
  ul { padding-left: 20px; margin-top: 5px; }
  li { margin-bottom: 5px; }
`;
export default css;
