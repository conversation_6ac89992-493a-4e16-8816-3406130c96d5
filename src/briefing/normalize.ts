import { Item, Section, SourceKey } from "./types";

// Parse multiple date candidates safely with validation and sane fallbacks
export function safeDate(...candidates: Array<string | number | Date | null | undefined>): Date {
  const isValid = (d: Date) => d instanceof Date && !Number.isNaN(d.getTime()) && d.getFullYear() > 1990 && d.getFullYear() < 2100;

  for (const raw of candidates) {
    if (raw === null || raw === undefined) continue;

    // Already a Date
    if (raw instanceof Date) {
      if (isValid(raw)) return raw;
      continue;
    }

    // Numbers: epoch seconds or milliseconds
    if (typeof raw === "number") {
      const ms = raw < 1e12 ? raw * 1000 : raw; // treat small as seconds
      const d = new Date(ms);
      if (isValid(d)) return d;
      continue;
    }

    // Strings: try numeric, then Date.parse
    if (typeof raw === "string") {
      const s = raw.trim();
      if (!s) continue;

      // epoch string
      if (/^\d+$/.test(s)) {
        const n = Number(s);
        const ms = n < 1e12 ? n * 1000 : n;
        const d = new Date(ms);
        if (isValid(d)) return d;
      }

      const d = new Date(s);
      if (isValid(d)) return d;
      continue;
    }
  }

  // fallback to now if everything fails
  return new Date();
}

function firstTruthyString(...vals: any[]): string | undefined {
  for (const v of vals) {
    if (typeof v === "string" && v.trim()) return v.trim();
  }
  return undefined;
}

function ensureHttpUrl(candidate?: any, fallback?: string): string | undefined {
  if (candidate) {
    try {
      const u = new URL(String(candidate));
      if (u.protocol === "http:" || u.protocol === "https:") return u.toString();
    } catch {
      // ignore
    }
  }
  if (fallback) {
    try {
      const u = new URL(String(fallback));
      if (u.protocol === "http:" || u.protocol === "https:") return u.toString();
    } catch {
      // ignore
    }
  }
  return undefined;
}

function deriveUrlFromRssItem(i: any): string {
  // Try common fields and structures
  const candidates: any[] = [
    i.link,
    i.link?.href,
    i.id,
    i.guid,
    i.url,
    i.enclosure?.url,
  ];
  for (const c of candidates) {
    const u = ensureHttpUrl(c);
    if (u) return u;
  }
  // last resort
  return "https://example.com/";
}

function deriveIdFallback(source: SourceKey, section: Section, title: string, url: string, publishedAt: Date): string {
  // Compose a stable, somewhat unique fallback ID without external libs
  const base = `${source}|${section}|${title}|${url}|${publishedAt.getTime()}`;
  // simple 32-bit hash
  let hash = 0;
  for (let i = 0; i < base.length; i++) {
    hash = (hash << 5) - hash + base.charCodeAt(i);
    hash |= 0; // to 32-bit int
  }
  return `${source}:${section}:${Math.abs(hash)}`;
}

export function normalizeRssItems(feed: any, source: SourceKey, section: Section): Item[] {
  return (feed.items || []).map((i: any) => {
    const url = deriveUrlFromRssItem(i);
    const publishedAt = safeDate(
      i.isoDate,
      i.pubDate,
      i.pubdate,
      i.published,
      i.updated,
      i.date
    );

    const id = firstTruthyString(i.guid, i.id, url) || deriveIdFallback(source, section, i.title || "(untitled)", url, publishedAt);

    return {
      id,
      title: i.title || "(untitled)",
      url,
      source,
      section,
      publishedAt,
      summary: i.contentSnippet || i.content || undefined,
    } as Item;
  });
}

function deriveRemoteOkUrl(r: any): string {
  // Prefer provided URLs first
  const direct = ensureHttpUrl(r.url) || ensureHttpUrl(r.original);
  if (direct) return direct;

  // Construct from slug if available
  const slug = firstTruthyString(r.slug, r.id?.toString?.());
  if (slug) {
    const constructed = `https://remoteok.com/remote-jobs/${slug}`;
    const u = ensureHttpUrl(constructed);
    if (u) return u;
  }

  return "https://remoteok.com/";
}

export function normalizeRemoteOk(rows: any[]): Item[] {
  return rows.map((r: any) => {
    const url = deriveRemoteOkUrl(r);
    const publishedAt = safeDate(r.date, r.epoch, r.timestamp, r.created_at, r.published_at);

    // Robust ID derivation with sensible fallbacks
    const primaryId = firstTruthyString(
      r.id?.toString?.(),
      r.slug,
      r.url,
      r.original
    );
    const id = primaryId || deriveIdFallback(SourceKey.RemoteOK, Section.Jobs, r.position || r.title || "Job", url, publishedAt);

    // Build summary without trailing dashes; include tags only if they exist
    const company = firstTruthyString(r.company);
    const tagsArr = Array.isArray(r.tags) ? r.tags.filter(Boolean) : [];
    const tagsStr = tagsArr.length ? tagsArr.join(", ") : "";
    const summary = company
      ? (tagsStr ? `${company} – ${tagsStr}` : company)
      : (tagsStr || undefined);

    return {
      id,
      title: r.position || r.title || "Job",
      url,
      source: SourceKey.RemoteOK,
      section: Section.Jobs,
      publishedAt,
      summary,
      meta: { company: r.company, tags: r.tags },
    } as Item;
  });
}
