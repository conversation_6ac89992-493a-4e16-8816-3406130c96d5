/**
 * System Status Dashboard Tools
 * Provides comprehensive system status monitoring and reporting
 */

import { tool } from 'ai';
import { z } from 'zod';
import * as YAML from 'yaml';
import { systemHealthMonitor } from '@/services/SystemHealthMonitor';
import { serviceRecovery } from '@/recovery/ServiceRecovery';
import { errorRecoveryMiddleware } from '@utils/errorRecoveryMiddleware';
import { mcpServerManager } from '@/mcp';
import { activeTaskManager } from '@/recovery/ActiveTaskManager';

export const getSystemDashboardTool = tool({
  name: 'get_system_dashboard',
  description: 'Get a comprehensive system status dashboard showing all services, errors, and recovery statistics.',
  inputSchema: z.object({
    includeDetails: z.boolean().default(true).describe('Include detailed information about each component'),
    includeHistory: z.boolean().default(false).describe('Include historical data and trends'),
    includeRecommendations: z.boolean().default(true).describe('Include actionable recommendations'),
  }),
  execute: async (params: any) => {
    try {
      const { includeDetails, includeHistory, includeRecommendations } = params;

      // Get system health status
      const systemHealth = systemHealthMonitor.getSystemStatus();

      // Get recovery statistics
      const recoveryStats = serviceRecovery.getRecoveryStats();
      const errorRecoveryStats = errorRecoveryMiddleware.getRecoveryStats();

      // Get MCP server status
      const mcpServers = mcpServerManager.getAllServerStatuses();
      const connectedMCP = mcpServers.filter(s => s.status === 'connected').length;

      // Get active tasks
      let activeTasks;
      try {
        const tasks = await activeTaskManager.listActiveTasks();
        activeTasks = {
          total: tasks.length,
          inProgress: tasks.filter(t => t.progress > 0 && t.progress < 100).length,
          resumable: tasks.filter(t => t.isResumable).length,
        };
      } catch {
        // Active task manager might not be available
        activeTasks = { total: 0, inProgress: 0, resumable: 0 };
      }

      const dashboard = {
        timestamp: new Date().toISOString(),
        overallHealth: systemHealth.overall,
        summary: {
          systemStatus: systemHealth.overall,
          servicesHealthy: `${systemHealth.diagnostics.healthyServices}/${systemHealth.diagnostics.totalServices}`,
          mcpConnectivity: `${connectedMCP}/${mcpServers.length} servers`,
          activeRecoveries: recoveryStats.activeRecoveries,
          totalRecoveryAttempts: recoveryStats.totalExecutions,
          successfulRecoveries: recoveryStats.successfulExecutions,
          activeTasks: activeTasks.total,
        },
        services: systemHealth.services.map(service => ({
          name: service.name,
          type: service.type,
          status: service.status,
          lastCheck: service.lastCheck,
          responseTime: service.responseTime,
          error: service.error,
        })),
        mcpServers: mcpServers.map(server => ({
          name: server.name,
          id: server.id,
          status: server.status,
          lastConnected: server.lastConnected,
          toolCount: server.toolCount,
          error: server.lastError,
        })),
        recovery: {
          serviceRecovery: {
            totalPlans: recoveryStats.totalPlans,
            totalExecutions: recoveryStats.totalExecutions,
            successRate: recoveryStats.totalExecutions > 0
              ? (recoveryStats.successfulExecutions / recoveryStats.totalExecutions * 100).toFixed(1) + '%'
              : 'N/A',
            activeRecoveries: recoveryStats.activeRecoveries,
          },
          errorRecovery: {
            totalAttempts: errorRecoveryStats.totalAttempts,
            successfulRecoveries: errorRecoveryStats.successfulRecoveries,
            successRate: errorRecoveryStats.totalAttempts > 0
              ? (errorRecoveryStats.successfulRecoveries / errorRecoveryStats.totalAttempts * 100).toFixed(1) + '%'
              : 'N/A',
            diagnosticAgentInvocations: errorRecoveryStats.diagnosticAgentInvocations,
            averageRecoveryTime: errorRecoveryStats.averageRecoveryTime
              ? `${errorRecoveryStats.averageRecoveryTime.toFixed(0)}ms`
              : 'N/A',
          },
        },
        tasks: activeTasks,
      };

      if (includeDetails) {
        // Add detailed service information
        (dashboard as any).detailedServices = systemHealth.services;

        // Add error patterns
        const errorPatterns = systemHealthMonitor.getErrorPatterns().slice(0, 10);
        (dashboard as any).errorPatterns = errorPatterns.map(pattern => ({
          service: pattern.service,
          pattern: pattern.pattern.substring(0, 100),
          count: pattern.count,
          lastOccurred: pattern.lastOccurred,
          suggestedFix: pattern.suggestedFix,
        }));

        // Add recovery plan statistics
        (dashboard as any).recoveryPlans = recoveryStats.planStats;
      }

      if (includeHistory) {
        // Add execution history (last 10 entries)
        const execHistory = serviceRecovery.getExecutionHistory().slice(-10);
        (dashboard as any).recentRecoveries = execHistory.map(exec => ({
          planId: exec.planId,
          startTime: exec.startTime,
          endTime: exec.endTime,
          success: exec.success,
          duration: exec.endTime ?
            `${(exec.endTime.getTime() - exec.startTime.getTime())}ms` : 'ongoing',
          actionsExecuted: exec.actionsExecuted.length,
          errors: exec.errors.length,
        }));

        // Add recent error recovery failures
        (dashboard as any).recentFailures = errorRecoveryStats.recentFailures.slice(0, 5);
      }

      if (includeRecommendations) {
        const recommendations: string[] = [];

        // System health recommendations
        if (systemHealth.recommendations) {
          recommendations.push(...systemHealth.recommendations);
        }

        // Recovery-specific recommendations
        if (systemHealth.overall === 'critical') {
          recommendations.push('System is in critical state - immediate attention required');
        }

        if (recoveryStats.activeRecoveries > 0) {
          recommendations.push('Recovery operations are in progress - monitor completion');
        }

        if (connectedMCP < mcpServers.length) {
          recommendations.push(`${mcpServers.length - connectedMCP} MCP servers are disconnected - check connectivity`);
        }

        if (errorRecoveryStats.recentFailures.length > 3) {
          recommendations.push('Multiple recent recovery failures detected - manual intervention may be needed');
        }

        if (activeTasks.inProgress > 5) {
          recommendations.push('Many tasks in progress - system may be under heavy load');
        }

        (dashboard as any).recommendations = recommendations;
      }

      return {
        success: true,
        dashboard,
        message: `System dashboard generated - Status: ${systemHealth.overall}`,
        healthIndicator: getHealthIndicator(systemHealth.overall),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'Failed to generate system dashboard',
      };
    }
  },
});

export const monitorSystemHealthTool = tool({
  name: 'monitor_system_health',
  description: 'Start continuous system health monitoring with real-time alerts.',
  inputSchema: z.object({
    alertThreshold: z.enum(['low', 'medium', 'high', 'critical']).default('high')
      .describe('Minimum severity level for alerts'),
    checkInterval: z.number().min(5000).max(300000).default(30000)
      .describe('Health check interval in milliseconds'),
    autoRecover: z.boolean().default(true).describe('Automatically attempt recovery for failed services'),
  }),
  execute: async (params: any) => {
    try {
      const { alertThreshold, checkInterval, autoRecover } = params;

      // Start system health monitoring
      await systemHealthMonitor.startMonitoring();

      // Set up event listeners for real-time monitoring
      const monitoringSession = {
        startTime: new Date(),
        alertThreshold,
        checkInterval,
        autoRecover,
        alertsGenerated: 0,
        recoveriesAttempted: 0,
      };

      // This would typically set up event listeners, but for demonstration
      // we'll just return the configuration

      return {
        success: true,
        monitoring: {
          status: 'active',
          session: monitoringSession,
          currentHealth: systemHealthMonitor.getSystemStatus().overall,
        },
        message: `System health monitoring started with ${alertThreshold} alert threshold`,
        nextSteps: [
          'Monitoring is now active',
          'Use get_system_dashboard to check status',
          'Alerts will be generated based on threshold',
          autoRecover ? 'Automatic recovery is enabled' : 'Manual recovery required for issues',
        ],
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'Failed to start system health monitoring',
      };
    }
  },
});

export const generateHealthReportTool = tool({
  name: 'generate_health_report',
  description: 'Generate a detailed health report with analysis and recommendations.',
  inputSchema: z.object({
    timeframe: z.enum(['last_hour', 'last_day', 'last_week']).default('last_day')
      .describe('Time frame for the report'),
    format: z.enum(['summary', 'detailed', 'technical']).default('detailed')
      .describe('Report detail level'),
    includeMetrics: z.boolean().default(true).describe('Include performance metrics'),
    includeTrends: z.boolean().default(true).describe('Include trend analysis'),
  }),
  execute: async (params: any) => {
    try {
      const { timeframe, format, includeMetrics, includeTrends } = params;

      // Calculate time window based on timeframe parameter
      const now = Date.now();
      const timeWindows: Record<string, number> = {
        'last_hour': 60 * 60 * 1000,
        'last_day': 24 * 60 * 60 * 1000,
        'last_week': 7 * 24 * 60 * 60 * 1000,
      };
      const windowStart = now - timeWindows[timeframe as string];

      // Get data and filter by timeframe
      const currentStatus = systemHealthMonitor.getSystemStatus();
      const recoveryStats = serviceRecovery.getRecoveryStats();
      const errorRecoveryStats = errorRecoveryMiddleware.getRecoveryStats();
      const errorPatterns = systemHealthMonitor.getErrorPatterns();

      // Filter error patterns by timeframe (using count as proxy for recency)
      // Note: In production, we'd track actual timestamps for each error occurrence
      const recentErrorCount = errorPatterns.reduce((sum, pattern) =>
        sum + (pattern.count || 0), 0
      );

      // Calculate metrics within timeframe window
      const timeframeMetrics = calculateTimeframeMetrics(
        recoveryStats,
        errorRecoveryStats,
        windowStart,
        now
      );

      const report = {
        metadata: {
          generatedAt: new Date().toISOString(),
          timeframe,
          format,
          systemVersion: 'Dante v1.0',
        },
        executive_summary: {
          overallHealth: currentStatus.overall,
          criticalIssues: currentStatus.services.filter(s => s.status === 'failed').length,
          activeServices: currentStatus.diagnostics.healthyServices,
          totalServices: currentStatus.diagnostics.totalServices,
          systemReliability: timeframeMetrics.successRate,
          timeframeWindow: `${timeframe.replace('_', ' ')}`,
          totalExecutionsInTimeframe: timeframeMetrics.totalExecutions,
          recentErrors: recentErrorCount,
        },
        service_analysis: currentStatus.services.map(service => {
          const analysis = {
            name: service.name,
            status: service.status,
            uptime: getUptimeEstimate(service),
            issues: [] as string[],
            recommendations: [] as string[],
          };

          if (service.error) {
            analysis.issues.push(service.error);
          }

          if (service.status === 'failed') {
            analysis.recommendations.push('Immediate restart required');
          } else if (service.status === 'degraded') {
            analysis.recommendations.push('Monitor closely for improvement');
          }

          return analysis;
        }),
        error_analysis: {
          totalPatterns: errorPatterns.length,
          topErrors: errorPatterns
            .sort((a, b) => b.count - a.count)
            .slice(0, 5)
            .map(pattern => ({
              service: pattern.service,
              pattern: pattern.pattern.substring(0, 100),
              occurrences: pattern.count,
              lastSeen: pattern.lastOccurred,
              severity: pattern.count > 10 ? 'high' : pattern.count > 5 ? 'medium' : 'low',
              suggestedFix: pattern.suggestedFix,
            })),
        },
        recovery_performance: {
          timeframe: {
            period: timeframe.replace('_', ' '),
            startTime: timeframeMetrics.timeframeStart,
            endTime: timeframeMetrics.timeframeEnd,
          },
          totalAttempts: timeframeMetrics.totalExecutions,
          successfulRecoveries: timeframeMetrics.successfulExecutions,
          failedRecoveries: timeframeMetrics.failedExecutions,
          successRate: timeframeMetrics.successRate,
          averageRecoveryTime: timeframeMetrics.avgRecoveryTime
            ? `${timeframeMetrics.avgRecoveryTime}ms`
            : 'N/A',
          errorRecoveriesInTimeframe: timeframeMetrics.errorRecoveries,
        },
      };

      if (includeMetrics && format !== 'summary') {
        (report as any).performance_metrics = {
          responseTimeDistribution: getResponseTimeDistribution(currentStatus.services),
          serviceAvailability: getServiceAvailability(currentStatus.services),
          errorFrequency: getErrorFrequency(errorPatterns),
          recoveryEffectiveness: getRecoveryEffectiveness(recoveryStats),
        };
      }

      if (includeTrends && format === 'detailed') {
        (report as any).trend_analysis = {
          systemHealthTrend: 'Stable', // This would be calculated from historical data
          errorTrend: errorPatterns.length > 0 ? 'Increasing' : 'Stable',
          recoveryTrend: recoveryStats.successfulExecutions > 0 ? 'Improving' : 'Stable',
          recommendations: generateTrendRecommendations(errorPatterns, recoveryStats),
        };
      }

      return {
        success: true,
        report,
        message: `Health report generated for ${timeframe} with ${format} detail level`,
        summary: {
          overallHealth: currentStatus.overall,
          totalIssues: currentStatus.diagnostics.failedServices + currentStatus.diagnostics.degradedServices,
          recoverySuccessRate: recoveryStats.totalExecutions > 0
            ? `${(recoveryStats.successfulExecutions / recoveryStats.totalExecutions * 100).toFixed(1)}%`
            : 'N/A',
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'Failed to generate health report',
      };
    }
  },
});

export const exportSystemDataTool = tool({
  name: 'export_system_data',
  description: 'Export system health data for external analysis or backup.',
  inputSchema: z.object({
    dataTypes: z.array(z.enum(['health_status', 'error_patterns', 'recovery_history', 'mcp_status']))
      .default(['health_status', 'error_patterns'])
      .describe('Types of data to export'),
    format: z.enum(['json', 'csv', 'yaml']).default('json').describe('Export format'),
    includeTimestamps: z.boolean().default(true).describe('Include timestamp information'),
  }),
  execute: async (params: any) => {
    try {
      const { dataTypes, format, includeTimestamps } = params;

      const exportData: any = {};

      if (includeTimestamps) {
        exportData.exported_at = new Date().toISOString();
        exportData.system_info = {
          version: 'Dante v1.0',
          platform: process.platform,
          node_version: process.version,
        };
      }

      if (dataTypes.includes('health_status')) {
        exportData.health_status = systemHealthMonitor.getSystemStatus();
      }

      if (dataTypes.includes('error_patterns')) {
        exportData.error_patterns = systemHealthMonitor.getErrorPatterns();
      }

      if (dataTypes.includes('recovery_history')) {
        exportData.recovery_history = {
          service_recovery: serviceRecovery.getExecutionHistory(),
          error_recovery: errorRecoveryMiddleware.getRecoveryStats(),
        };
      }

      if (dataTypes.includes('mcp_status')) {
        exportData.mcp_status = mcpServerManager.getAllServerStatuses();
      }

      // Format the data according to the requested format
      let formattedData: string;
      switch (format) {
        case 'json':
          formattedData = JSON.stringify(exportData, null, 2);
          break;
        case 'yaml':
          // Use proper YAML library for robust conversion
          formattedData = YAML.stringify(exportData);
          break;
        case 'csv':
          // Convert to CSV format (simplified)
          if (dataTypes.includes('health_status')) {
            const services = exportData.health_status.services;
            formattedData = 'Service,Type,Status,Last Check,Error\n' +
              services.map((s: any) => `${s.name},${s.type},${s.status},${s.lastCheck},${s.error || ''}`).join('\n');
          } else {
            formattedData = 'No CSV conversion available for selected data types';
          }
          break;
        default:
          formattedData = JSON.stringify(exportData, null, 2);
      }

      return {
        success: true,
        export: {
          format,
          dataTypes,
          size: formattedData.length,
          data: formattedData.length > 10000
            ? formattedData.substring(0, 10000) + '... (truncated for display)'
            : formattedData,
        },
        message: `System data exported in ${format} format`,
        metadata: {
          totalSize: formattedData.length,
          recordsCount: Object.keys(exportData).length,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'Failed to export system data',
      };
    }
  },
});

/**
 * Helper functions
 */

function calculateTimeframeMetrics(
  recoveryStats: any,
  errorRecoveryStats: any,
  windowStart: number,
  windowEnd: number
): any {
  // Calculate metrics within the specified timeframe
  // This would filter historical data based on timestamps
  // For now, we'll return a subset of the stats
  return {
    totalExecutions: recoveryStats.totalExecutions || 0,
    successfulExecutions: recoveryStats.successfulExecutions || 0,
    failedExecutions: recoveryStats.failedExecutions || 0,
    errorRecoveries: errorRecoveryStats.totalRecoveries || 0,
    timeframeStart: new Date(windowStart).toISOString(),
    timeframeEnd: new Date(windowEnd).toISOString(),
    // In a real implementation, these would be filtered by timestamp
    avgRecoveryTime: recoveryStats.avgRecoveryTime || 0,
    successRate: recoveryStats.totalExecutions > 0
      ? (recoveryStats.successfulExecutions / recoveryStats.totalExecutions * 100).toFixed(1)
      : 'N/A',
  };
}

function getHealthIndicator(status: string): string {
  switch (status) {
    case 'healthy': return '🟢';
    case 'degraded': return '🟡';
    case 'critical': return '🔴';
    default: return '⚪';
  }
}

function getUptimeEstimate(service: any): string {
  // This would calculate actual uptime from historical data
  return service.status === 'healthy' ? '99.9%' :
         service.status === 'degraded' ? '95.0%' : '0%';
}

function getResponseTimeDistribution(services: any[]): any {
  const times = services
    .filter(s => s.responseTime)
    .map(s => s.responseTime);

  if (times.length === 0) return { average: 0, min: 0, max: 0 };

  return {
    average: times.reduce((sum, time) => sum + time, 0) / times.length,
    min: Math.min(...times),
    max: Math.max(...times),
    count: times.length,
  };
}

function getServiceAvailability(services: any[]): number {
  const healthy = services.filter(s => s.status === 'healthy').length;
  return services.length > 0 ? (healthy / services.length) * 100 : 0;
}

function getErrorFrequency(patterns: any[]): number {
  return patterns.reduce((sum, pattern) => sum + pattern.count, 0);
}

function getRecoveryEffectiveness(stats: any): number {
  return stats.totalExecutions > 0
    ? (stats.successfulExecutions / stats.totalExecutions) * 100
    : 0;
}

function generateTrendRecommendations(errorPatterns: any[], recoveryStats: any): string[] {
  const recommendations: string[] = [];

  if (errorPatterns.length > 10) {
    recommendations.push('High error frequency detected - consider system optimization');
  }

  if (recoveryStats.successfulExecutions < recoveryStats.totalExecutions * 0.8) {
    recommendations.push('Recovery success rate below 80% - review recovery procedures');
  }

  if (recommendations.length === 0) {
    recommendations.push('System performance is stable - maintain current monitoring');
  }

  return recommendations;
}

// Export all system status tools
export const systemStatusTools = [
  getSystemDashboardTool,
  monitorSystemHealthTool,
  generateHealthReportTool,
  exportSystemDataTool,
];
