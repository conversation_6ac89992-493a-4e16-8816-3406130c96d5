import { tool } from 'ai';
import { z } from 'zod';
import { memoryManager } from '../memory/MemoryManager';
import { MemoryType, MemoryPriority } from '../memory/types';

// Define tool parameter schemas with proper types
const rememberToolParameters = z.object({
  content: z.string().describe('The information to remember'),
  category: z.enum(['user-preference', 'project-knowledge', 'solution', 'general'])
    .describe('Category of the memory'),
  importance: z.enum(['critical', 'high', 'medium', 'low'])
    .describe('Importance level of this information')
});

type RememberToolParams = z.infer<typeof rememberToolParameters>;

// Memory-aware tools
export const rememberTool = tool({
  description: 'Store important information in long-term memory for future reference',
  inputSchema: rememberToolParameters,
  execute: async (params: RememberToolParams) => {
    const { content, category, importance } = params;
    const priorityMap = {
      critical: MemoryPriority.CRITICAL,
      high: MemoryPriority.HIGH,
      medium: MemoryPriority.MEDIUM,
      low: MemoryPriority.LOW
    } as const;

    const typeMap = {
      'user-preference': MemoryType.SEMANTIC,
      'project-knowledge': MemoryType.SEMANTIC,
      'solution': MemoryType.PROCEDURAL,
      'general': MemoryType.EPISODIC
    } as const;

    try {
      // Create memory with enhanced metadata for better consolidation
      const enrichedContent = {
        content,
        category,
        timestamp: new Date().toISOString(),
        context: {
          createdBy: 'user_interaction',
          importance
        }
      };

      const result = await memoryManager.create(
        typeMap[category],
        enrichedContent,
        {
          priority: priorityMap[importance],
          tags: [category, `priority-${importance}`],
          confidence: 0.9,
          source: 'user_memory_tool'
        }
      );

      return {
        success: result.success,
        message: result.success
          ? `Successfully remembered: ${content.substring(0, 100)}${content.length > 100 ? '...' : ''}`
          : `Failed to remember: ${result.error}`,
        memoryId: result.success ? (result.memoryId || null) : null
      };
    } catch (error) {
      return {
        success: false,
        message: `Error storing memory: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }
});

const recallToolParameters = z.object({
  query: z.string().describe('What to search for in memory'),
  limit: z.number().optional().describe('Maximum number of memories to recall (default: 5)'),
  expandConsolidated: z.boolean().optional().describe('Expand consolidated memories to show original details (default: true)'),
  includeConsolidated: z.boolean().optional().describe('Include consolidated summary memories (default: false)')
});

type RecallToolParams = z.infer<typeof recallToolParameters>;

export const recallTool = tool({
  description: 'Intelligently search for specific, relevant memories based on current conversation context. Only use when you need specific information that might be stored in memory. Be specific in your query. Automatically expands consolidated memories to show full details.',
  inputSchema: recallToolParameters as any,
  execute: async (params: RecallToolParams) => {
    const { query, limit, expandConsolidated, includeConsolidated } = params;
    const maxLimit = Math.min(limit || 3, 5); // Default to 3, cap at 5

    try {
      // Use enhanced recall that properly expands consolidated memories
      const memories = await memoryManager.recallWithDetails(query, {
        limit: maxLimit,
        expandConsolidated: expandConsolidated ?? true,
        includeConsolidated: includeConsolidated ?? false
      });

      if (memories.length === 0) {
        return {
          success: true,
          memories: [],
          message: `No relevant memories found for: "${query}". Try a more specific search or different keywords.`
        };
      }

      // Filter for higher relevance and format
      const formattedMemories = memories
        .filter(m => {
          // Only include high-confidence or high-priority memories
          const confidence = m.metadata.confidence || 0;
          const priority = m.metadata.priority || 0;
          return confidence > 0.6 || priority >= MemoryPriority.HIGH;
        })
        .map((m, i) => {
          const content = m.content as any;
          // Check if this is from a consolidated memory
          const isFromConsolidated = m.metadata.source === 'consolidation' ||
                                     content?.consolidatedFrom?.length > 0;

          return {
            index: i + 1,
            type: m.metadata.type,
            content: m.content,
            priority: m.metadata.priority,
            confidence: Math.round((m.metadata.confidence || 0) * 100),
            createdAt: m.metadata.createdAt,
            relevance: (m as any).similarity ? Math.round((m as any).similarity * 100) : 'N/A',
            source: m.metadata.source,
            isConsolidated: isFromConsolidated,
            consolidatedCount: content?.originalMemories?.length || content?.consolidatedFrom?.length || 0
          };
        });

      if (formattedMemories.length === 0) {
        return {
          success: true,
          memories: [],
          message: `Found ${memories.length} memories but none met relevance threshold. Try a more specific query.`
        };
      }

      const consolidatedInfo = formattedMemories.some(m => m.isConsolidated)
        ? ` (including expanded details from consolidated memories)`
        : '';

      return {
        success: true,
        memories: formattedMemories,
        message: `Found ${formattedMemories.length} highly relevant memories for "${query}"${consolidatedInfo}`,
        summary: formattedMemories.map(m => {
          const consolidatedTag = m.isConsolidated ? ` [Consolidated:${m.consolidatedCount}]` : '';
          return `${m.index}. [${m.type}, ${m.relevance}% match]${consolidatedTag} ${JSON.stringify(m.content).substring(0, 100)}...`;
        }).join('\n'),
        expandedFromConsolidated: formattedMemories.filter(m => m.isConsolidated).length
      };
    } catch (error) {
      return {
        success: false,
        message: `Error recalling memories: ${error instanceof Error ? error.message : 'Unknown error'}`,
        memories: []
      };
    }
  }
});

const learnFromErrorToolParameters = z.object({
  error: z.string().describe('The error message or pattern'),
  solution: z.string().describe('The solution that fixed the error'),
  context: z.object({
    file: z.string().nullable().optional(),
    line: z.number().nullable().optional(),
    language: z.string().nullable().optional(),
    framework: z.string().nullable().optional()
  }).nullable().optional().describe('Context about the error'),
  agentName: z.string().nullable().optional().describe('Name of the agent that encountered the error'),
  runId: z.string().nullable().optional().describe('Run or session identifier for correlating events'),
  code: z.union([z.string(), z.number()]).nullable().optional().describe('Optional error code'),
  stack: z.string().nullable().optional().describe('Stack trace or diagnostic details'),
  nextSteps: z.array(z.string()).nullable().optional().describe('Suggested next steps captured during salvage'),
  recentToolCalls: z.array(z.any()).nullable().optional().describe('Recent tool call arguments for context (last 5 ideal)'),
  recentToolResults: z.array(z.any()).nullable().optional().describe('Recent tool call results for context (last 3 ideal)'),
  partialResults: z.any().nullable().optional().describe('Any partial results that were produced before the error'),
  additionalContext: z.record(z.string(), z.any()).nullable().optional().describe('Any additional key-value context about the failure')
});

type LearnFromErrorParams = z.infer<typeof learnFromErrorToolParameters>;

export const learnFromErrorTool = tool({
  description: 'Learn from an error and its solution for future reference. Captures salvaged context (partial results, recent tool calls/results, next steps) so future agents can avoid repeating the same mistakes.',
  inputSchema: learnFromErrorToolParameters as any,
  execute: async (params: LearnFromErrorParams) => {
    const {
      error,
      solution,
      context,
      agentName,
      runId,
      code,
      stack,
      nextSteps,
      recentToolCalls,
      recentToolResults,
      partialResults,
      additionalContext
    } = params;

    try {
      // Create enriched error memory with enhanced metadata and salvaged details
      const errorHash = error.substring(0, 50).replace(/[^a-zA-Z0-9]/g, '_');
      const enrichedContext = {
        ...(context || {}),
        ...(additionalContext || {}),
        timestamp: new Date().toISOString(),
        errorHash,
        solutionType: 'user_provided',
        agentName: agentName || undefined,
        runId: runId || undefined,
        errorCode: code ?? undefined,
        stack: stack || undefined,
        salvage: {
          nextSteps: nextSteps || [],
          recentToolCalls: Array.isArray(recentToolCalls) ? recentToolCalls.slice(-5) : [],
          recentToolResults: Array.isArray(recentToolResults) ? recentToolResults.slice(-3) : [],
          partialResults: partialResults ?? null
        }
      };

      const result = await memoryManager.rememberError(error, solution, enrichedContext);

      const salvageIncluded = !!(
        (nextSteps && nextSteps.length) ||
        (recentToolCalls && recentToolCalls.length) ||
        (recentToolResults && recentToolResults.length) ||
        partialResults
      );

      return {
        success: result.success,
        message: result.success
          ? `Learned from error and saved${salvageIncluded ? ' with salvaged context' : ''}. I will use this to prevent repeats.`
          : `Failed to learn from error: ${result.error}`,
        errorPattern: error.substring(0, 100),
        solution: solution.substring(0, 100),
        memoryId: result.memoryId || null,
        salvageIncluded
      };
    } catch (error) {
      return {
        success: false,
        message: `Error learning from error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }
});

const forgetToolParameters = z.object({
  query: z.string().describe('What to forget from memory'),
  confirmDeletion: z.boolean().default(false).describe('Confirm that you want to delete these memories')
});

type ForgetToolParams = z.infer<typeof forgetToolParameters>;

export const forgetTool = tool({
  description: 'Remove specific information from memory (use with caution)',
  inputSchema: forgetToolParameters as any,
  execute: async (params: ForgetToolParams) => {
    const { query, confirmDeletion } = params;

    if (!confirmDeletion) {
      return {
        success: false,
        message: 'Memory deletion requires explicit confirmation. Set confirmDeletion to true if you really want to forget this information.'
      };
    }

    try {
      // First, find memories that match the query (using enhanced recall)
      const memories = await memoryManager.recallWithDetails(query, {
        limit: 10,
        expandConsolidated: true,
        includeConsolidated: true // Include consolidated to delete them too
      });

      if (memories.length === 0) {
        return {
          success: true,
          message: 'No memories found matching that query.',
          deletedCount: 0
        };
      }

      // Delete the matching memories
      let deletedCount = 0;
      for (const memory of memories) {
        try {
          await memoryManager.delete(memory.metadata.id);
          deletedCount++;
        } catch (deleteError) {
          console.warn(`Failed to delete memory ${memory.metadata.id}:`, deleteError);
        }
      }

      return {
        success: true,
        message: `Successfully forgot ${deletedCount} memories related to: ${query}`,
        deletedCount
      };
    } catch (error) {
      return {
        success: false,
        message: `Error forgetting memories: ${error instanceof Error ? error.message : 'Unknown error'}`,
        deletedCount: 0
      };
    }
  }
});

const getMemoryStatsToolParameters = z.object({
  includeDetails: z.boolean().default(false).describe('Include detailed breakdown by type and priority')
});

type GetMemoryStatsParams = z.infer<typeof getMemoryStatsToolParameters>;

export const getMemoryStatsTool = tool({
  description: 'Get statistics about stored memories',
  inputSchema: getMemoryStatsToolParameters as any,
  execute: async (params: GetMemoryStatsParams) => {
    const { includeDetails } = params;

    try {
      const stats = await memoryManager.getStats();

      if (includeDetails) {
        return {
          success: true,
          stats,
          message: `Memory system contains ${stats.totalMemories} memories.`
        };
      } else {
        return {
          success: true,
          totalMemories: stats.totalMemories,
          memoryTypes: Object.keys(stats.byType || {}).length,
          message: `Memory system contains ${stats.totalMemories} memories.`
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `Error getting memory stats: ${error instanceof Error ? error.message : 'Unknown error'}`,
        stats: null
      };
    }
  }
});

// Context-aware memory search tool
const CONTEXTUAL_SEARCH_REPEAT_WINDOW_MS = 60_000;
const CONTEXTUAL_SEARCH_GUARD_THRESHOLD = 3;
const CONTEXTUAL_SEARCH_EXPIRATION_MS = CONTEXTUAL_SEARCH_REPEAT_WINDOW_MS * 5;
const CONTEXTUAL_SEARCH_MAX_TRACKED = 200;
const CONTEXTUAL_SEARCH_STOP_WORDS = new Set<string>([
  'the', 'a', 'an', 'and', 'or', 'to', 'for', 'with', 'without', 'in', 'on', 'at', 'by', 'from',
  'is', 'are', 'was', 'were', 'be', 'been', 'being', 'this', 'that', 'these', 'those', 'it', 'its',
  'as', 'of', 'about', 'into', 'over', 'after', 'before', 'should', 'would', 'could', 'can', 'will',
  'just', 'please', 'help', 'let', 'know', 'tell', 'me', 'we', 'you', 'they', 'their', 'our', 'your',
  'if', 'when', 'whether', 'do', 'does', 'did', 'using', 'use', 'used', 'versus', 'vs', 'vs.'
]);

interface ContextualSearchUsageEntry {
  count: number;
  firstAt: number;
  lastAt: number;
  lastResult?: any;
}

const contextualSearchUsage = new Map<string, ContextualSearchUsageEntry>();

const buildContextualSearchKey = (conversationContext?: string, specificNeed?: string): string => {
  const combined = `${conversationContext || ''} ${specificNeed || ''}`.toLowerCase();
  const cleaned = combined.replace(/[^a-z0-9\s]/g, ' ');
  const tokens = cleaned
    .split(/\s+/)
    .map((token) => token.trim())
    .filter((token) => token.length > 0)
    .filter((token) => !CONTEXTUAL_SEARCH_STOP_WORDS.has(token));
  if (tokens.length === 0) {
    return cleaned.trim() || '__empty__';
  }
  tokens.sort();
  return tokens.slice(0, 40).join('|');
};

const appendGuardHint = (baseMessage: string | undefined, addition: string): string => {
  const trimmed = (baseMessage || '').trim();
  if (!trimmed) return addition;
  if (trimmed.includes(addition)) return trimmed;
  return `${trimmed}\n\n${addition}`;
};

const cloneContextualResult = <T>(value: T): T => {
  try {
    const globalStructuredClone = (globalThis as any)?.structuredClone;
    if (typeof globalStructuredClone === 'function') {
      return globalStructuredClone(value);
    }
  } catch {}
  try {
    return JSON.parse(JSON.stringify(value));
  } catch {
    return value;
  }
};

const pruneContextualSearchUsage = (now: number) => {
  for (const [key, entry] of contextualSearchUsage.entries()) {
    if (now - entry.lastAt > CONTEXTUAL_SEARCH_EXPIRATION_MS) {
      contextualSearchUsage.delete(key);
    }
  }
  if (contextualSearchUsage.size <= CONTEXTUAL_SEARCH_MAX_TRACKED) return;
  const entries = Array.from(contextualSearchUsage.entries()).sort((a, b) => a[1].lastAt - b[1].lastAt);
  while (entries.length > CONTEXTUAL_SEARCH_MAX_TRACKED) {
    const oldest = entries.shift();
    if (oldest) {
      contextualSearchUsage.delete(oldest[0]);
    }
  }
};

const contextualSearchParameters = z.object({
  conversationContext: z.string().describe('Current conversation context to search for relevant memories'),
  specificNeed: z.string().optional().describe('Specific information you need from memory'),
  maxResults: z.number().optional().describe('Maximum memories to retrieve (1-5)')
});

type ContextualSearchParams = z.infer<typeof contextualSearchParameters>;

export const contextualMemorySearchTool = tool({
  description: 'Intelligently search for memories relevant to the current conversation context. Use this when the conversation suggests relevant past knowledge might exist.',
  inputSchema: contextualSearchParameters as any,
  execute: async (params: ContextualSearchParams) => {
    const { conversationContext, specificNeed, maxResults } = params;
    const finalMaxResults = maxResults || 3;
    const now = Date.now();
    pruneContextualSearchUsage(now);
    const key = buildContextualSearchKey(conversationContext, specificNeed);
    const existing = contextualSearchUsage.get(key);
    const withinWindow = existing && (now - existing.firstAt) <= CONTEXTUAL_SEARCH_REPEAT_WINDOW_MS;

    if (existing && !withinWindow) {
      contextualSearchUsage.delete(key);
    }

    if (existing && withinWindow && existing.lastResult) {
      existing.count += 1;
      existing.lastAt = now;
      contextualSearchUsage.set(key, existing);
      const base = cloneContextualResult(existing.lastResult);
      const guardTriggered = existing.count >= CONTEXTUAL_SEARCH_GUARD_THRESHOLD;
      const seconds = Math.max(1, Math.round((now - existing.firstAt) / 1000));
      const hint = guardTriggered
        ? `⚠️ Memory search loop guard triggered (#${existing.count} in ${seconds}s). Use the retrieved memories to plan next steps before calling contextual_memory_search again.`
        : `ℹ️ Reusing cached contextual_memory_search result (#${existing.count}). Apply the retrieved context before requesting another memory search.`;
      const result = {
        ...base,
        message: appendGuardHint(base?.message, hint),
        reusedFromCache: true,
        loopGuardCount: existing.count,
        loopGuardActive: guardTriggered
      } as any;
      if (guardTriggered) {
        result.guardrail = 'memory_search_loop';
        result.loopGuardReason = 'repeat_query';
        console.warn('[memory] Prevented contextual_memory_search loop for repeated query:', hint);
      }
      return result;
    }

    try {
      // Extract keywords from context for search
      const searchQuery = specificNeed || conversationContext;
      const limit = Math.min(finalMaxResults, 5);

      // Use enhanced recall with automatic expansion of consolidated memories
      const memories = await memoryManager.recallWithDetails(searchQuery, {
        limit,
        expandConsolidated: true,
        includeConsolidated: false
      });

      if (memories.length === 0) {
        return {
          success: true,
          memories: [],
          message: `No relevant memories found for current context. This appears to be new information.`,
          shouldRemember: true
        };
      }

      // Filter for only highly relevant memories
      const relevantMemories = memories
        .filter(m => {
          const confidence = m.metadata.confidence || 0;
          const priority = m.metadata.priority || 0;
          return confidence > 0.7 || priority >= MemoryPriority.HIGH;
        })
        .slice(0, finalMaxResults);

      if (relevantMemories.length === 0) {
        return {
          success: true,
          memories: [],
          message: `Found ${memories.length} potential memories but none were highly relevant to current context.`,
          shouldRemember: true
        };
      }

      const formattedMemories = relevantMemories.map((m, i) => ({
        index: i + 1,
        type: m.metadata.type,
        content: m.content,
        confidence: Math.round((m.metadata.confidence || 0) * 100),
        priority: m.metadata.priority,
        createdAt: m.metadata.createdAt
      }));

      const result = {
        success: true,
        memories: formattedMemories,
        message: `Found ${relevantMemories.length} relevant memories for current context`,
        contextualSummary: formattedMemories.map(m =>
          `${m.type}: ${JSON.stringify(m.content).substring(0, 150)}...`
        ).join('\n'),
        shouldRemember: false
      };
      const cachedResult = cloneContextualResult(result);
      const nextEntry: ContextualSearchUsageEntry = existing && withinWindow
        ? {
            count: (existing.count || 0) + 1,
            firstAt: existing.firstAt,
            lastAt: now,
            lastResult: cachedResult
          }
        : {
            count: 1,
            firstAt: now,
            lastAt: now,
            lastResult: cachedResult
          };
      contextualSearchUsage.set(key, nextEntry);
      return {
        ...result,
        reusedFromCache: false,
        loopGuardActive: false,
        loopGuardCount: nextEntry.count
      };

    } catch (error) {
      const failure = {
        success: false,
        message: `Error searching contextual memories: ${error instanceof Error ? error.message : 'Unknown error'}`,
        memories: [],
        shouldRemember: true
      };
      contextualSearchUsage.set(key, {
        count: existing && withinWindow ? (existing.count || 0) + 1 : 1,
        firstAt: existing && withinWindow ? existing.firstAt : now,
        lastAt: now,
        lastResult: cloneContextualResult(failure)
      });
      return {
        ...failure,
        reusedFromCache: false,
        loopGuardActive: false,
        loopGuardCount: existing && withinWindow ? (existing.count || 0) + 1 : 1
      };
    }
  }
});

export const resetContextualMemorySearchGuard = () => {
  contextualSearchUsage.clear();
};

/**
 * Helper function to create properly structured memories based on type
 */
export function createStructuredMemory(
  type: MemoryType,
  content: any,
  metadata: Partial<any> = {}
): { content: any; metadata: any } {
  const baseMetadata = {
    timestamp: new Date().toISOString(),
    version: '2.0', // Version 2.0 includes consolidation support
    ...metadata
  };

  switch (type) {
    case MemoryType.SEMANTIC:
      return {
        content: {
          data: content,
          type: 'semantic',
          searchableText: typeof content === 'string' ? content : JSON.stringify(content),
          ...baseMetadata
        },
        metadata: {
          ...baseMetadata,
          tags: [...(metadata.tags || []), 'semantic']
        }
      };

    case MemoryType.EPISODIC:
      return {
        content: {
          event: content,
          type: 'episodic',
          when: baseMetadata.timestamp,
          ...baseMetadata
        },
        metadata: {
          ...baseMetadata,
          tags: [...(metadata.tags || []), 'episodic', 'event']
        }
      };

    case MemoryType.PROCEDURAL:
      return {
        content: {
          procedure: content,
          type: 'procedural',
          steps: Array.isArray(content) ? content : [content],
          ...baseMetadata
        },
        metadata: {
          ...baseMetadata,
          tags: [...(metadata.tags || []), 'procedural', 'howto']
        }
      };

    default:
      return {
        content: {
          data: content,
          ...baseMetadata
        },
        metadata: baseMetadata
      };
  }
}

// Export all memory tools
export const memoryTools = [
  rememberTool,
  recallTool,
  contextualMemorySearchTool,
  learnFromErrorTool,
  forgetTool,
  getMemoryStatsTool
];

// Initialize memory system without loading memories into context
export async function initializeMemory(_maxTokens: number = 5000) {
  try {
    // Initialize memory without forcing a full scan to keep startup light
    await memoryManager.initialize();

    // Prefer lightweight vector-store stats to avoid loading all memories
    const vectorStore: any = memoryManager.getVectorStore();
    let totalVectors: number | undefined = undefined;
    try {
      if (vectorStore && typeof vectorStore.getStats === 'function') {
        const vstats = await vectorStore.getStats();
        totalVectors = vstats?.totalVectors;
      }
    } catch {
      // Ignore vector stats errors; system may still function
    }

    const memInfo = typeof totalVectors === 'number'
      ? `${totalVectors} vectors indexed`
      : 'initialized';

    console.log(`🧠 Memory system ${memInfo}`);
    console.log(`📋 Use 'recall' tool to search for relevant memories when needed`);

    return {
      success: true,
      totalMemories: totalVectors,
      message: 'Memory system initialized (lightweight) - use recall tool for contextual retrieval'
    };
  } catch (error) {
    console.warn('⚠️ Memory initialization failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Get only critical user preferences (not general memories)
export async function getUserPreferences(): Promise<string> {
  try {
    const preferences = await memoryManager.search({
      limit: 3,
      minPriority: MemoryPriority.HIGH
    });

    if (preferences.memories.length === 0) {
      return '';
    }

    const prefText = preferences.memories
      .filter(m => m.content && typeof m.content === 'object')
      .map(m => {
        const content = m.content as any;
        return `${content.category || 'preference'}: ${JSON.stringify(content)}`;
      })
      .join('; ');

    return prefText ? `User preferences: ${prefText}` : '';
  } catch (error) {
    console.warn('Failed to get user preferences:', error);
    return '';
  }
}
