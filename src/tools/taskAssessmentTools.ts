import { tool } from 'ai';
import { z } from 'zod';
// Remove circular dependencies - we'll implement assessment logic directly here
// import { assessTaskComplexity } from '../agents/DanteOrchestrator';
// import { needsPlanning, estimateComplexity } from '../agents/PlanningAgent';
import { selectModelForFileOperation, estimateFileOperationTokens } from '../utils/tokenLimiter';
import OpenAI from 'openai';
import { geminiClient, GeminiClient } from '../utils/geminiClient';

// Intelligent task assessment tool
export const assessTaskTool = tool({
  name: 'assess_task',
  description: 'Intelligently assess task complexity, planning needs, and optimal delegation strategy before processing.',
  inputSchema: z.object({
    userMessage: z.string().describe('The user\'s request to assess'),
    conversationHistory: z.array(z.object({
      role: z.string(),
      content: z.string(),
      timestamp: z.number().optional()
    })).default([]).describe('Previous conversation messages for context'),
    includeRecommendations: z.boolean().default(true).describe('Include agent delegation recommendations'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { userMessage, conversationHistory, includeRecommendations } = params;

      // Implement assessment logic directly to avoid circular dependencies
      const assessment = await assessTaskComplexityDirect(userMessage, conversationHistory);

      const response: any = {
        success: true,
        assessment: {
          complexity: assessment.complexity,
          needsPlanning: assessment.needsPlanning,
          tokenEstimate: assessment.tokenEstimate,
          shouldCreateRecoveryPoint: assessment.shouldCreateRecoveryPoint,
        },
        message: `Task assessed as ${assessment.complexity} complexity, ${assessment.tokenEstimate} tokens estimated`,
      };

      if (includeRecommendations) {
        response.recommendations = {
          recommendedAgent: assessment.recommendedAgent,
          reasoning: generateRecommendationReasoning(assessment, userMessage),
          nextSteps: generateNextSteps(assessment),
        };
      }

      return response;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to assess task complexity',
      };
    }
  },
});

// Planning detection tool
export const checkPlanningNeedTool = tool({
  name: 'check_planning_need',
  description: 'Check if a request needs structured planning before execution.',
  inputSchema: z.object({
    userMessage: z.string().describe('The user\'s request to analyze'),
    conversationHistory: z.array(z.object({
      role: z.string(),
      content: z.string(),
      timestamp: z.number().optional()
    })).default([]).describe('Previous conversation messages'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { userMessage, conversationHistory } = params;

      const planningNeeded = needsPlanningDirect(userMessage);
      const complexity = estimateComplexityDirect(userMessage);

      return {
        success: true,
        needsPlanning: planningNeeded,
        complexity,
        reasoning: planningNeeded ?
          'Request contains multiple steps, vague requirements, or complex dependencies that benefit from structured planning' :
          'Request is straightforward and can be handled directly',
        recommendation: planningNeeded ?
          'Delegate to Planning Agent for structured todo creation' :
          'Proceed with direct task handling or specialist delegation',
        message: `Planning ${planningNeeded ? 'recommended' : 'not needed'} for ${complexity} complexity task`,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to check planning requirements',
      };
    }
  },
});

// Complexity estimation tool
export const estimateTaskComplexityTool = tool({
  name: 'estimate_task_complexity',
  description: 'Estimate the complexity level of a task for optimal resource allocation.',
  inputSchema: z.object({
    userMessage: z.string().describe('The user\'s request to analyze'),
    conversationHistory: z.array(z.object({
      role: z.string(),
      content: z.string(),
      timestamp: z.number().optional()
    })).default([]).describe('Previous conversation messages'),
    includeTokenEstimate: z.boolean().default(true).describe('Include token usage estimation'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { userMessage, conversationHistory, includeTokenEstimate } = params;

      const complexity = estimateComplexityDirect(userMessage);

      const response: any = {
        success: true,
        complexity,
        description: getComplexityDescription(complexity),
        estimatedTimeRange: getTimeEstimate(complexity),
        resourceRequirements: getResourceRequirements(complexity),
        message: `Task estimated as ${complexity} complexity`,
      };

      if (includeTokenEstimate) {
        const baseTokens = Math.floor(userMessage.length / 4);
        const historyTokens = conversationHistory.reduce((sum: number, msg: any) =>
          sum + Math.floor((msg.content?.length || 0) / 4), 0);

        response.tokenEstimate = {
          baseTokens,
          historyTokens,
          totalEstimate: baseTokens + historyTokens,
          recommendedApproach: (baseTokens + historyTokens) > 20000 ? 'Task Orchestrator' : 'Direct handling',
        };
      }

      return response;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to estimate task complexity',
      };
    }
  },
});

// Helper functions for the tools
function generateRecommendationReasoning(assessment: any, userMessage: string): string {
  const reasons = [];

  if (assessment.needsPlanning) {
    reasons.push('Multi-step planning detected');
  }

  if (assessment.tokenEstimate > 20000) {
    reasons.push('High token usage requires distributed processing');
  }

  if (assessment.complexity === 'expert') {
    reasons.push('Expert-level complexity requires specialized handling');
  }

  const lowerMessage = userMessage.toLowerCase();
  if (lowerMessage.includes('code') || lowerMessage.includes('implement')) {
    reasons.push('Code generation/implementation task detected');
  }

  if (assessment.shouldCreateRecoveryPoint) {
    reasons.push('Development task requires recovery point for hot-reload tolerance');
  }

  return reasons.length > 0 ? reasons.join('; ') : 'Standard task routing based on content analysis';
}

function generateNextSteps(assessment: any): string[] {
  const steps = [];

  if (assessment.shouldCreateRecoveryPoint) {
    steps.push('Create task recovery point before starting');
  }

  if (assessment.needsPlanning) {
    steps.push('Delegate to Planning Agent for structured breakdown');
  } else if (assessment.recommendedAgent) {
    steps.push(`Delegate to ${assessment.recommendedAgent} for specialized handling`);
  } else {
    steps.push('Handle directly with available tools');
  }

  if (assessment.complexity === 'complex' || assessment.complexity === 'expert') {
    steps.push('Monitor progress and create checkpoints');
    steps.push('Consider agent coordination for comprehensive solution');
  }

  return steps;
}

function getComplexityDescription(complexity: string): string {
  switch (complexity) {
    case 'simple':
      return 'Straightforward task with clear requirements and direct solution path';
    case 'moderate':
      return 'Standard task requiring some analysis or multi-step approach';
    case 'complex':
      return 'Multi-faceted task requiring coordination, planning, or specialized expertise';
    case 'expert':
      return 'Highly complex task requiring deep expertise, extensive coordination, or innovative solutions';
    default:
      return 'Complexity level unknown';
  }
}

function getTimeEstimate(complexity: string): string {
  switch (complexity) {
    case 'simple':
      return '1-5 minutes';
    case 'moderate':
      return '5-15 minutes';
    case 'complex':
      return '15-45 minutes';
    case 'expert':
      return '45+ minutes';
    default:
      return 'Unknown';
  }
}

function getResourceRequirements(complexity: string): string[] {
  const requirements = [];

  switch (complexity) {
    case 'simple':
      requirements.push('Minimal context', 'Direct tools');
      break;
    case 'moderate':
      requirements.push('Standard context', 'Core tools', 'Memory search');
      break;
    case 'complex':
      requirements.push('Extended context', 'Specialized agents', 'Task management', 'Memory integration');
      break;
    case 'expert':
      requirements.push('Full context', 'Multiple agents', 'Task orchestration', 'Recovery points', 'Memory + project context');
      break;
  }

  return requirements;
}

// Model recommendation tool for file operations
export const recommendModelForFileOperationTool = tool({
  name: 'recommend_model_for_file_operation',
  description: 'Recommend the optimal model for file operations based on size and complexity to avoid token limits.',
  inputSchema: z.object({
    operationType: z.enum(['search', 'list_directory', 'list_directory_lite', 'analyze_project', 'code_search']).describe('Type of file operation'),
    recursive: z.boolean().default(false).describe('Whether operation is recursive'),
    fileCount: z.number().optional().describe('Estimated number of files to process'),
    searchPattern: z.string().optional().describe('Search pattern if applicable'),
    currentModel: z.string().default('gpt-4.1').describe('Current model being used'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { operationType, recursive, fileCount, searchPattern, currentModel } = params;

      // Estimate tokens based on operation parameters
      const tokenEstimate = estimateFileOperationTokens({
        fileCount,
        recursive,
        searchPattern,
        directoryDepth: recursive ? 5 : 1
      });

      // Get recommended model
      const recommendedModel = selectModelForFileOperation(tokenEstimate, currentModel);

      // Determine if model change is needed
      const needsModelChange = recommendedModel !== currentModel;

      return {
        success: true,
        recommendation: {
          currentModel,
          recommendedModel,
          needsModelChange,
          tokenEstimate,
          operationType,
          reasoning: generateModelRecommendationReasoning(
            tokenEstimate,
            currentModel,
            recommendedModel,
            operationType
          )
        },
        message: needsModelChange
          ? `Recommend switching to ${recommendedModel} for this ${operationType} operation (${tokenEstimate} tokens estimated)`
          : `Current model ${currentModel} is suitable for this operation (${tokenEstimate} tokens estimated)`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to generate model recommendation',
      };
    }
  },
});

function generateModelRecommendationReasoning(
  tokenEstimate: number,
  currentModel: string,
  recommendedModel: string,
  operationType: string
): string {
  const reasons = [];

  if (tokenEstimate > 100000) {
    reasons.push(`Very high token estimate (${tokenEstimate}) requires long-context model`);
  } else if (tokenEstimate > 50000) {
    reasons.push(`High token estimate (${tokenEstimate}) benefits from higher capacity model`);
  } else if (tokenEstimate > 25000) {
    reasons.push(`Moderate token usage (${tokenEstimate}) may exceed standard model limits`);
  }

  if ((operationType === 'list_directory' || operationType === 'list_directory_lite') && tokenEstimate > 15000) {
    reasons.push('Directory listing operations can produce large outputs');
  }

  if (operationType === 'code_search' && tokenEstimate > 20000) {
    reasons.push('Code search results with many matches benefit from higher capacity');
  }

  if (currentModel === 'gpt-4.1' && tokenEstimate > 350000) {
    reasons.push('GPT-4.1 has 400K TPM limit which may be exceeded');
  }

  if (recommendedModel.includes('long-context')) {
    reasons.push('Long-context model provides 200K-400K TPM capacity');
  }

  return reasons.length > 0
    ? reasons.join('; ')
    : 'Current model is sufficient for this operation';
}

// Direct implementation functions to avoid circular dependencies

/**
 * Direct task complexity assessment to avoid importing from agents
 */
async function assessTaskComplexityDirect(
  userMessage: string,
  conversationHistory: any[] = []
): Promise<{
  needsPlanning: boolean;
  complexity: 'simple' | 'moderate' | 'complex' | 'expert';
  recommendedAgent: string;
  shouldCreateRecoveryPoint: boolean;
  tokenEstimate: number;
}> {
  try {
    // Analyze message for complexity indicators
    const planningNeeded = needsPlanningDirect(userMessage);
    const complexity = estimateComplexityDirect(userMessage);

    // Calculate token estimate
    const baseTokens = Math.floor(userMessage.length / 4);
    const historyTokens = conversationHistory.reduce((sum, msg) =>
      sum + Math.floor((msg.content?.length || 0) / 4), 0);
    const tokenEstimate = baseTokens + historyTokens;

    // Use LLM-based intelligent agent classification
    const recommendedAgent = await classifyTaskWithLLM(userMessage, {
      planningNeeded,
      tokenEstimate,
      complexity,
      conversationHistory
    });

    // Determine if recovery point needed
    const lowerMessage = userMessage.toLowerCase();
    const shouldCreateRecoveryPoint = (
      complexity !== 'simple' &&
      (lowerMessage.includes('code') || lowerMessage.includes('implement') ||
       lowerMessage.includes('integrate') || lowerMessage.includes('tool') ||
       lowerMessage.includes('improve') || recommendedAgent === 'TaskOrchestrator')
    );

    return {
      needsPlanning: planningNeeded,
      complexity,
      recommendedAgent,
      shouldCreateRecoveryPoint,
      tokenEstimate,
    };
  } catch (error) {
    console.warn('Task assessment failed, using defaults:', error);
    return {
      needsPlanning: false,
      complexity: 'moderate',
      recommendedAgent: 'TaskOrchestrator',
      shouldCreateRecoveryPoint: false,
      tokenEstimate: Math.floor(userMessage.length / 4),
    };
  }
}

/**
 * Direct planning need assessment
 */
function needsPlanningDirect(userMessage: string): boolean {
  const lowerMessage = userMessage.toLowerCase();

  // Multi-step indicators
  const multiStepIndicators = [
    'step by step', 'stages', 'phases', 'multiple', 'several',
    'first then', 'after that', 'once done', 'sequence'
  ];

  // Complex task indicators
  const complexityIndicators = [
    'build', 'create application', 'implement feature', 'develop',
    'analyze and improve', 'investigate and fix', 'research and implement',
    'audit and report', 'test and deploy'
  ];

  // Vague requirement indicators
  const vagueIndicators = [
    'somehow', 'figure out', 'make it work', 'improve', 'optimize',
    'better', 'enhance', 'upgrade'
  ];

  return multiStepIndicators.some(indicator => lowerMessage.includes(indicator)) ||
         complexityIndicators.some(indicator => lowerMessage.includes(indicator)) ||
         vagueIndicators.some(indicator => lowerMessage.includes(indicator)) ||
         userMessage.length > 500; // Long messages often need planning
}

/**
 * Direct complexity estimation
 */
function estimateComplexityDirect(userMessage: string): 'simple' | 'moderate' | 'complex' | 'expert' {
  const lowerMessage = userMessage.toLowerCase();
  const messageLength = userMessage.length;

  // Expert level indicators
  const expertIndicators = [
    'architecture', 'design pattern', 'enterprise', 'scalable',
    'distributed', 'microservices', 'advanced algorithm', 'machine learning'
  ];

  // Complex level indicators
  const complexIndicators = [
    'integrate', 'comprehensive', 'full-featured', 'end-to-end',
    'multiple components', 'entire system', 'cross-platform'
  ];

  // Moderate level indicators
  const moderateIndicators = [
    'function', 'feature', 'component', 'module', 'refactor',
    'optimize', 'improve', 'enhance'
  ];

  if (expertIndicators.some(indicator => lowerMessage.includes(indicator)) || messageLength > 1000) {
    return 'expert';
  } else if (complexIndicators.some(indicator => lowerMessage.includes(indicator)) || messageLength > 500) {
    return 'complex';
  } else if (moderateIndicators.some(indicator => lowerMessage.includes(indicator)) || messageLength > 100) {
    return 'moderate';
  } else {
    return 'simple';
  }
}

/**
 * LLM-based intelligent task classification for agent recommendation
 * Supports both OpenAI (GPT-4.1-mini) and Gemini (2.5 Flash Lite) models
 */
async function classifyTaskWithLLM(
  userMessage: string,
  context: {
    planningNeeded: boolean;
    tokenEstimate: number;
    complexity: string;
    conversationHistory?: any[];
  }
): Promise<string> {
  const systemPrompt = `You are an expert task classifier for a multi-agent AI system. Your job is to analyze user requests and recommend the optimal agent(s) to handle them.

AVAILABLE AGENTS:
- PlanningAgent: Multi-step planning and task breakdown
- TaskOrchestrator: Large/complex tasks requiring distributed processing (>20k tokens)
- ResearchAgent: Information gathering, web research, analysis
- CodeGenerationAgent: Writing, implementing, creating code/applications
- DebugAgent: Fixing errors, troubleshooting, debugging code
- SecurityAnalysisAgent: Security audits, vulnerability assessment, compliance
- WeatherAgent: Weather-related queries and monitoring
- ReminderAgent: Scheduling, reminders, monitoring tasks

CLASSIFICATION RULES:
1. If planningNeeded=true OR task involves multiple complex steps → PlanningAgent
2. If tokenEstimate > 20000 OR task involves >5 files → TaskOrchestrator
3. If task is primarily about research/investigation → ResearchAgent
4. If task involves writing/implementing code → CodeGenerationAgent
5. If task involves fixing bugs/errors → DebugAgent
6. If task involves security analysis → SecurityAnalysisAgent
7. If task involves weather → WeatherAgent
8. If task involves reminders/scheduling → ReminderAgent
9. For simple coordination tasks → PlanningAgent

MULTIPLE AGENTS: If a task requires multiple agents, recommend the PRIMARY agent that should coordinate. Secondary agents will be delegated as needed.

RESPONSE FORMAT: Return ONLY the agent name (e.g., "CodeGenerationAgent"). Do not include explanations or reasoning.`;

  const userPrompt = `Classify this task:
USER REQUEST: "${userMessage}"

CONTEXT:
- Planning needed: ${context.planningNeeded}
- Token estimate: ${context.tokenEstimate}
- Complexity: ${context.complexity}
- Has conversation history: ${(context.conversationHistory?.length || 0) > 0}

Recommend the PRIMARY agent:`;

  const messages = [
    { role: 'system' as const, content: systemPrompt },
    { role: 'user' as const, content: userPrompt }
  ];

  const validAgents = [
    'DanteCore', 'PlanningAgent', 'TaskOrchestrator', 'ResearchAgent',
    'CodeGenerationAgent', 'DebugAgent', 'SecurityAnalysisAgent',
    'WeatherAgent', 'ReminderAgent'
  ];

  // Try Gemini 2.5 Flash Lite first (faster, more cost-effective for classification)
  if (geminiClient.isConfigured()) {
    try {
      const thinkingConfig = GeminiClient.createThinkingConfig('simple', false); // No thinking needed for classification

      const response = await geminiClient.createChatCompletion({
        model: 'gemini-2.5-flash-lite',
        messages,
        max_tokens: 50,
        thinking: thinkingConfig
      });

      const recommendedAgent = response.choices?.[0]?.message?.content?.trim();

      if (recommendedAgent && validAgents.includes(recommendedAgent)) {
        return recommendedAgent;
      }
    } catch (error) {
      console.warn('Gemini classification failed, trying OpenAI:', error);
    }
  }

  // Fallback to OpenAI GPT-4.1-mini
  try {
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const response = await openai.chat.completions.create({
      model: 'gpt-4o-mini', // Fast, efficient model for classification
      messages,
      max_tokens: 50, // Just need the agent name
    });

    const recommendedAgent = response.choices[0]?.message?.content?.trim();

    if (recommendedAgent && validAgents.includes(recommendedAgent)) {
      return recommendedAgent;
    }

    // Fallback logic if LLM classification fails
    console.warn('LLM classification failed or invalid, using fallback logic');
    return getFallbackAgentRecommendation(userMessage, context);

  } catch (error) {
    console.warn('Both Gemini and OpenAI classification failed, using fallback:', error);
    return getFallbackAgentRecommendation(userMessage, context);
  }
}

/**
 * Fallback agent recommendation logic
 */
function getFallbackAgentRecommendation(
  userMessage: string,
  context: {
    planningNeeded: boolean;
    tokenEstimate: number;
    complexity: string;
  }
): string {
  const lowerMessage = userMessage.toLowerCase();

  // Priority-based fallback logic
  if (context.planningNeeded) return 'PlanningAgent';
  if (context.tokenEstimate > 20000) return 'TaskOrchestrator';

  // Simple keyword matching as last resort
  if (lowerMessage.includes('research') || lowerMessage.includes('investigate')) return 'ResearchAgent';
  if (lowerMessage.includes('code') || lowerMessage.includes('implement')) return 'CodeGenerationAgent';
  if (lowerMessage.includes('debug') || lowerMessage.includes('fix') || lowerMessage.includes('error')) return 'DebugAgent';
  if (lowerMessage.includes('security') || lowerMessage.includes('vulnerability')) return 'SecurityAnalysisAgent';
  if (lowerMessage.includes('weather')) return 'WeatherAgent';
  if (lowerMessage.includes('reminder') || lowerMessage.includes('schedule')) return 'ReminderAgent';

  return 'TaskOrchestrator';
}

// Export all assessment tools
export const taskAssessmentTools = [
  assessTaskTool,
  checkPlanningNeedTool,
  estimateTaskComplexityTool,
  recommendModelForFileOperationTool,
];
