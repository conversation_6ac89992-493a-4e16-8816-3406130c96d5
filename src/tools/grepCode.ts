import { tool } from 'ai';
import { z } from 'zod';
import { promisify } from 'util';
import { exec } from 'child_process';
import * as path from 'path';

const execAsync = promisify(exec);

const IGNORED_DIRS = [
  'node_modules','dist','build','coverage','.next','.nuxt','out','public','static','assets','vendor','tmp','temp','lib'
];

function escapeSingleQuotes(s: string): string {
  // Safe single-quote shell escaping: ' -> '\''
  return s.replace(/'/g, "'\\''");
}

async function hasRg(): Promise<boolean> {
  try {
    await execAsync("bash -lc 'command -v rg > /dev/null && echo yes || echo no'");
    const { stdout } = await execAsync("bash -lc 'command -v rg >/dev/null 2>&1 && echo yes || echo no'" );
    return stdout.trim() === 'yes';
  } catch {
    return false;
  }
}

function buildRgCommand(params: any): string {
  const {
    pattern,
    searchPath,
    fileTypes = [],
    caseSensitive = false,
    maxResults = 200,
    contextLines = 0,
  } = params;

  const flags = [
    '--json',
    '-n',
    `-C ${Math.max(0, contextLines)}`,
    caseSensitive ? '' : '-i',
    `-m ${Math.max(1, Math.min(maxResults, 10000))}`,
    '--no-ignore-messages',
  ]
    .filter(Boolean)
    .join(' ');

  const globs = [
    ...IGNORED_DIRS.map(d => `-g '!${d}/**'`),
    ...(Array.isArray(fileTypes) && fileTypes.length > 0
      ? fileTypes.map((ext: string) => `-g '*.${ext.replace(/^\./, '')}'`)
      : []),
  ].join(' ');

  const pat = `'${escapeSingleQuotes(pattern)}'`;
  const base = `'${escapeSingleQuotes(path.resolve(searchPath || 'src'))}'`;
  return `bash -lc "rg ${flags} ${globs} -- ${pat} ${base}"`;
}

async function runGrepFallback(params: any) {
  const {
    pattern,
    searchPath,
    fileTypes = [],
    caseSensitive = false,
    maxResults = 200,
  } = params;

  const base = path.resolve(searchPath || 'src');
  const includeArgs = (Array.isArray(fileTypes) && fileTypes.length > 0)
    ? fileTypes.map((ext: string) => `--include='*.${ext.replace(/^\./, '')}'`).join(' ')
    : "";
  const excludeDirs = IGNORED_DIRS.map(d => `--exclude-dir='${d}'`).join(' ');
  const ci = caseSensitive ? '' : '-i';
  const pat = `'${escapeSingleQuotes(pattern)}'`;
  const cmd = `bash -lc "grep -R ${ci} -n ${excludeDirs} ${includeArgs} ${pat} '${escapeSingleQuotes(base)}' | head -n ${Math.max(1, Math.min(maxResults, 10000))}"`;
  const { stdout } = await execAsync(cmd);
  const lines = stdout.split('\n').filter(Boolean);
  const results: Array<{ file: string; line: number; match: string }> = [];
  for (const ln of lines) {
    const idx1 = ln.indexOf(':');
    if (idx1 === -1) continue;
    const idx2 = ln.indexOf(':', idx1 + 1);
    if (idx2 === -1) continue;
    const file = ln.slice(0, idx1);
    const lineStr = ln.slice(idx1 + 1, idx2);
    const rest = ln.slice(idx2 + 1);
    const lineNum = parseInt(lineStr, 10) || 0;
    results.push({ file, line: lineNum, match: rest.trim() });
  }
  return results;
}

export const grepCodeTool = tool({
  name: 'grep_code',
  description: 'Shell-backed code search using ripgrep (rg) when available, or grep fallback. Faster and more accurate for large repos. Returns matches and suggested regions. IMPORTANT: Use narrow, specific patterns to avoid dumping large amounts of content into context.',
  inputSchema: z.object({
    pattern: z.string().describe('Search pattern (regex supported by rg/grep)'),
    searchPath: z.string().optional().describe('Path to search (default: src)'),
    fileTypes: z.array(z.string()).optional().describe('File extensions to include (e.g., ["ts","tsx"])'),
    caseSensitive: z.boolean().optional().describe('Case sensitive search (default: false)'),
    contextLines: z.number().optional().describe('Context lines to report alongside matches (default: 0)'),
    windowBefore: z.number().optional().describe('Suggested lines before match to extract later (default: 10)'),
    windowAfter: z.number().optional().describe('Suggested lines after match to extract later (default: 30)'),
    maxResults: z.number().optional().describe('Max number of matches to return (default: 200, max: 500)')
  }) as any,
  execute: async (params: any) => {
    try {
      const useRg = await hasRg();
      const {
        pattern,
        searchPath = 'src',
        fileTypes = ['ts','tsx','js','jsx'],
        caseSensitive = false,
        contextLines = 0,
        windowBefore = 10,
        windowAfter = 30,
        maxResults = 200,
      } = params;

      // Enforce safe limits to prevent context overflow
      const SAFE_MAX_RESULTS = 500;
      if (maxResults > SAFE_MAX_RESULTS) {
        return {
          success: false,
          error: `Requested maxResults=${maxResults} exceeds safe limit of ${SAFE_MAX_RESULTS}. ` +
                 `Use a more specific pattern to narrow your search and avoid context overflow.`
        };
      }

      let matches: Array<{ file: string; line: number; match: string }>;

      if (useRg) {
        const cmd = buildRgCommand({ pattern, searchPath, fileTypes, caseSensitive, maxResults, contextLines });
        let stdout: string;
        try {
          const out = await execAsync(cmd);
          stdout = out.stdout;
        } catch (e: any) {
          // rg exits with code 1 when no matches; treat as an empty result (not an error)
          stdout = e?.stdout || '';
        }
        const lines = stdout.split('\n').filter(Boolean);
        const parsed: Array<{ type: string; data?: any }>= [];
        for (const ln of lines) {
          try { parsed.push(JSON.parse(ln)); } catch {}
        }
        matches = [];
        for (const ev of parsed) {
          if (ev.type === 'match' && ev.data) {
            const file = ev.data.path?.text || '';
            const line = ev.data.line_number || ev.data.line_number || 0;
            const text = ev.data.lines?.text?.trim() || '';
            matches.push({ file, line, match: text });
            if (matches.length >= maxResults) break;
          }
        }
      } else {
        try {
          matches = await runGrepFallback({ pattern, searchPath, fileTypes, caseSensitive, maxResults });
        } catch (e: any) {
          // grep exits with code 1 when no matches; treat as empty
          const stdout = e?.stdout || '';
          const lines = stdout.split('\n').filter(Boolean);
          const results: Array<{ file: string; line: number; match: string }> = [];
          for (const ln of lines) {
            const idx1 = ln.indexOf(':');
            if (idx1 === -1) continue;
            const idx2 = ln.indexOf(':', idx1 + 1);
            if (idx2 === -1) continue;
            const file = ln.slice(0, idx1);
            const lineStr = ln.slice(idx1 + 1, idx2);
            const rest = ln.slice(idx2 + 1);
            const lineNum = parseInt(lineStr, 10) || 0;
            results.push({ file, line: lineNum, match: rest.trim() });
          }
          matches = results;
        }
      }

      // Build suggested regions per file
      const byFile = new Map<string, Array<{ line: number; text: string }>>();
      for (const m of matches) {
        const arr = byFile.get(m.file) || [];
        arr.push({ line: m.line, text: m.match });
        byFile.set(m.file, arr);
      }

      const results: any[] = [];
      for (const [file, arr] of byFile.entries()) {
        arr.sort((a,b) => a.line - b.line);
        // Merge into windows
        const windows: Array<{ startLine: number; endLine: number }> = [];
        for (const m of arr) {
          const s = Math.max(1, m.line - Math.max(0, windowBefore));
          const e = Math.max(m.line, m.line + Math.max(0, windowAfter));
          const last = windows[windows.length - 1];
          if (!last || s > last.endLine + 2) {
            windows.push({ startLine: s, endLine: e });
          } else {
            last.endLine = Math.max(last.endLine, e);
          }
        }
        results.push({ file, totalMatches: arr.length, regions: windows, sample: arr.slice(0, 5) });
      }

      return {
        success: true,
        engine: useRg ? 'ripgrep' : 'grep',
        basePath: path.resolve(searchPath || 'src'),
        totalMatches: matches.length,
        files: results
      };
    } catch (error: any) {
      return {
        success: false,
        error: error?.message || 'grep_code failed'
      };
    }
  }
});

export const grepCodeTools = [grepCodeTool];
