/**
 * DanteOrchestrator - Unified orchestrator using Vercel AI SDK
 * This file now contains the full orchestrator implementation to avoid forking.
 * Use absolute paths when using file tools (e.g., read_file, file_edit).
 */

import { generateText, streamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';
import { anthropic } from '@ai-sdk/anthropic';
import { createOllama } from 'ai-sdk-ollama';
import { config } from '../utils/config';
import { memoryManager } from '../memory/MemoryManager';
import * as fsSync2 from 'fs';
import * as crypto from 'crypto';
import { MemoryType, MemoryPriority } from '../memory/types';
import { EventEmitter } from 'events';
import { registerAllAgents } from './BuiltInAgents';
import { appendStep, newRunId } from '../utils/stepAggregator';
import {
  rememberTool,
  recallTool,
  forgetTool,
  getMemoryStatsTool,
  contextualMemorySearchTool,
  learnFromErrorTool,
  initializeMemory
} from '../tools/memoryTools';
import { pdfExtractTool } from '../tools/pdfExtract';

import { AgentDefinition, AnalysisTurnState, OrchestratorOptions, ToolUsageStatsSnapshot, StreamStepAggregation, MODEL_MAP } from '@/types/orchestrator-types';
import { ConcurrencyQueue } from './helpers/queue';
import { routeToAgent } from './helpers/agent-actions';
import { executeWithAgent } from './agent-actions';
// Provider mapping
const ollama = createOllama({ baseURL: config.ollama.baseURL });
const PROVIDERS = { openai, google, anthropic, ollama } as const;

const MEMORY_TOOL_NAMES = new Set([
  'remember',
  'recall',
  'forget_memories',
  'get_memory_stats',
  'contextual_memory_search',
  'learn_from_error'
]);

export class DanteAIOrchestrator extends EventEmitter {
  public agents = new Map<string, AgentDefinition>();
  public currentModel: string;
  public globalTools: Record<string, any> = {};
  public recentToolArgs: Array<{ name: string; args: any; t: number }> = [];
  public recentMemoryNotes: Map<string, number> = new Map();
  // Track recent tool result previews per agent to enrich handoff summaries
  public recentToolResultsByAgent: Map<string, Array<{ name: string; preview: string; t: number }>> = new Map();
  // Track recent write/edit outcomes with basic success + diagnostics signal
  public recentWriteStatusByAgent: Map<string, Array<{ name: string; ok: boolean; path?: string; t: number }>> = new Map();
  // Track recent assertion outcomes (e.g., assert_file_contains) to allow verification-based acceptance
  public recentAssertStatusByAgent: Map<string, Array<{ ok: boolean; path?: string; t: number }>> = new Map();
  // Track recent diagnostics outcomes (e.g., diagnose_file_syntax ok flags) per agent
  public recentDiagnosticsOkByAgent: Map<string, Array<{ path?: string; ok: boolean; t: number }>> = new Map();
  // Track last known assistant final text per agent (non-stream runs)
  public lastAgentTextByAgent: Map<string, string> = new Map();
  // Track current projectId (working directory) when set_working_directory is called
  public currentProjectId?: string;

  // Budget repeated auto-repair attempts across nested retries/fallbacks within the same request
  public noWriteBudgetByKey: Map<string, { count: number; firstT: number; lastPath?: string }> = new Map();

  // Permit bounded read-only turns that demonstrably gather new context before enforcing write guards
  public analysisTurnAllowance: Map<string, AnalysisTurnState> = new Map();

  // Lightweight metrics and concurrency controls (from ModelOrchestrator)
  public activeRequests = new Map<string, { startTime: number; model: string; type: string }>();
  private queue: ConcurrencyQueue;

  private maxConcurrentRequests = 5;
  private modelMetrics = new Map<string, {
    requestCount: number;
    successCount: number;
    failureCount: number;
    averageResponseTime: number;
    totalTokensUsed: number;
    lastUsed: Date | null;
    errorRate: number;
  }>();
  private sessionContext: Map<string, { lastPlan?: any; completedSteps?: string[]; lastStrategy?: string; lastUpdated: number }> = new Map();

  constructor(defaultModel: string = 'gpt-5') {
    super();
    // Normalize possible Google-style ids coming from the UI (e.g. 'models/gemini-2.5-pro')
    this.currentModel = DanteAIOrchestrator.normalizeModelId(defaultModel);
    // Initialize concurrency queue helper
    this.queue = new ConcurrencyQueue({
      getActiveCount: () => this.activeRequests.size,
      getMaxConcurrent: () => this.maxConcurrentRequests,
      generateId: () => this.generateRequestId(),
      onError: (err) => console.error('Error in queued processor:', err),
    });
  }

  // Accept UI ids like 'models/gemini-2.5-pro' and normalize to our internal map keys
  static normalizeModelId(modelId: string | undefined): string {
    if (!modelId) return 'gpt-5';
    return modelId.startsWith('models/') ? modelId.slice('models/'.length) : modelId;
  }

  registerAgent(agent: AgentDefinition): void {
    this.agents.set(agent.name, agent);
    console.log(`✅ Registered agent: ${agent.name}`);
  }

  registerTools(tools: Record<string, any>): void {
    this.globalTools = { ...this.globalTools, ...tools };
    console.log(`🔧 Registered ${Object.keys(tools).length} global tools`);
  }


  // Internal generate wrapper so tests can stub and record attempt order without hitting providers.
  public async doGenerate(modelKey: string, cfg: any, stream: boolean): Promise<any> {
    if (stream) {
      return await streamText(cfg);
    }
    return await generateText(cfg);
  }

  // Attempt auto-retry for file_edit with unified_diff by escalating mergeStrategy.
  // Extracted from onStepFinish to improve readability and testability.
  public async _attemptUnifiedDiffAutoRetry(
    finalTools: Record<string, any>,
    agentName: string,
    argsObj: any,
    toolResult: any,
    summarize: (val: any, depth?: number) => any,
  ): Promise<void> {
    const baseArgs = { ...argsObj };
    const current = (baseArgs.mergeStrategy ? String(baseArgs.mergeStrategy) : (baseArgs.fuzzy ? 'fuzzy' : 'strict')) as 'strict'|'fuzzy'|'git_3way'|'conflict_markers';
    const order: Array<'strict'|'fuzzy'|'git_3way'|'conflict_markers'> = ['strict','fuzzy','git_3way','conflict_markers'];
    const startIdx = Math.min(order.indexOf(current) + 1, order.length - 1);
    for (let idx = startIdx; idx < order.length; idx++) {
      const strategy = order[idx];
      const retryArgs = { ...baseArgs, mergeStrategy: strategy } as any;
      if (strategy === 'fuzzy') retryArgs.fuzzy = true;
      if (strategy === 'strict') retryArgs.fuzzy = false;
      console.warn(`🔁 Auto-retrying file_edit unified_diff with mergeStrategy='${strategy}'`);
      // Find the tool implementation (validated tool has invoke; raw tool has execute)
      const t = (finalTools as any)['file_edit'];
      let retryRes: any;
      try {
        if (t?.invoke) {
          retryRes = await t.invoke({}, JSON.stringify(retryArgs));
        } else if (t?.execute) {
          retryRes = await t.execute(retryArgs);
        }
      } catch {
        retryRes = undefined;
      }
      if (retryRes && retryRes.success === true) {
        console.log(`✅ Unified diff auto-retry succeeded with mergeStrategy='${strategy}'`);
        // Record as a successful tool result in buffers
        try {
          const preview = JSON.stringify(summarize(retryRes));
          const buf2 = this.recentToolResultsByAgent.get(agentName) || [];
          buf2.push({ name: 'file_edit', preview: preview.length > 160 ? preview.slice(0,160)+'…' : preview, t: Date.now() });
          while (buf2.length > 10) buf2.shift();
          this.recentToolResultsByAgent.set(agentName, buf2);
        } catch {}
        // Write status bookkeeping
        try {
          const success = true;
          const diagOk = typeof retryRes?.diagnostics?.ok === 'boolean' ? !!retryRes.diagnostics.ok : true;
          const modifiedFlag = (typeof retryRes?.modified === 'boolean') ? !!retryRes.modified : true;
          const replacements = typeof retryRes?.replacements === 'number' ? retryRes.replacements : undefined;
          const notNoOp = modifiedFlag && (replacements == null || replacements > 0);
          let persistedOk = true;
          try {
            const p = typeof (retryRes?.path) === 'string' ? retryRes.path : undefined;
            const afterHash: string | undefined = retryRes?.hashes?.after;
            if (p && afterHash) {
              const data = fsSync2.readFileSync(p, 'utf-8');
              const h = crypto.createHash('sha256').update(data).digest('hex');
              if (h !== afterHash) persistedOk = false;
            }
          } catch { persistedOk = false; }
          const ok = success && diagOk && notNoOp && persistedOk;
          const path2 = typeof retryRes?.path === 'string' ? retryRes.path : undefined;
          const wb2 = this.recentWriteStatusByAgent.get(agentName) || [];
          wb2.push({ name: 'file_edit', ok, path: path2, t: Date.now() });
          while (wb2.length > 20) wb2.shift();
          this.recentWriteStatusByAgent.set(agentName, wb2);
        } catch {}
        // Replace the failed display value with the successful retry for downstream guards
        (toolResult as any).result = retryRes;
        break;
      }
    }
  }

  // Concurrency queue handled by ConcurrencyQueue helper (see src/agents/helpers/queue.ts)

  // --- Metrics and status ---
  getStatus(): {
    modelMetrics: Map<string, any>;
    activeRequests: number;
    queueLength: number;
    totalRequestsProcessed: number;
    averageResponseTime: number;
    systemHealth: 'healthy' | 'degraded' | 'critical';
  } {
    const totalRequests = Array.from(this.modelMetrics.values()).reduce((s, m) => s + m.requestCount, 0);
    const totalResp = Array.from(this.modelMetrics.values()).reduce((s, m) => s + m.averageResponseTime * m.requestCount, 0);
    const avg = totalRequests > 0 ? totalResp / totalRequests : 0;
    const maxErr = Math.max(0, ...Array.from(this.modelMetrics.values()).map(m => m.errorRate || 0));
    const systemHealth: 'healthy' | 'degraded' | 'critical' = maxErr > 0.6 ? 'critical' : maxErr > 0.25 ? 'degraded' : 'healthy';
    return {
      modelMetrics: new Map(this.modelMetrics),
      activeRequests: this.activeRequests.size,
      queueLength: this.queue.getQueueLength(),
      totalRequestsProcessed: totalRequests,
      averageResponseTime: avg,
      systemHealth,
    };
  }

  // Centralized initializer for model metrics to avoid duplication
  private _getOrCreateModelMetrics(model: string): {
    requestCount: number;
    successCount: number;
    failureCount: number;
    averageResponseTime: number;
    totalTokensUsed: number;
    lastUsed: Date | null;
    errorRate: number;
  } {
    const existing = this.modelMetrics.get(model);
    if (existing) return existing;
    const m = {
      requestCount: 0,
      successCount: 0,
      failureCount: 0,
      averageResponseTime: 0,
      totalTokensUsed: 0,
      lastUsed: null as Date | null,
      errorRate: 0,
    };
    this.modelMetrics.set(model, m);
    return m;
  }

  private recordRequestSuccess(model: string, processingTime: number, tokens: number = 0): void {
    const m = this._getOrCreateModelMetrics(model);
    m.requestCount++;
    m.successCount++;
    m.totalTokensUsed += Math.max(0, tokens || 0);
    m.averageResponseTime = m.averageResponseTime + (processingTime - m.averageResponseTime) / m.requestCount;
    m.lastUsed = new Date();
    m.errorRate = m.requestCount > 0 ? m.failureCount / m.requestCount : 0;
    this.modelMetrics.set(model, m);
  }

  private recordRequestFailure(model: string, _error: Error, processingTime: number): void {
    const m = this._getOrCreateModelMetrics(model);
    m.requestCount++;
    m.failureCount++;
    m.averageResponseTime = m.averageResponseTime + (processingTime - m.averageResponseTime) / m.requestCount;
    m.lastUsed = new Date();
    m.errorRate = m.requestCount > 0 ? m.failureCount / m.requestCount : 0;
    this.modelMetrics.set(model, m);
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`;
  }

  async persistTaskSnapshot(agentName: string, input: string, status: 'checkpoint' | 'handoff' | 'completed') {
    try {
      const now = new Date();
      const recentTools = Array.from(new Set(this.recentToolArgs.slice(-10).map(e => e.name)));
      const recentResults = (this.recentToolResultsByAgent.get(agentName) || []).slice(-3);
      const lastNote = this.lastAgentTextByAgent.get(agentName) || '';
      const objective = String(input).replace(/\s+/g, ' ').trim();
      const content = {
        kind: 'task_summary',
        objective,
        agent: agentName,
        status,
        recentTools,
        recentResults,
        lastAssistantNote: lastNote,
        timestamp: now.toISOString(),
      };

      await memoryManager.create(
        MemoryType.SEMANTIC,
        content,
        {
          tags: ['task', 'summary', 'task-summary', 'searchable', agentName],
          source: `orchestrator_${status}`,
          confidence: 0.9,
          priority:  MemoryPriority.MEDIUM,
          projectId: this.currentProjectId,
          ttl: 90 * 24 * 60 * 60 * 1000 // 90 days
        } as any
      );
      console.log(`🧠 Persisted ${status} task snapshot (${agentName})${this.currentProjectId ? ` for project ${this.currentProjectId}` : ''}`);
    } catch (e) {
      console.warn('Failed to persist task snapshot:', e);
    }
  }

  // --- Session plan helpers (for follow-ups like "proceed") ---
  public setSessionPlan(sessionId: string, plan: any): void { this.upsertSessionPlan(sessionId, plan); }
  public getSessionPlan(sessionId: string): any | undefined { return this.sessionContext.get(sessionId)?.lastPlan; }
  public markStepCompleted(sessionId: string, stepId: string): void { this.markStepComplete(sessionId, stepId); }
  private async upsertSessionPlan(sessId: string, plan: any): Promise<void> {
    try {
      if (sessId && plan) {
        const ctx = this.sessionContext.get(sessId) || ({ lastUpdated: Date.now() } as any);
        ctx.lastPlan = plan; ctx.lastStrategy = 'guided_pipeline'; ctx.lastUpdated = Date.now();
        if (!Array.isArray(ctx.completedSteps)) ctx.completedSteps = [];
        this.sessionContext.set(sessId, ctx);
      }
    } catch (e) { console.error(`Failed to upsert session plan for session ${sessId}:`, e); }
  }
  private async markStepComplete(sessId: string, stepId: string): Promise<void> {
    try {
      const ctx = this.sessionContext.get(sessId) || ({ lastUpdated: Date.now() } as any);
      if (!Array.isArray(ctx.completedSteps)) ctx.completedSteps = [];
      if (!ctx.completedSteps.includes(stepId)) ctx.completedSteps.push(stepId);
      ctx.lastUpdated = Date.now();
      this.sessionContext.set(sessId, ctx);
    } catch (e) { console.error(`Failed to mark step ${stepId} as complete for session ${sessId}:`, e); }
  }

  private async _processInternal(input: string, options: OrchestratorOptions = {}): Promise<any> {
    const runId = newRunId('orchestrator');
    const requestId = this.generateRequestId();
    const startTime = Date.now();
    try { this.activeRequests.set(requestId, { startTime, model: this.currentModel, type: 'unknown' }); } catch {}
    // Route using provided routeText (typically last user message) when available
    const routeBasis = options.routeText && typeof options.routeText === 'string' && options.routeText.trim().length > 0
      ? options.routeText
      : input;
    const { agent: agentName, confidence } = await routeToAgent(routeBasis);
    console.log(`📊 Routing to ${agentName} (confidence: ${(confidence * 100).toFixed(0)}%)`);
    this.emit('agentSelected', { agent: agentName, confidence, input });
    try { options.onAgentSelected?.({ agent: agentName, confidence }); } catch (e) { console.warn('onAgentSelected callback error:', e); }
    // Wrap onStepFinish to also record steps for UI
    const wrappedOptions = {
      ...options,
      background: (options as any).background === true || this.isBackgroundTask(routeBasis, agentName),
      onStepFinish: (step: any) => {
        try { appendStep(runId, { t: Date.now(), agent: agentName, step }); } catch {}
        try { options.onStepFinish?.(step); } catch {}
      }
    };
    let result: any;
    try {
      result = await executeWithAgent(agentName, input, wrappedOptions, { orchestratorInstance: this });
      const processingTime = Date.now() - startTime;
      this.recordRequestSuccess(this.currentModel, processingTime, 0);
      this.emit('requestCompleted', { requestId, model: this.currentModel, processingTime, success: true });
    } catch (e) {
      const processingTime = Date.now() - startTime;
      this.recordRequestFailure(this.currentModel, e as Error, processingTime);
      this.emit('requestFailed', { requestId, model: this.currentModel, error: e instanceof Error ? e.message : String(e), processingTime });
      throw e;
    } finally {
      try { this.activeRequests.delete(requestId); } catch {}
      // In case there are queued requests waiting for capacity
      try { this.queue.drain().catch(() => {}); } catch {}
    }
    const asAny = result as any;
    if (asAny && asAny.textStream && typeof asAny.textStream[Symbol.asyncIterator] === 'function') return result;
    if (asAny && typeof asAny[Symbol.asyncIterator] === 'function') return result;
    if (asAny && 'text' in asAny) {
      // If text is empty but we have toolCalls (e.g., answer tool only), surface a helpful log
      try {
        if ((typeof asAny.text !== 'string' || asAny.text.trim().length === 0) && Array.isArray(asAny.toolCalls) && asAny.toolCalls.length > 0) {
          console.log(`ℹ️ [Orchestrator] Empty text with ${asAny.toolCalls.length} toolCalls — UI should read structured fields/toolCalls.`);
        }
      } catch {}
      return { text: asAny.text, agent: agentName, toolCalls: asAny.toolCalls, usage: asAny.usage, steps: asAny.steps, runId };
    }
    return { ...result, runId };
  }

  async process(input: string, options: OrchestratorOptions = {}): Promise<any> {
    // If we're at capacity, enqueue the request
    if (this.activeRequests.size >= this.maxConcurrentRequests) {
      // Basic priority heuristic: shorter maxSteps gets slightly higher priority
      const priority = Math.max(1, Math.min(3, (options.maxSteps && options.maxSteps <= 6) ? 2 : 1));
      return this.queue.enqueue(priority, () => this._processInternal(input, options));
    }
    return this._processInternal(input, options);
  }

  switchModel(model: string): void {
    const normalized = DanteAIOrchestrator.normalizeModelId(model);
    if (!MODEL_MAP[normalized]) throw new Error(`Model ${model} not supported`);
    this.currentModel = normalized;
    console.log(`🔄 Switched to model: ${model}`);
  }

  getAgents(): AgentDefinition[] { return Array.from(this.agents.values()); }

  getAgentMap(): Map<string, AgentDefinition> { return new Map(this.agents); }

  public analysisTurnKey(agentName: string, options: OrchestratorOptions, input: string): string | null {
    try {
      const root = this.currentProjectId || process.cwd();
      const route = (options.routeText || '').slice(0, 160);
      const session = options.sessionId ? `|session:${options.sessionId}` : '';
      const payload = `${agentName}|${root}${session}|${route}|${String(input || '').slice(0, 256)}`;
      return crypto.createHash('sha256').update(payload).digest('hex');
    } catch {
      return null;
    }
  }

  public resetAnalysisProgress(key: string | null): void {
    if (!key) return;
    this.analysisTurnAllowance.delete(key);
  }

  public maybeAllowContextGatheringTurn(
    key: string | null,
    stats: ToolUsageStatsSnapshot,
    agentName: string,
  ): boolean {
    if (!key) return false;

    const parsedLimit = Number.parseInt(process.env.NO_WRITE_ANALYSIS_GRACE ?? '5', 10);
    const limit = Number.isFinite(parsedLimit) && parsedLimit >= 0 ? parsedLimit : 5;
    if (limit === 0) {
      this.analysisTurnAllowance.delete(key);
      return false;
    }

    const now = Date.now();
    let state = this.analysisTurnAllowance.get(key);
    if (state && now - state.lastUpdated > 10 * 60_000) {
      this.analysisTurnAllowance.delete(key);
      state = undefined;
    }
    if (!state) {
      state = {
        count: 0,
        readPaths: new Set<string>(),
        listDirs: new Set<string>(),
        totalToolCalls: 0,
        lastUpdated: now,
      };
    }

    const readPaths = Array.from(stats.readFile.paths.keys()).filter((p): p is string => typeof p === 'string' && p.length > 0);
    const dirPaths = Array.from(stats.listDirectory.paths.keys()).filter((p): p is string => typeof p === 'string' && p.length > 0);
    const introducedRead = readPaths.some((p) => !state.readPaths.has(p));
    const introducedDir = dirPaths.some((p) => !state.listDirs.has(p));
    const toolDelta = stats.totalCalls > state.totalToolCalls;

    state.lastUpdated = now;
    state.totalToolCalls = Math.max(state.totalToolCalls, stats.totalCalls);
    readPaths.forEach((p) => state.readPaths.add(p));
    dirPaths.forEach((p) => state.listDirs.add(p));

    const hasContextActivity = stats.readFile.total > 0 || stats.listDirectory.total > 0 || stats.totalCalls > 0;
    const qualifies = stats.fileEdit === 0 && hasContextActivity && (introducedRead || introducedDir || toolDelta);

    if (!qualifies) {
      this.analysisTurnAllowance.set(key, state);
      return false;
    }

    if (state.count >= limit) {
      this.analysisTurnAllowance.delete(key);
      return false;
    }

    state.count += 1;
    this.analysisTurnAllowance.set(key, state);
    try {
      console.log(`ℹ️ Skipping NO_WRITE_CONFIRMED guard: read-only progress turn ${state.count}/${limit} for ${agentName}.`);
    } catch {}
    return true;
  }

  private isBackgroundTask(text?: string, agentName?: string): boolean {
    try {
      if (agentName === 'IsolatedComputerUseAgent') return true;
      const s = String(text || '').toLowerCase();
      const hints = [
        'summarize','summary','summarization',
        'classify','classification','categorize',
        'tag','label','cluster','batch',
        'non-interactive','non interactive','background','headless',
        'periodically','cron','schedule',
        'embed','embeddings','embedding','vectorize',
        'extract','sentiment','digest','index'
      ];
      return hints.some(h => s.includes(h));
    } catch {
      return false;
    }
  }


}

// Create the main orchestrator instance
const orchestrator = new DanteAIOrchestrator(DanteAIOrchestrator.normalizeModelId(config.openai?.defaultModel || 'gpt-5'));

// Register all agents
registerAllAgents(orchestrator);

// Register global memory tools so all agents can access them
orchestrator.registerTools({
  remember: rememberTool,
  recall: recallTool,
  forget_memories: forgetTool,
  get_memory_stats: getMemoryStatsTool,
  contextual_memory_search: contextualMemorySearchTool,
  learn_from_error: learnFromErrorTool,
  // PDF extraction tool available to all agents
  pdf_extract: pdfExtractTool,
});

// Initialize memory system (non-blocking)
void initializeMemory().catch((e) => console.warn('Memory init failed:', e));

// Compatibility wrapper for legacy callers
const DanteOrchestrator = {
  name: 'DanteOrchestrator',
  model: orchestrator.currentModel,
  tools: [] as any[],
  async execute(input: string | any[], options?: any) {
    const inputStr = Array.isArray(input) ? input.map(i => typeof i === 'string' ? i : JSON.stringify(i)).join('\n') : input;
    // Prefer routing STRICTLY on the latest user message to avoid pollution from prior assistant/system content
    let routeText: string | undefined = undefined;
    if (Array.isArray(input)) {
      try {
        const messages = input as any[];
        const userMsgs = messages.filter(
          (m) => typeof m === 'object' && m && (m as any).role === 'user'
        );
        const lastUser = userMsgs[userMsgs.length - 1];
        if (lastUser) {
          const userContent = typeof (lastUser as any).content === 'string'
            ? ((lastUser as any).content as string)
            : undefined;
          // Route ONLY on the user's last message
          if (userContent) {
            routeText = userContent;
          }
        }
      } catch {}
    }
    return await orchestrator.process(inputStr, {
      stream: options?.stream,
      maxSteps: options?.maxTurns,
      onStepFinish: options?.onStepFinish,
      onAgentSelected: options?.onAgentSelected,
      routeText
    });
  },
  setModel(model: string) { orchestrator.switchModel(model); (this as any).model = model; },
  getAgents() { return orchestrator.getAgents(); },
  getAgentMap() { return orchestrator.getAgentMap(); }
};

export default DanteOrchestrator;
export { orchestrator };
