/**
 * AutoFollowUpManager
 *
 * Programmatic workflow that takes initiative to:
 * 1) Research external docs relevant to the request ("Here's what I found…")
 * 2) Analyze the local codebase for related implementation points
 * 3) Create a plan and delegate to specialized agents to execute
 *
 * The function returns a structured object (non-stream) suitable for
 * EnhancedStreamHandler to surface via SSE when called as a tool.
 */

import { orchestrator } from './DanteOrchestrator';
import { checkCancellation } from '../utils/cancel';
import { executeWithAgent } from './agent-actions';
import { planAndDelegate } from './helpers/planning';
import { AgentDefinition } from '@/types/orchestrator-types';

type ResearchOutput = {
  summary?: string;
  keyFindings?: string[];
  sources?: Array<{ title?: string; url?: string; note?: string }>;
  insights?: string[];
  nextSteps?: string[];
};

export interface AutoFollowUpResult {
  // Stage 1 – external docs
  docResearch?: ResearchOutput;
  docSummaryText?: string; // friendly text for UI: "Here's what I found…"

  // Stage 2 – codebase mapping
  codebaseResearch?: ResearchOutput;
  codebaseSummaryText?: string;

  // Stage 3 – planning + delegation (compatible with EnhancedStreamHandler bridges)
  plan?: { summary?: string; steps: Array<{ id: string; agent: string; dependsOn?: string[]; title?: string; description?: string }>; };
  stepResults?: Array<{ stepId: string; agent: string; title?: string; description?: string; success: boolean }>;

  // Final synthesized message from orchestration (optional)
  finalMessage?: string;
}

export interface AutoFollowUpOptions {
  sessionId?: string;
  context?: any;
}

/**
 * Build a short, friendly bullet summary from a research object.
 */
function renderResearchSummary(prefix: string, r?: ResearchOutput): string | undefined {
  if (!r) return undefined;
  const lines: string[] = [];
  if (r.summary) lines.push(r.summary.trim());
  if (Array.isArray(r.keyFindings) && r.keyFindings.length) {
    lines.push('Key findings:');
    for (const k of r.keyFindings.slice(0, 6)) lines.push(`- ${k}`);
  }
  if (Array.isArray(r.sources) && r.sources.length) {
    lines.push('Top sources:');
    for (const s of r.sources.slice(0, 4)) {
      const title = s.title ? `${s.title} — ` : '';
      lines.push(`- ${title}${s.url ?? ''}${s.note ? ` (${s.note})` : ''}`);
    }
  }
  if (lines.length === 0) return undefined;
  return `${prefix}\n${lines.join('\n')}`;
}

/**
 * Extract a structured research payload if present in a ResearchAgent result.
 */
function extractStructuredResearch(res: any): ResearchOutput | undefined {
  try {
    const structured = res?.structuredResearch;
    if (structured && (structured.summary || structured.keyFindings || structured.sources)) {
      return structured as ResearchOutput;
    }
  } catch (e) {
    console.warn('Failed to extract structured research:', e);
  }
  return undefined;
}

/**
 * Execute the autonomous follow-up workflow end-to-end.
 * Returns a JSON object that the SSE layer can interpret to emit user-facing events.
 */
export async function executeAutoFollowUp(request: string,  options: AutoFollowUpOptions = {}): Promise<AutoFollowUpResult> {
  const out: AutoFollowUpResult = {};

  // Heuristic signals for external research (installation/integration/setup for unknown tech)
  const lower = request.toLowerCase();
  const looksLikeSetup = /(install|setup|set up|integrate|integration|configure|deployment|deploy|cli|sdk|connect|enable|how to)/.test(lower);

  // Stage 1: External documentation research
  if (looksLikeSetup) {
    checkCancellation(false);
    const researchPrompt = `You are researching external documentation to enable the requested task.
User request: "${request}"

Do the following:
1) Identify the official docs or credible sources relevant to this task
2) Summarize the exact steps needed
3) Provide links (URLs) and any version/OS caveats
4) Keep it project-specific where possible

Return your findings via the 'answer' tool in a structured format.`;

    const researchRes = await executeWithAgent('ResearchAgent', researchPrompt, {
      stream: false,
      maxSteps: 12,
    }, {orchestratorInstance: orchestrator} as any);
    out.docResearch = extractStructuredResearch(researchRes);
    out.docSummaryText = renderResearchSummary("Here's what I found about the required setup:", out.docResearch)
      || (typeof researchRes?.text === 'string' ? researchRes.text : undefined);
  }

  // Stage 2: Codebase mapping – find where changes likely go
  checkCancellation(false);
  {
    const codePrompt = `Search the local codebase for the best insertion points or relevant modules for this request.
Request: "${request}"

Use grep_code and read_file/read_files to:
- Identify relevant files/directories (return exact project-relative paths)
- Show narrow snippets that justify why they are relevant
- Note any existing abstractions or patterns we should follow

Return your findings via the 'answer' tool in a structured format.`;

    const codeRes = await executeWithAgent('ResearchAgent', codePrompt, {
      stream: false,
      maxSteps: 10,
    }, {orchestratorInstance: orchestrator} as any);
    out.codebaseResearch = extractStructuredResearch(codeRes);
    out.codebaseSummaryText = renderResearchSummary('Codebase mapping summary:', out.codebaseResearch)
      || (typeof codeRes?.text === 'string' ? codeRes.text : undefined);
  }

  // Stage 3: Planning + Delegation – leverage TaskOrchestrator for structured plan and execution
  checkCancellation(false);
  {
    // Compose a context-aware orchestration input that includes what we learned
    const preface: string[] = [];
    if (out.docSummaryText) preface.push(out.docSummaryText);
    if (out.codebaseSummaryText) preface.push(out.codebaseSummaryText);
    const orchestrationInput = [
      'Objective:', request,
      preface.length ? '\n\nContext learned:\n' + preface.join('\n\n') : ''
    ].join('\n');

    // Run TaskOrchestrator without streaming and capture plan + step outcomes
    // so server can surface plan/delegation events like delegate_to_agents does.
    const orchestration = await planAndDelegate(orchestrationInput, {
      stream: false,
      maxSteps: 15,
      context: options.context,
      maxWorkers: 3,
      orchestratorInstance: orchestrator,
    });

    try {
      // Attempt to extract plan + results if present (executeTaskOrchestrator may return various shapes)
      const obj = typeof orchestration === 'string' ? JSON.parse(orchestration) : orchestration;
      if (obj?.plan && Array.isArray(obj.plan.steps)) {
        out.plan = { summary: obj.plan.summary, steps: obj.plan.steps };
      }
      if (Array.isArray(obj?.stepResults)) {
        out.stepResults = obj.stepResults.map((s: any) => ({ stepId: s.stepId, agent: s.agent, title: s.title, description: s.description, success: !!s.success }));
      }
      const fm = obj?.finalMessage || obj?.text;
      if (typeof fm === 'string') out.finalMessage = fm;
    } catch {
      // If not JSON, fall back to textual message
      if (typeof orchestration === 'string') out.finalMessage = orchestration;
    }
  }

  return out;
}

export default {
  executeAutoFollowUp,
};
