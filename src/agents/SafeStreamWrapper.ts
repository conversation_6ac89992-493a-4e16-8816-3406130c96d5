import { extractToolCallId } from "./helpers/formatting";

/**
 * Safe Stream Wrapper
 *
 * Provides memory-safe stream handling for Vercel AI SDK streams
 * and normalizes events so downstream consumers (tests/UI) can rely
 * on:
 *  - raw_model_stream_event for text deltas
 *  - tool_call / tool_result for tool invocations
 *  - final_result then stream_complete at end
 */
export class SafeStreamWrapper {
  private destroyed = false;
  private chunks: string[] = [];
  private maxChunks: number; // Limit stored chunks to prevent memory issues

  constructor(maxChunks?: number) {
    // Allow configuration via env; default higher to avoid premature truncation on large-context models
    const envLimit =
      typeof process !== 'undefined' &&
      (process as any).env &&
      (process as any).env.STREAM_MAX_CHUNKS
        ? parseInt(String((process as any).env.STREAM_MAX_CHUNKS), 10)
        : NaN;
    const fallback = 20000; // higher default to support 1M-context models like Gemini 2.5
    this.maxChunks =
      Number.isFinite(envLimit) && envLimit > 0
        ? envLimit
        : maxChunks && maxChunks > 0
        ? maxChunks
        : fallback;
  }

  /**
   * Wrap a Vercel AI SDK stream to make it memory-safe and normalize events
   */
  static wrapStream(stream: any, workerName: string, options?: { maxChunks?: number }): any {
    const wrapper = new SafeStreamWrapper(options?.maxChunks);

    const normalizeToolCallId = (value: unknown): string | undefined => {
      if (typeof value === 'string') {
        const trimmed = value.trim();
        return trimmed.length > 0 ? trimmed : undefined;
      }
      if (typeof value === 'number' && Number.isFinite(value)) return String(value);
      if (typeof value === 'bigint') return value.toString();
      return undefined;
    };

    // Expose a 'completed' promise so downstream can distinguish from raw-chat streams
    let resolveCompleted: () => void;
    const completed = new Promise<void>((resolve) => {
      resolveCompleted = resolve;
    });

    const result: any = {
      completed,
      [Symbol.asyncIterator]: async function* () {
        // Track across the entire stream
        let emittedText = false;
        let lastMemorySummary: string | null = null;

        try {
          // Add agent identification
          yield {
            type: 'agent_updated_stream_event',
            agent: { name: workerName },
          } as any;

          // Prefer structured fullStream when available to preserve tool events
          const full = (stream as any)?.fullStream;
          if (full && typeof full[Symbol.asyncIterator] === 'function') {
            let chunkCount = 0;
            let lastToolName: string | null = null;
            let lastToolId: string | null = null;

            for await (const ev of full) {
              if (wrapper.destroyed) break;

              chunkCount++;
              if (chunkCount > wrapper.maxChunks) {
                console.warn(`⚠️ Stream chunk limit reached for ${workerName}, stopping stream`);
                break;
              }

              if (ev && typeof ev === 'object') {
                const anyEv: any = ev;

                // Pass through normalized raw text events (and usage) as-is
                if (anyEv.type === 'raw_model_stream_event') {
                  const d = anyEv?.data;
                  if (
                    d &&
                    (d.type === 'output_text_delta' || d.type === 'output_text') &&
                    typeof (d.delta ?? d.text) === 'string'
                  ) {
                    emittedText = true;
                    lastMemorySummary = String(d.delta ?? d.text);
                  }
                  yield { ...anyEv };
                  continue;
                }

                // Map Vercel run item events into normalized tool/text events
                if (anyEv.type === 'run_item_stream_event' && anyEv.item) {
                  const item: any = anyEv.item;

                  // Tool call
                  if (item.type === 'tool_call_item') {
                    const tName: string | undefined =
                      item.name || item.tool_name || item.function?.name || item.rawItem?.name;
                    const tArgs: any =
                      item.arguments || item.args || item.function?.arguments || item.rawItem?.arguments;
                    const toolCallId = extractToolCallId(item) ?? extractToolCallId(anyEv);

                    if (tName) lastToolName = tName;
                    lastToolId = toolCallId ?? null;

                    const toolCallData: any = { name: tName || 'unknown_tool', args: tArgs };
                    if (toolCallId) {
                      toolCallData.id = toolCallId;
                      toolCallData.toolCallId = toolCallId;
                      toolCallData.callId = toolCallId;
                    }

                    yield {
                      type: 'tool_call',
                      data: toolCallData,
                    } as any;
                    continue;
                  }

                  // Tool result
                  if (item.type === 'tool_call_output' || item.type === 'tool_output') {
                    const rName: string | undefined =
                      item.name || item.tool_name || lastToolName || item.rawItem?.name;
                    const rawVal: any =
                      item.output ?? item.result ?? item.content ?? item.rawItem?.output;
                    const toolCallId =
                      extractToolCallId(item) ?? extractToolCallId(anyEv) ?? lastToolId ?? undefined;

                    // Emit normalized tool_result event
                    const resultString = typeof rawVal === 'string' ? rawVal : JSON.stringify(rawVal);
                    const toolResultData: any = { name: rName || 'tool', result: resultString };
                    if (toolCallId) {
                      toolResultData.id = toolCallId;
                      toolResultData.toolCallId = toolCallId;
                      toolResultData.callId = toolCallId;
                    }
                    yield {
                      type: 'tool_result',
                      data: toolResultData,
                    } as any;

                    // Opportunistic minimal user-facing synthesis (memory tools -> produce readable text)
                    try {
                      let payload: any = rawVal;
                      if (typeof payload === 'string') {
                        try {
                          payload = JSON.parse(payload);
                        } catch {
                          // ignore
                        }
                      }

                      let summary = '';
                      // Prefer explicit message from tool payload
                      if (payload && typeof payload.message === 'string' && payload.message.trim()) {
                        summary = payload.message.trim();
                      } else {
                        const toolLc = String(rName || '').toLowerCase();
                        const looksMemory =
                          toolLc.includes('recall') ||
                          toolLc.includes('contextual_memory_search') ||
                          toolLc.includes('memory');

                        if (looksMemory) {
                          const memField = payload?.memories;
                          const sample = Array.isArray(memField)
                            ? memField
                            : Array.isArray(memField?.sample)
                            ? memField.sample
                            : [];
                          const count = Array.isArray(sample)
                            ? sample.length
                            : typeof memField?.length === 'number'
                            ? memField.length
                            : 0;

                          if (count === 0) {
                            summary =
                              "I don't have specific memories about you yet. Share details you'd like me to remember.";
                          } else {
                            // Build a short preview from first 1–2 items (surface specifics like 'TypeScript' / 'dante-gpt')
                            const previews: string[] = [];
                            for (const m of sample.slice(0, 2)) {
                              const c: any = m?.content ?? {};
                              if (typeof c === 'string') previews.push(c.slice(0, 120));
                              else if (c.preference) previews.push(String(c.preference));
                              else if (c.data) previews.push(String(c.data));
                              else if (c.summary) previews.push(String(c.summary));
                            }
                            const joined = previews.filter(Boolean).join('; ');
                            summary = joined
                              ? `I remember ${count} things about you: ${joined}`
                              : `I remember ${count} things about you.`;
                          }
                        }
                      }

                      if (summary) {
                        emittedText = true;
                        lastMemorySummary = summary;
                        yield {
                          type: 'raw_model_stream_event',
                          data: { type: 'output_text_delta', delta: summary },
                        } as any;
                      }
                    } catch {
                      // Never fail the stream on synthesis
                    }

                    // reset pairing hint after result
                    lastToolName = null;
                    lastToolId = null;
                    continue;
                  }

                  // Assistant/user text surfaced via run items -> convert to raw text delta
                  if (
                    item.type === 'text' ||
                    item.type === 'message' ||
                    item.type === 'assistant_message'
                  ) {
                    const textContent: string | undefined =
                      item.text || item.content || item.message;
                    if (typeof textContent === 'string' && textContent.length > 0) {
                      emittedText = true;
                      lastMemorySummary = textContent;
                      yield {
                        type: 'raw_model_stream_event',
                        data: { type: 'output_text_delta', delta: textContent },
                      } as any;
                    }
                    continue;
                  }

                  // Unknown run item -> pass through for visibility
                  yield { ...anyEv };
                  continue;
                }

                // Some providers emit plain "message" events with content
                if (anyEv.type === 'message' && typeof anyEv.data?.message === 'string') {
                  emittedText = true;
                  lastMemorySummary = anyEv.data.message;
                  yield {
                    type: 'raw_model_stream_event',
                    data: { type: 'output_text_delta', delta: anyEv.data.message },
                  } as any;
                  continue;
                }

                // Fallback: pass through unknown objects
                yield { ...anyEv };
                continue;
              } else if (typeof ev === 'string') {
                // Normalize stray text chunks into raw model stream events
                emittedText = true;
                lastMemorySummary = ev;
                yield {
                  type: 'raw_model_stream_event',
                  data: { type: 'output_text_delta', delta: ev },
                } as any;
              }

              if (chunkCount % 100 === 0) {
                wrapper.clearOldChunks();
              }
            }
          }
          // Fallback: textStream from Vercel AI SDK (only text deltas)
          else if (stream && stream.textStream && typeof stream.textStream[Symbol.asyncIterator] === 'function') {
            let chunkCount = 0;
            for await (const chunk of stream.textStream) {
              if (wrapper.destroyed) break;

              chunkCount++;
              if (chunkCount > wrapper.maxChunks) {
                console.warn(`⚠️ Stream chunk limit reached for ${workerName}, stopping stream`);
                break;
              }

              if (typeof chunk === 'string' && chunk.length > 0) {
                emittedText = true;
                lastMemorySummary = chunk;
                // Emit Agent SDK-compatible raw model stream event for text
                yield {
                  type: 'raw_model_stream_event',
                  data: { type: 'output_text_delta', delta: chunk },
                } as any;
              }

              if (chunkCount % 100 === 0) {
                wrapper.clearOldChunks();
              }
            }
          }
          // Handle raw async iterables (provider-native)
          else if (stream && typeof stream[Symbol.asyncIterator] === 'function') {
            let chunkCount = 0;
            for await (const chunk of stream) {
              if (wrapper.destroyed) break;

              chunkCount++;
              if (chunkCount > wrapper.maxChunks) {
                console.warn(`⚠️ Stream chunk limit reached for ${workerName}, stopping stream`);
                break;
              }

              if (typeof chunk === 'string') {
                emittedText = true;
                lastMemorySummary = chunk;
                yield {
                  type: 'raw_model_stream_event',
                  data: { type: 'output_text_delta', delta: chunk },
                } as any;
              } else if (chunk && typeof chunk === 'object') {
                // Pass through events (shallow copy) to avoid holding references
                yield { ...chunk };
              }

              if (chunkCount % 100 === 0) {
                wrapper.clearOldChunks();
              }
            }
          }

          // If no textual content ever arrived, synthesize from last memory summary (if any)
          if (!emittedText && lastMemorySummary) {
            yield {
              type: 'raw_model_stream_event',
              data: { type: 'output_text_delta', delta: lastMemorySummary },
            } as any;
            emittedText = true;
          }

          // Signal completion
          yield { type: 'final_result' } as any;
          yield { type: 'stream_complete' } as any;
        } catch (error) {
          console.error(`❌ Stream error for ${workerName}:`, error);
          yield {
            type: 'error',
            error: error instanceof Error ? error.message : String(error),
          } as any;
        } finally {
          // Cleanup
          wrapper.destroy();
          try {
            resolveCompleted!();
          } catch {}
        }
      },
    };

    // Pass through helpful properties when present
    try {
      (result as any).state = (stream as any).state;
      (result as any).history = (stream as any).history;
      (result as any).output = (stream as any).output;
      (result as any).input = (stream as any).input;
      (result as any).toTextStream = (stream as any).toTextStream?.bind(stream);
      // Preserve Vercel AI SDK-style textStream for compatibility with tests/UI
      (result as any).textStream = (stream as any).textStream;
      (result as any).usage = (stream as any).usage;
      (result as any).baseStream = (stream as any).baseStream;
    } catch {}

    return result;
  }

  /**
   * Clear old chunks to prevent memory accumulation
   */
  private clearOldChunks(): void {
    if (this.chunks.length > 100) {
      // Keep only last 50 chunks
      this.chunks = this.chunks.slice(-50);
    }
  }

  /**
   * Destroy the wrapper and free resources
   */
  private destroy(): void {
    this.destroyed = true;
    this.chunks = [];
  }

  /**
   * Check if a result is a Vercel AI SDK stream
   */
  static isVercelStream(result: any): boolean {
    return !!(
      result &&
      (typeof result.textStream !== 'undefined' ||
        (typeof result[Symbol.asyncIterator] === 'function' && (result as any).baseStream))
    );
  }
}
