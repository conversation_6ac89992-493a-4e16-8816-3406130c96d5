/**
 * Planning Agent - Vercel AI SDK Implementation
 * Handles task breakdown, planning, and execution strategy development
 */

import { generateText, streamText, tool, stepCountIs } from 'ai';
import { openai } from '@ai-sdk/openai';
import { taskManagementTools } from '../tools/taskManagement';
import { fileOperationTools } from '../tools/fileOperations';
import { mapToolsByName } from '../utils/toolUtils';
import { z } from 'zod';
import { google } from '@ai-sdk/google';
import { getThinkingConfigForTask } from '../utils/geminiClient';
import { orchestrator } from './DanteOrchestrator';
import { inferLastAffectedPath } from './helpers/analysis';
import { summarizeAgentResult as summarizeAgentResultUtil } from '@utils/summarizeAgentResult';
import { executeWithAgent } from './agent-actions';
import { discoverContext, formatDiscoveryContext } from './helpers/discovery';

export interface PlanningAgentOptions {
  stream?: boolean;
  maxSteps?: number;
  temperature?: number;
  context?: any;
  planningDepth?: 'shallow' | 'detailed' | 'comprehensive';
  includeRiskAssessment?: boolean;
  includeResourceEstimation?: boolean;
}

/**
 * Execute planning operations with Vercel AI SDK
 */
export async function executePlanningAgent(
  input: string,
  options: PlanningAgentOptions = {}
): Promise<any> {
  const model = google('gemini-2.5-flash'); // Good balance for planning tasks

  const planningDepth = options.planningDepth || 'detailed';
  const includeRiskAssessment = options.includeRiskAssessment ?? true;
  const includeResourceEstimation = options.includeResourceEstimation ?? true;

  const systemPrompt = `You are Dante's Planning Specialist. Your expertise is in breaking down complex tasks, creating detailed execution plans, and providing strategic guidance for project implementation.

Your primary functions include:
1. **Task Breakdown**: Decompose complex requests into manageable, sequential steps
2. **Strategic Planning**: Develop comprehensive plans with timelines, dependencies, and milestones
3. **Risk Assessment**: Identify potential blockers, challenges, and mitigation strategies
4. **Resource Planning**: Estimate time, effort, and resource requirements for each task
5. **Execution Strategy**: Recommend optimal approaches, tools, and methodologies
6. **Quality Assurance**: Include testing, validation, and review steps in plans

Planning Depth: ${planningDepth}
- **Shallow**: High-level steps with basic dependencies
- **Detailed**: Step-by-step breakdown with estimated timelines and prerequisites
- **Comprehensive**: Full project plan with risk analysis, resource allocation, and multiple execution paths

${
  includeRiskAssessment ?
  `
Risk Assessment Guidelines:
- Identify technical, resource, and timeline risks
- Provide severity levels (low, medium, high, critical)
- Suggest specific mitigation strategies
- Consider dependencies and external factors` : ''
}

${
  includeResourceEstimation ?
  `
Resource Estimation Guidelines:
- Estimate time requirements (hours, days, weeks)
- Identify required skills and expertise levels
- Consider tool and infrastructure needs
- Account for testing, review, and deployment time` : ''
}

Task Management:
- Use task management tools to track planning progress
- Create tasks for each major planning phase
- Update progress as you develop the plan
- Provide clear deliverables and success criteria

Best Practices:
- Start with requirements clarification
- Break down complex tasks into 2-4 hour chunks when possible
- Include buffer time for unknowns (15-25%)
- Consider both technical and non-technical stakeholders
- Plan for iterative development and continuous feedback
- Include documentation and knowledge transfer steps`;

  const commonConfig = {
    model,
    system: systemPrompt,
    prompt: input,
    tools: mapToolsByName([...taskManagementTools, ...fileOperationTools]),
    maxSteps: options.maxSteps ?? 10,
    stopWhen: stepCountIs(options.maxSteps ?? 10),
    context: options.context,
    // Allow multiple steps for comprehensive planning
    providerOptions: {
      google: {
        thinkingConfig: {
          includeThoughts: true,
          thinkingBudget: 8192,
        }
      },
      openai: {
        reasoningSummary: 'auto',
        reasoningEffort: 'high',
      }
    },
  };

  if (options.stream) {
    return streamText(commonConfig);
  } else {
    return await generateText(commonConfig);
  }
}

/**
 * Deterministic structured planning with a required tool call.
 * Returns parsed plan JSON that matches the schema the orchestrator expects.
 */
export async function createStructuredPlan(
  input: string,
  options: PlanningAgentOptions = {}
): Promise<any> {
  const model = openai('gpt-5');
  const think = getThinkingConfigForTask('analysis', 'gpt-5', input);
  const tb = (think as any)?.thinking_budget || 2048;

  // Run discovery to identify relevant files and directories
  let discoveryContext = '';
  try {
    const discoveries = await discoverContext(input, {
      maxFiles: 15,
      maxDirectories: 5,
      includeTests: input.toLowerCase().includes('test'),
    });
    discoveryContext = formatDiscoveryContext(discoveries);
  } catch (error) {
    console.warn('Discovery failed, continuing without context:', error);
    // Continue without discovery context if it fails
  }

  const planTool = tool({
    description: 'Return the final execution plan in strict JSON',
    inputSchema: z.object({
      summary: z.string().describe('Short summary of the overall approach'),
      highlights: z.array(z.string()).optional().describe('Key focus areas or themes for the plan'),
      steps: z.array(z.object({
        id: z.string().describe('Short identifier'),
        title: z.string().optional(),
        focus: z.string().optional().describe('Specific focus or sub-problem for this step'),
        description: z.string().describe('Overview of the work to complete'),
        agent: z.string().describe('One of: research | code-generation | debug | security | review | planning | diagnostic'),
        instructions: z.array(z.string()).min(1).describe('Explicit, step-scoped instructions the agent must follow'),
        input: z.string().optional(),
        dependsOn: z.array(z.string()).optional(),
        expectedOutputs: z.array(z.string()).optional(),
        acceptanceCriteria: z.array(z.string()).optional(),
        handoffNotes: z.string().optional().describe('What the next step should know about this work'),
        references: z.array(z.string()).optional().describe('Files, URLs, or artifacts relevant to this step'),
        // NEW: Scope fields to reduce agent exploration and prevent tool budget exhaustion
        scopeFiles: z.array(z.string()).optional().describe('Specific file paths this step will read or modify (e.g., ["src/api/routes.ts", "src/middleware/auth.ts"]). CRITICAL for code-generation steps to prevent agents from exhausting their tool budget on exploration.'),
        scopePaths: z.array(z.string()).optional().describe('Directories relevant to this step (e.g., ["src/api", "src/middleware"]). Use when specific files are uncertain but directory scope is known.'),
        searchHints: z.array(z.object({
          pattern: z.string().describe('Regex or keyword pattern to search for'),
          context: z.string().describe('Why this pattern is relevant to the task'),
        })).optional().describe('Search patterns the agent can use if they need more context beyond the provided scope files.'),
      }))
    }) as any,
    // No execute; model must call the tool to finish
  } as any);

  const system = `You are Dante's Planning Specialist. Create a dependency-aware plan with explicit instructions per specialized agent.

${discoveryContext ? `${discoveryContext}\n\n` : ''}Output: Use the 'plan' tool to emit JSON in the following structure:
{
  "summary": string,
  "highlights": string[] (optional),
  "steps": [
    {
      "id": string,
      "title": string (optional),
      "focus": string (optional),
      "description": string,
      "agent": string,
      "instructions": string[],
      "input": string (optional),
      "dependsOn": string[] (optional),
      "expectedOutputs": string[] (optional),
      "acceptanceCriteria": string[] (optional),
      "handoffNotes": string (optional),
      "references": string[] (optional),
      "scopeFiles": string[] (optional - CRITICAL for code-generation),
      "scopePaths": string[] (optional),
      "searchHints": [{"pattern": string, "context": string}] (optional)
    }
  ]
}

Rules:
- Provide 3–8 minimal, actionable steps.
- Instructions must describe exactly what the assigned agent should do within its step and must not bleed into future steps.
- Include handoffNotes when later steps need to inspect artifacts produced here (files, URLs, commands, etc.).
- Use dependsOn to encode ordering.
- Choose the agent by capability, not by name:
  - research: analyzing a repository/project, searching/reading files, identifying which files implement something, web/file investigation, summarization.
  - code-generation: writing or updating code, applying patches, running commands, scaffolding, refactoring.
  - debug: reproducing and fixing bugs, tracing errors.
  - security: security review or hardening.
  - review: code review or QA pass.
  - workspace: Gmail/Calendar/Drive tasks (e.g., unread emails, meetings today, search Drive files); this agent handles OAuth and connector tools.
- For discovery tasks (“which file manages X”, “analyze the repo”, “list files…”) use agent='research'.
- Only pick agent='code-generation' when the step must modify code or run commands.

CRITICAL - FILE IDENTIFICATION (prevents agent tool budget exhaustion):
- Agents have LIMITED tool calls: 3 list_directory, 2 grep_code, 5 read_files per task
- For code-generation steps, you MUST identify specific files in scopeFiles to prevent exploration waste
- Use the file operation tools (list_directory_lite, grep_code, read_file) during planning to discover files
- Provide 3-10 specific file paths in scopeFiles when possible (e.g., ["src/api/routes.ts", "src/middleware/auth.ts"])
- If uncertain about exact files, provide scopePaths (directories) and searchHints (patterns to search)
- Better to over-specify files than under-specify - agents will fail if they exhaust their tool budget on exploration
- For research steps that need to discover files, provide scopePaths and searchHints to guide their search

Examples:
✅ GOOD: scopeFiles: ["src/api/server.ts", "src/api/routes.ts", "src/middleware/auth.ts"]
✅ GOOD: scopePaths: ["src/api", "src/middleware"], searchHints: [{"pattern": "router|app\\.use", "context": "finding route definitions"}]
❌ BAD: No scopeFiles or scopePaths provided for code-generation step (agent will waste budget exploring)`;

  const {text, reasoning, toolCalls, toolResults  } = await generateText({
    model,
    system,
    prompt: input,
    tools: { plan: planTool },
    toolChoice: 'required' as any,
    stopWhen: stepCountIs(options.maxSteps ?? 8),
    providerOptions: {
      google: {
        thinkingConfig: { thinkingBudget: tb, includeThoughts: true }
      },
      openai: {
        reasoningSummary: 'auto',
        reasoningEffort: 'high',
      }
    },
  });

  try {
    const call = toolCalls?.find((c: any) => (c.name || c.toolName) === 'plan')
      || toolCalls?.[0];
    return call?.input ?? JSON.parse(text || '{}');
  } catch {
    return JSON.parse(text || '{}');
  }
}

// Legacy compatibility wrapper
export const planningAgent = {
  name: 'Planning Agent',
  model: 'gpt-5',

  async execute(input: string | any[], options: any = {}) {
    const inputStr = Array.isArray(input)
      ? input.map(i => typeof i === 'string' ? i : JSON.stringify(i)).join('\n')
      : input;

    return await executePlanningAgent(inputStr, options);
  },
  async createStructuredExecutionPlan(input: string | any[], options: any = {}) {
    const inputStr = Array.isArray(input)
      ? input.map(i => typeof i === 'string' ? i : JSON.stringify(i)).join('\n')
      : input;
    return await createStructuredPlan(inputStr, options);
  },

  // Additional planning-specific methods
  async createTaskBreakdown(description: string, options: {
    granularity?: 'high' | 'medium' | 'low';
    estimateTime?: boolean;
    includeDependencies?: boolean;
  } = {}) {
    const planningOptions: PlanningAgentOptions = {
      planningDepth: 'detailed',
      includeResourceEstimation: options.estimateTime ?? true,
      includeRiskAssessment: false, // Focus on breakdown, not risks
      stream: false
    };

    const prompt = `Create a detailed task breakdown for the following project:

${description}

Requirements:
- Break into ${options.granularity || 'medium'}-level tasks
- ${options.estimateTime ? 'Include time estimates for each task' : 'Focus on task sequence and dependencies'}
- ${options.includeDependencies ? 'Clearly identify task dependencies' : 'List tasks in logical order'}
- Provide clear deliverables for each task
- Include testing and validation steps where appropriate`;

    return await executePlanningAgent(prompt, planningOptions);
  },

  async assessProjectRisks(projectDescription: string, projectScope?: string) {
    const planningOptions: PlanningAgentOptions = {
      planningDepth: 'comprehensive',
      includeRiskAssessment: true,
      includeResourceEstimation: false, // Focus on risks, not resources
      stream: false
    };

    const prompt = `Perform a comprehensive risk assessment for this project:

**Project**: ${projectDescription}
${projectScope ? `\n**Scope**: ${projectScope}` : ''}

Provide:
1. Technical risks and mitigation strategies
2. Resource and timeline risks
3. External dependency risks
4. Quality and delivery risks
5. Risk prioritization and monitoring recommendations`;

    return await executePlanningAgent(prompt, planningOptions);
  },

  async estimateProjectResources(projectDescription: string, requirements?: string[]) {
    const planningOptions: PlanningAgentOptions = {
      planningDepth: 'detailed',
      includeRiskAssessment: false,
      includeResourceEstimation: true,
      stream: false
    };

    const prompt = `Estimate resources needed for this project:

**Project**: ${projectDescription}
${requirements ? `\n**Requirements**: ${requirements.join(', ')}` : ''}

Provide estimates for:
1. Development time (by phase/component)
2. Required skills and expertise levels
3. Infrastructure and tooling needs
4. Testing and QA requirements
5. Documentation and training time
6. Buffer time recommendations`;

    return await executePlanningAgent(prompt, planningOptions);
  }
};

// --- TaskOrchestrator compatibility types and minimal stubs ---
export type Task = {
  id: string;
  title?: string;
  focus?: string;
  description: string;
  agent: string;
  input: any;
  instructions?: string[];
  expectedOutputs?: string[];
  handoffNotes?: string;
  acceptanceCriteria?: string[];
  references?: string[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  // NEW: Scope fields to reduce agent exploration and prevent tool budget exhaustion
  scopeFiles?: string[];
  scopePaths?: string[];
  searchHints?: Array<{
    pattern: string;
    context: string;
  }>;
};

export type ExecutionPlan = {
  summary?: string;
  highlights?: string[];
  steps: Task[];
  dependencies: Record<string, string[]>;
};

// Map planned labels/capabilities to registered agent names
function mapPlannedAgentToRegistered(agentLabel: string): string {
  const a = String(agentLabel || '').trim().toLowerCase();
  if (['research', 'researcher', 'analysis', 'analyze'].includes(a)) return 'ResearchAgent';
  if (['code', 'coding', 'implementation', 'build', 'code-generation', 'codegen'].includes(a)) return 'CodeGenerationAgent';
  if (['debug', 'fix', 'bugfix', 'troubleshoot'].includes(a)) return 'DebugAgent';
  if (['security', 'security-analysis', 'pentest', 'vuln'].includes(a)) return 'SecurityAnalysisAgent';
  if (['review', 'code-review', 'qa'].includes(a)) return 'CodeReviewAgent';
  if (['planning', 'plan'].includes(a)) return 'PlanningAgent';
  if (['workspace', 'google', 'google-workspace', 'gmail', 'calendar', 'drive', 'email', 'inbox'].includes(a)) return 'GoogleWorkspaceAgent';
  if (['computer', 'computer-use', 'browser', 'automation', 'web-automation'].includes(a)) return 'ComputerUseAgent';
  if (['isolated-computer-use', 'headless', 'background', 'monitoring'].includes(a)) return 'IsolatedComputerUseAgent';
  return 'ResearchAgent';
}

export type AgentInputContext = {
  title?: string;
  dependencySummaries?: Array<{ id: string; summary?: string; handoffNotes?: string }>;
  planSummary?: string;
  agentName?: string;
};

// Build concise, instruction-rich agent input payload
export function buildAgentInput(step: Task, context: AgentInputContext = {}): string {
  const lines: string[] = [];
  const title = context.title || step.title || step.id;

  lines.push(`Step: ${title}`);
  if (step.focus) {
    lines.push(`Focus: ${step.focus}`);
  }
  lines.push('');

  lines.push('Primary Objective:');
  lines.push(step.description || '');
  lines.push('');

  if (context.planSummary) {
    lines.push('Plan Summary Context:');
    lines.push(context.planSummary);
    lines.push('');
  }

  if (context.dependencySummaries && context.dependencySummaries.length > 0) {
    lines.push('Existing Progress From Previous Steps:');
    for (const dep of context.dependencySummaries) {
      const summary = dep.summary ? dep.summary.trim() : 'No summary captured. Review the task log or repository state.';
      const handoff = dep.handoffNotes ? ` (Handoff Guidance: ${dep.handoffNotes})` : '';
      lines.push(`- ${dep.id}: ${summary}${handoff}`);
    }
    lines.push('');
  }

  const instructions = Array.isArray(step.instructions) && step.instructions.length > 0
    ? step.instructions
    : undefined;
  if (instructions) {
    lines.push('Step Instructions:');
    instructions.forEach((instr, idx) => {
      lines.push(`${idx + 1}. ${instr}`);
    });
    lines.push('');
  }

  const expected = Array.isArray(step.expectedOutputs) && step.expectedOutputs.length > 0
    ? step.expectedOutputs
    : step.acceptanceCriteria;
  if (expected && expected.length > 0) {
    lines.push('Completion Requirements:');
    expected.forEach((item) => lines.push(`- ${item}`));
    lines.push('');
  }

  if (step.handoffNotes) {
    lines.push('Handoff Notes for the Next Agent:');
    lines.push(step.handoffNotes);
    lines.push('');
  }

  if (Array.isArray(step.references) && step.references.length > 0) {
    lines.push('Useful References:');
    step.references.forEach((ref) => lines.push(`- ${ref}`));
    lines.push('');
  }

  if (step.input != null && step.input !== '') {
    lines.push('Additional Input:');
    lines.push(typeof step.input === 'string' ? step.input : JSON.stringify(step.input, null, 2));
    lines.push('');
  }

  // NEW: Display scope files if provided by planner (CRITICAL for preventing tool budget exhaustion)
  if (Array.isArray((step as any).scopeFiles) && (step as any).scopeFiles.length > 0) {
    lines.push('');
    lines.push('📁 Files In Scope (identified during planning):');
    for (const file of (step as any).scopeFiles) {
      lines.push(`  - ${file}`);
    }
    lines.push('');
    lines.push('⚠️  IMPORTANT: Start by reading these files with read_file before making changes.');
    lines.push('   This saves your tool budget (3 list_directory, 2 grep_code, 5 read_files) for actual work.');
  }

  // NEW: Display scope paths if provided
  if (Array.isArray((step as any).scopePaths) && (step as any).scopePaths.length > 0) {
    lines.push('');
    lines.push('📂 Relevant Directories:');
    for (const dir of (step as any).scopePaths) {
      lines.push(`  - ${dir}/`);
    }
    lines.push('   Use list_directory_lite on these paths if you need to explore further.');
  }

  // NEW: Display search hints if provided
  if (Array.isArray((step as any).searchHints) && (step as any).searchHints.length > 0) {
    lines.push('');
    lines.push('🔍 Search Hints (if you need more context):');
    for (const hint of (step as any).searchHints) {
      lines.push(`  - Pattern: "${hint.pattern}" (${hint.context})`);
    }
  }

  // Enrich preamble with a suggested focus file path when available
  try {
    const agent = context.agentName || mapPlannedAgentToRegistered(step.agent);
    const recentWrites = orchestrator.recentWriteStatusByAgent.get(agent) || [];
    const suggestedPath = inferLastAffectedPath(orchestrator.recentToolArgs, recentWrites as any);
    if (suggestedPath) {
      lines.push('');
      lines.push('Suggested Focus Path (from recent activity):');
      lines.push(`- ${suggestedPath}`);
      lines.push('If making changes, start with read_file, then diagnose_file_syntax on this path before editing.');
    }
  } catch (e) {
  console.warn('Failed to enrich agent input with suggested focus path:', e);
}
  lines.push('');
  lines.push('Always stay within the scope of this step. Do not begin work assigned to later steps.');

  return lines.join('\n').trim();
}

export async function createPlan(query: string): Promise<ExecutionPlan> {
  try {
    const raw = await createStructuredPlan(query, {});
    const stepsArr = Array.isArray(raw?.steps) ? raw.steps : [];
    const steps: Task[] = stepsArr.map((s: any, i: number) => {
      const id = String(s?.id ?? `step_${i + 1}`);
      const description = String(s?.description ?? s?.title ?? '');
      const plannedAgent = String(s?.agent ?? 'research');
      const agent = mapPlannedAgentToRegistered(plannedAgent);
      const instructions = Array.isArray(s?.instructions)
        ? s.instructions.map((instr: any) => String(instr))
        : (typeof s?.instructions === 'string' ? [String(s.instructions)] : undefined);
      const expectedOutputs = Array.isArray(s?.expectedOutputs)
        ? s.expectedOutputs.map((o: any) => String(o))
        : undefined;
      const acceptanceCriteria = Array.isArray(s?.acceptanceCriteria)
        ? s.acceptanceCriteria.map((a: any) => String(a))
        : undefined;
      const references = Array.isArray(s?.references)
        ? s.references.map((r: any) => String(r))
        : undefined;
      // NEW: Parse scope fields
      const scopeFiles = Array.isArray(s?.scopeFiles)
        ? s.scopeFiles.map((f: any) => String(f))
        : undefined;
      const scopePaths = Array.isArray(s?.scopePaths)
        ? s.scopePaths.map((p: any) => String(p))
        : undefined;
      const searchHints = Array.isArray(s?.searchHints)
        ? s.searchHints.map((h: any) => ({
            pattern: String(h?.pattern ?? ''),
            context: String(h?.context ?? ''),
          }))
        : undefined;
      return {
        id,
        title: s?.title ? String(s.title) : undefined,
        focus: s?.focus ? String(s.focus) : undefined,
        description,
        agent,
        input: s?.input,
        instructions,
        expectedOutputs,
        acceptanceCriteria,
        handoffNotes: s?.handoffNotes ? String(s.handoffNotes) : undefined,
        references,
        scopeFiles,
        scopePaths,
        searchHints,
        status: 'pending',
      } as Task;
    });
    const dependencies: Record<string, string[]> = {};
    for (let i = 0; i < steps.length; i++) {
      const orig = stepsArr[i];
      const deps = Array.isArray(orig?.dependsOn) ? orig.dependsOn.map((d: any) => String(d)) : [];
      dependencies[steps[i].id] = deps;
    }
    const highlights = Array.isArray(raw?.highlights) ? raw.highlights.map((h: any) => String(h)) : undefined;
    const summary = typeof raw?.summary === 'string' ? raw.summary : undefined;
    return { summary, highlights, steps, dependencies };
  } catch {
    return {
      steps: [{
        id: 'step_1',
        description: 'Placeholder plan step',
        agent: 'ResearchAgent',
        input: query,
        status: 'pending',
      }],
      dependencies: {},
    };
  }
}

export async function executePlan(plan: ExecutionPlan, startStep?: string): Promise<string> {
  // Normalize graph
  const stepsById = new Map<string, Task>();
  for (const s of plan.steps) stepsById.set(s.id, { ...s });
  const deps: Record<string, string[]> = { ...plan.dependencies };
  for (const id of stepsById.keys()) if (!Array.isArray(deps[id])) deps[id] = [];

  // Build reverse edges
  const dependents = new Map<string, Set<string>>();
  for (const id of stepsById.keys()) dependents.set(id, new Set());
  for (const [sid, arr] of Object.entries(deps)) {
    for (const d of arr) {
      if (!dependents.has(d)) dependents.set(d, new Set());
      (dependents.get(d) as Set<string>).add(sid);
    }
  }

  // Ancestors (upstream) of a node
  const getAncestors = (sid: string): Set<string> => {
    const seen = new Set<string>();
    const visit = (n: string) => {
      for (const p of deps[n] || []) {
        if (!seen.has(p)) { seen.add(p); visit(p); }
      }
    };
    visit(sid);
    return seen;
  };
  // Downstream (dependents) of a node
  const getDownstream = (sid: string): Set<string> => {
    const seen = new Set<string>();
    const q: string[] = [sid];
    while (q.length) {
      const cur = q.shift() as string;
      for (const nxt of (dependents.get(cur) || [])) {
        if (!seen.has(nxt)) { seen.add(nxt); q.push(nxt); }
      }
    }
    return seen;
  };

  // Determine execution scope and initial completed set for resumption
  const completed = new Set<string>();
  let scope = new Set<string>(Array.from(stepsById.keys()));
  if (startStep && stepsById.has(startStep)) {
    const upstream = getAncestors(startStep);
    // Treat all upstream deps of startStep as completed
    upstream.forEach(u => completed.add(u));
    // Build scope: nodes reachable from startStep (including its extra needed deps not upstream)
    const downstream = getDownstream(startStep);
    scope = new Set<string>([startStep, ...downstream]);
    // Include any extra prerequisites for nodes in scope (except upstream which are treated as completed)
    let changed = true;
    while (changed) {
      changed = false;
      for (const sid of Array.from(scope)) {
        for (const d of deps[sid] || []) {
          if (!completed.has(d) && !scope.has(d)) { scope.add(d); changed = true; }
        }
      }
    }
  }

  // Detect missing dependencies
  const missingByStep = new Map<string, string[]>();
  for (const [sid, arr] of Object.entries(deps)) {
    if (!scope.has(sid)) continue;
    for (const d of arr) {
      if (!stepsById.has(d)) {
        const m = missingByStep.get(sid) || [];
        m.push(d);
        missingByStep.set(sid, m);
      }
    }
  }

  // Bookkeeping
  const status = new Map<string, Task['status']>();
  for (const id of stepsById.keys()) status.set(id, stepsById.get(id)!.status || 'pending');
  for (const id of completed) status.set(id, 'completed');

  // Remaining deps within scope (excluding already-completed ancestors)
  const depsLeft = new Map<string, Set<string>>();
  for (const id of scope) {
    const needs = new Set<string>();
    for (const d of deps[id] || []) {
      if (completed.has(d)) continue;
      // Only count dependencies that are in plan and within scope
      if (stepsById.has(d) && scope.has(d)) needs.add(d);
    }
    depsLeft.set(id, needs);
  }

  // Steps blocked due to missing or failed deps
  const permanentlyBlocked = new Map<string, string>(); // reason
  for (const [sid, miss] of missingByStep) {
    permanentlyBlocked.set(sid, `missing_dependency: ${miss.join(', ')}`);
  }

  const completedSummaries = new Map<string, string>();

  const summarizeAgentResult = (result: any, step: Task): string =>
    summarizeAgentResultUtil(result, step, {
      fallbackLogLabel: 'run log',
      fallbackDetailsLabel: 'precise details',
    });

  // Concurrency control
  const MAX_CONCURRENCY = Math.max(1, Number(process.env.PLANNING_MAX_CONCURRENCY || 3));
  const running = new Map<Promise<{ id: string; ok: boolean; result?: any; error?: string }>, string>();

  const tryStartReady = () => {
    const started: string[] = [];
    if (running.size >= MAX_CONCURRENCY) return started;
    // Ready = in scope, pending, depsLeft empty, not permanently blocked
    const ready = Array.from(scope)
      .filter(id => status.get(id) === 'pending')
      .filter(id => (depsLeft.get(id)?.size || 0) === 0)
      .filter(id => !permanentlyBlocked.has(id));
    while (running.size < MAX_CONCURRENCY && ready.length > 0) {
      const id = ready.shift() as string;
      const step = stepsById.get(id)!;
      status.set(id, 'running');
      const agentName = mapPlannedAgentToRegistered(step.agent);
      const title = step.title || step.id;
      const dependencySummaries = (deps[id] || []).map(depId => ({
        id: depId,
        summary: completedSummaries.get(depId),
        handoffNotes: stepsById.get(depId)?.handoffNotes,
      }));
      const inputForAgent = buildAgentInput(step, {
        title,
        dependencySummaries,
        planSummary: plan.summary,
        agentName,
      });
      const p = executeWithAgent(agentName, inputForAgent, {
        stream: false,
        maxSteps: 12,
            } as any, {orchestratorInstance: orchestrator} as any)
        .then((result: any) => ({ id, ok: true as const, result }))
        .catch((e: any) => ({ id, ok: false as const, error: e?.message || String(e) }));
      running.set(p, id);
      started.push(id);
    }
    return started;
  };

  // Kick off initial ready tasks
  tryStartReady();

  // Main loop
  while (running.size > 0) {
    const settled = await Promise.race(Array.from(running.keys()));
    const sid = running.get(
      Array.from(running.keys()).find(k => (k as any) === (settled as any)) as Promise<any>
    ) || settled.id;
    // Remove that promise from running
    for (const [pk, val] of Array.from(running.entries())) {
      if (val === sid) { running.delete(pk); break; }
    }
    // Update status
    if (settled.ok) {
      status.set(sid, 'completed');
      const step = stepsById.get(sid);
      if (step) {
        const summary = summarizeAgentResult((settled as any).result, step);
        completedSummaries.set(sid, summary);
      }
      // Unblock dependents (within scope)
      for (const dep of (dependents.get(sid) || [])) {
        if (!scope.has(dep)) continue;
        const s = depsLeft.get(dep);
        if (s) s.delete(sid);
      }
    } else {
      status.set(sid, 'failed');
      // Cascade block dependents
      for (const dep of (dependents.get(sid) || [])) {
        if (!scope.has(dep)) continue;
        if (!permanentlyBlocked.has(dep)) permanentlyBlocked.set(dep, `failed_dependency: ${sid}`);
      }
    }
    // Try to start more
    tryStartReady();
    // If no running and we still have pending, but none are ready, break
    if (running.size === 0) {
      const pendingInScope = Array.from(scope).filter(id => status.get(id) === 'pending');
      const readyLeft = pendingInScope.filter(id => (depsLeft.get(id)?.size || 0) === 0 && !permanentlyBlocked.has(id));
      if (pendingInScope.length > 0 && readyLeft.length === 0) {
        // Nothing more can be scheduled (deadlock due to failures/missing deps)
        break;
      }
    }
  }

  // Final tallies (consider only scoped tasks that were candidates to run or pre-completed ancestors)
  const considered = Array.from(scope);
  const completedCount = considered.filter(id => status.get(id) === 'completed').length;
  const failedCount = considered.filter(id => status.get(id) === 'failed').length;
  const runningCount = considered.filter(id => status.get(id) === 'running').length;
  const pendingCount = considered.filter(id => status.get(id) === 'pending').length;
  const blockedCount = Array.from(permanentlyBlocked.keys()).filter(id => scope.has(id)).length;

  const summaryLines: string[] = [];
  summaryLines.push(`Plan execution complete.`);
  if (startStep) summaryLines.push(`Resumed from step: ${startStep}`);
  summaryLines.push(`Completed: ${completedCount}`);
  summaryLines.push(`Failed: ${failedCount}`);
  if (blockedCount) summaryLines.push(`Blocked: ${blockedCount}`);
  if (pendingCount) summaryLines.push(`Pending (not schedulable): ${pendingCount}`);
  if (runningCount) summaryLines.push(`Running (stuck): ${runningCount}`);
  // Optional: list failures/blocked
  const failedIds = considered.filter(id => status.get(id) === 'failed');
  if (failedIds.length) summaryLines.push(`Failed steps: ${failedIds.join(', ')}`);
  const blockedEntries = Array.from(permanentlyBlocked.entries()).filter(([id]) => scope.has(id));
  if (blockedEntries.length) {
    summaryLines.push(`Blocked steps:`);
    for (const [id, reason] of blockedEntries.slice(0, 20)) {
      summaryLines.push(`- ${id}: ${reason}`);
    }
  }
  return summaryLines.join('\n');
}

// Simple heuristics for external tests/utilities
export function needsPlanning(request: string): boolean {
  try {
    const s = String(request || '').toLowerCase();
    const complexHints = ['plan', 'steps', 'multi-step', 'decompose', 'research', 'investigate', 'refactor', 'implement', 'patch', 'apply diff', 'execute', 'run command', 'debug', 'fix', 'analyze repo', 'which file', 'architect', 'design', 'integrate', 'deploy', 'migrate'];
    const lengthHeavy = s.length > 320 || s.split(/\s+/).length > 60;
    return lengthHeavy || complexHints.some(h => s.includes(h));
  } catch { return true; }
}

export function estimateComplexity(request: string): 'low' | 'medium' | 'high' {
  try {
    const s = String(request || '');
    const words = s.trim().split(/\s+/).length;
    if (words <= 12) return 'low';
    if (words <= 60) return 'medium';
    return 'high';
  } catch { return 'medium'; }
}
