/**
 * Built-in Agent Registry
 *
 * Consolidates agent definitions used by the DanteAIOrchestrator without
 * exporting a separate definitions package. This avoids confusion between
 * legacy/duplicate agent surfaces and the orchestrator-driven agents.
 */

import { generateText, streamText, tool, stepCountIs } from 'ai';
import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';
import { z } from 'zod';
import { AgentDefinition } from '@/types/orchestrator-types';

// Tools used by ResearchAgent and others
import { webSearchTool } from '../tools/webSearch';
import { enhancedWebSearchTool } from '../tools/enhancedWebSearch';
import { webFetchTool } from '../tools/webFetch';
import { webContentChunkTool } from '../tools/webContentChunk';
import { webResearchTool } from '../tools/webResearch';
import { grepCodeTool } from '../tools/grepCode';
import { readFileTool, readFilesTool, fileOperationTools, gitOperationTool, setWorkingDirectoryTool, getWorkingDirectoryTool } from '../tools/fileOperations';
import { runFileAnalysisTool } from '../tools/fileAnalysis';
import { diagnoseFileSyntaxTool, diagnoseFilesSyntaxTool, diagnoseModifiedFilesTool, runBuildCheckTool, listModifiedFilesTool, raiseBuildFailureTaskTool } from '../tools/codeDiagnostics';
import { weatherTool } from '../tools/weather';
import { computerUseTool } from '../tools/computerUse/computerUseTool';
import { browserUseTools } from '../tools/browserUseTools';
import { config } from '../utils/config';
import { ensureWorkspaceAuthTool } from '../tools/workspaceAuth';
import {
  searchGmail,
  getRecentGmailEmails,
  readGmailEmail,
  batchReadGmailEmails,
  searchCalendarEvents,
  getTodayEvents,
  getWeekEvents,
  readCalendarEvent,
  getUpcomingMeetings,
  searchDrive,
  getRecentDocuments,
  fetchDriveFile,
  listSharedDrives,
  searchDriveByType
} from '../tools/connectors';
import { mapToolsByName } from '../utils/toolUtils';
import { getContextValue, setContextValue } from '../utils/requestContext';
import { taskManagementTools } from '../tools/taskManagement';
import { pdfExtractTool } from '../tools/pdfExtract';
import { planAndDelegate } from './helpers/planning';

// --- Agent Definitions ---

export const codeGenerationAgent: AgentDefinition = {
  name: 'CodeGenerationAgent',
  description: 'Specialized in writing high-quality, production-ready code',
  model: 'gpt-5-mini',
  instructions: `You are a Code Generation Specialist with expertise in creating clean, maintainable, and well-structured code.

Your responsibilities:
- Write production-ready code following best practices
- Include proper error handling and validation
- Implement security best practices
- Provide clear documentation and usage examples
- Test code when possible
- Use absolute paths when using file tools (read_file, file_edit, etc)
- Use file operations tools to read, write, and modify code files
- At the start of your work, call get_working_directory to confirm the current working directory. If it differs from the project root, immediately call set_working_directory with the correct project path before proceeding with any file operations.
- Use relative paths for file operations within the project (e.g., "src/agents/DanteOrchestrator.ts").
- The /uploads directory is reserved for user-uploaded files only - do not use it for project file operations.
- When working with project files, use the actual file paths in the codebase, not /uploads paths.
- Example: To read this file, use "src/agents/BuiltInAgents.ts".
- Use task management tools to track progress on assigned tasks

Languages you excel at: JavaScript, TypeScript, Python, Go, Rust, and more.
Always consider performance, security, and maintainability in your implementations.

CONTEXT MANAGEMENT (CRITICAL - READ CAREFULLY):
Your execution is monitored for token usage and tool call frequency. Exceeding limits will abort your run.

Tool Call Limits (per task):
- list_directory: Maximum 3 calls per task
- grep_code: Maximum 2 calls per task
- search_code: Maximum 2 calls per task
- read_files: Maximum 5 calls per task

Tool Selection Strategy (MANDATORY):
1. **Initial Exploration**: ALWAYS use list_directory_lite first
   - Returns directory structure and file counts without listing individual files
   - Prevents context bloat while giving you navigation information
   - Example: list_directory_lite(dirPath: "src", maxDepth: 2)

2. **Detailed Inspection**: Use list_directory ONLY when you need file details for a specific, narrow directory
   - Maximum maxFiles parameter: 100 (requests above this will be rejected)
   - Use for targeted exploration after identifying the right directory with list_directory_lite
   - Example: list_directory(dirPath: "src/agents", maxFiles: 50)

3. **Code Search**: Use grep_code with narrow, specific patterns
   - Avoid broad patterns like ".*" or "function" that match thousands of lines
   - Use specific identifiers: class names, function names, unique strings
   - Maximum maxResults: 500 (requests above this will be rejected)
   - Example: grep_code(pattern: "CodeGenerationAgent", fileTypes: ["ts"])

4. **File Reading**: Read files individually rather than batch operations
   - Use read_file for single files
   - Only use read_files when you need multiple related files (max 5 calls per task)

Token Budget Awareness:
- You are monitored for cumulative token usage
- At 70% of context limit: Warning issued
- At 85% of context limit: Execution aborted with error
- If you receive a warning, immediately:
  * Stop exploratory tool calls
  * Focus only on the specific files/areas needed for your task
  * Use list_directory_lite instead of list_directory
  * Narrow grep patterns to be more specific

If You Hit Limits:
- Summarize what you've learned so far
- Explain what additional information you need
- Request human guidance or task decomposition
- DO NOT retry the same broad exploration approach

File Editing Best Practices (CRITICAL):
1. **Always read files before editing** - Use read_file to see exact content first
2. **Use 'patch' operation for targeted changes** - Prefer operation='patch' with before/after over replace
3. **Keep patterns short and precise** - Use the smallest unique string that identifies the change location
4. **Never overwrite files as error recovery** - If an edit fails, read the file and retry with corrected parameters
5. **Verify edits succeeded** - Check the tool result and read the file again if needed
6. **Use multi-line anchored patches** - For changes spanning multiple lines, use patch with before/after
7. **Avoid truncation** - Never use '…' or truncate content in tool arguments

Error Recovery Strategy:
- If pattern_not_found: Read the file, find the correct pattern, retry with exact match
- If syntax errors after edit: Read the file, identify the issue, apply corrective patch
- If uncertain: Ask for clarification rather than guessing
- NEVER fall back to operation='write' with mode='overwrite' after a failed edit

Relentless quality bar:
- Every change must be syntax-correct, formatted, and build-clean before concluding.
- Diagnose modified files and run a build/typecheck or black (for python) as appropriate.
- If you encounter tool errors or cannot make progress, produce a concise, detailed explanation so the orchestrator can assign a diagnostic agent or retry.

CRITICAL: Once you have completed all code changes and called the 'answer' tool with your structured summary, your work is DONE. Do NOT start over, do NOT call get_working_directory again, do NOT read files again. Simply finish and let the orchestrator handle the response.`,
  maxSteps: 12,
  tools: {},
};

export const researchAgent: AgentDefinition = {
  name: 'ResearchAgent',
  description: 'Expert at gathering information, analyzing data, and conducting research',
  model: 'gemini-2.5-flash',
  instructions: `You are a Research Specialist skilled in gathering, analyzing, and synthesizing information.

Your capabilities:
- Web research and information gathering
- Data analysis and pattern recognition
- Fact-checking and verification
- Comprehensive report generation
- Source citation and credibility assessment

Always provide accurate, well-researched information with proper sources when available.
Focus on delivering insights, not just raw data.

Tool usage guidance:
- For local PDFs in the project (e.g., under uploads/<fileId.ext>), use the pdf_extract tool to parse text and metadata safely.
- Before reading local files, call get_working_directory and confirm it matches the project path in context; if not, immediately call set_working_directory to align it.
`,
  maxSteps: 12,
  tools: {
    web_search: webSearchTool,
    enhanced_web_search: enhancedWebSearchTool,
    web_fetch: webFetchTool,
    web_content_chunk: webContentChunkTool,
    web_research: webResearchTool,
    pdf_extract: pdfExtractTool,
    // Local codebase exploration for repo questions
    grep_code: grepCodeTool,
    read_file: readFileTool,
    read_files: readFilesTool,
  }
};

export const debugAgent: AgentDefinition = {
  name: 'DebugAgent',
  description: 'Specialized in identifying and fixing bugs, errors, and technical issues',
  model: 'gpt-5-mini',
  instructions: `You are a Debug Specialist expert at troubleshooting and fixing technical issues.

Your expertise includes:
- Identifying root causes of bugs and errors
- Analyzing error messages and stack traces
- Debugging code across multiple languages
- Performance optimization
- System diagnostics
- Using file operations to read, analyze, and fix code
- Check the current working directory with get_working_directory at the start of each investigation and use set_working_directory to align with the project path before reading or editing files.

Approach debugging systematically:
1. Understand the problem
2. Reproduce the issue
3. Identify the root cause
4. Implement and test the fix
5. Prevent future occurrences`,
  maxSteps: 10,
  tools: {},
};

export const securityAgent: AgentDefinition = {
  name: 'SecurityAnalysisAgent',
  description: 'Analyzes code and systems for security vulnerabilities',
  model: 'gemini-2.5-pro',
  instructions: `You are a Security Analysis Specialist focused on identifying and mitigating security risks.

Your responsibilities:
- Identify security vulnerabilities in code
- Assess compliance with security standards
- Recommend security improvements
- Analyze potential attack vectors
- Provide secure coding guidelines
- Confirm the working directory with get_working_directory before accessing local files and call set_working_directory when alignment with the project path is needed.

Security domains: OWASP Top 10, authentication, authorization, data protection, network security.
Always prioritize security without compromising functionality.`,
  maxSteps: 10,
  tools: {
    grep_code: grepCodeTool,
    read_file: readFileTool,
    read_files: readFilesTool,
  }
};

export const planningAgent: AgentDefinition = {
  name: 'PlanningAgent',
  description: 'Creates detailed plans and breaks down complex tasks',
  model: 'gpt-5',
  instructions: `You are a Planning Specialist who excels at breaking down complex tasks into manageable steps.

Your approach:
- Analyze requirements thoroughly
- Create detailed, actionable plans
- Consider dependencies and prerequisites
- Estimate time and resources
- Identify potential risks and mitigation strategies

Use structured thinking to create comprehensive plans that others can follow.
Always consider multiple approaches and recommend the most effective one.`,
  maxSteps: 8,
  tools: {
    create_structured_plan: tool({
      description: 'Create a concise, dependency-aware execution plan as strict JSON',
      inputSchema: z.object({
        request: z.string().describe('Planning request or problem statement'),
        options: z.any().optional().describe('Optional planning options: depth, risk, resources'),
      }) as any,
      execute: async ({ request, options }: { request: string; options?: any }) => {
        const mod = await import('./PlanningAgent');
        const res = await mod.createStructuredPlan(request, options || {});
        return res;
      }
    }) as any,
    ...mapToolsByName([...taskManagementTools, ...fileOperationTools])
  }
};

export const taskOrchestratorAgent: AgentDefinition = {
  name: 'TaskOrchestrator',
  description: 'Handles large-scale tasks by distributing work across multiple workers',
  model: 'gpt-5',
  instructions: `You are a Task Orchestrator specialized in managing large-scale, complex tasks.

Critical execution rule:
- Your very first action must be a call to the delegate_to_agents tool. Do not send intermediate analysis or call any other tool before delegate_to_agents has run at least once per user request.
- If delegate_to_agents returns an error, retry it when reasonable or report the failure explicitly, but never skip the attempt.
- IMPORTANT: Once delegate_to_agents completes successfully (returns with type='delegated_orchestration' and stepResults), your job is done. Present a brief summary of the results and FINISH. Do NOT call delegate_to_agents again or continue processing.

Your responsibilities:
- Analyze large requests (>20k tokens or >5 files)
- Break work into manageable chunks
- Distribute tasks to appropriate workers
- Coordinate parallel processing
- Synthesize results from multiple workers

You excel at:
- Token management and optimization
- Parallel task execution
- Result aggregation and synthesis
- Progress tracking and reporting

Autonomous follow-up:
- If the user asks to implement a brand-new feature, integrate or set up a technology not yet present in the repo (signals: install/setup/integrate/configure/deploy/CLI not found), or when the task lacks sufficient project context, call auto_follow_up_workflow first with the user request. It will: 1) research external docs, 2) map the codebase, 3) produce a plan and delegate.
- After auto_follow_up_workflow finishes, present a short "Here’s what I found…" summary and proceed unless the user presses Stop.`,
  maxSteps: 20,
  tools: {
    auto_follow_up_workflow: tool({
      description: 'Autonomously research external docs, analyze the local codebase, then plan and delegate tasks. Returns a structured summary + plan/results.',
      inputSchema: z.object({
        request: z.string().describe('Original user request'),
        context: z.any().optional(),
      }),
      execute: async ({ request, context }: { request: string; context?: any }) => {
        const mod = await import('./AutoFollowUpManager');
        const res = await mod.executeAutoFollowUp(request, { context });
        return res;
      }
    }),
    delegate_to_agents: tool({
      description: 'Plan with PlanningAgent and delegate each step to the right specialized agent. Always call this first.',
      inputSchema: z.object({ request: z.string(), context: z.any().optional() }),
      execute: async ({ request, context }: { request: string; context?: any }) => {
        const { orchestrator } = await import('./DanteOrchestrator');

        const onPlanCreated = getContextValue<(p: { summary?: string; steps: any[] }) => void>('onPlanCreated');
        const onDelegationStart = getContextValue<(p: { stepId: string; agent: string; title?: string; description?: string }) => void>('onDelegationStart');
        const onDelegationEnd = getContextValue<(p: { stepId: string; agent: string; success: boolean }) => void>('onDelegationEnd');
        const onInnerToolCall = getContextValue<(name: string, args: any) => void>('onInnerToolCall');
        const onInnerToolResult = getContextValue<(name: string, output: any) => void>('onInnerToolResult');
        const onPlanIssues = getContextValue<(issues: Array<{ stepId: string; agent: string; title?: string; reason?: string }>) => void>('onPlanIssues');

        const wantsStreaming = !!(onPlanCreated || onDelegationStart || onDelegationEnd);

        if (!wantsStreaming) {
          const res = await planAndDelegate(request, { context, maxWorkers: 3, stream: false, orchestratorInstance: orchestrator });
          return res;
        }

        try { setContextValue('orchestration_streamed', true); } catch {}
        const iterator: AsyncIterable<any> = await planAndDelegate(request, { context, maxWorkers: 3, stream: true, orchestratorInstance: orchestrator }) as any;
        let plan: any | undefined;
        const stepResults: Array<{ stepId: string; agent: string; success: boolean }> = [];
        let finalText = '';
        const blockedIssues: Array<{ stepId: string; agent: string; title?: string; reason?: string }> = [];
        for await (const ev of iterator) {
          const t = (ev as any)?.type;
          if (t === 'plan_created') {
            const payload = (ev as any).data || {};
            plan = { summary: payload.summary, steps: Array.isArray(payload.steps) ? payload.steps : [] };
            try { onPlanCreated?.(plan); } catch {}
          } else if (t === 'delegation_start') {
            const payload = (ev as any).data || {};
            try { onDelegationStart?.(payload); } catch {}
          } else if (t === 'delegation_end') {
            const payload = (ev as any).data || {};
            try { onDelegationEnd?.(payload); } catch {}
            if (payload && payload.stepId && payload.agent) {
              stepResults.push({ stepId: payload.stepId, agent: payload.agent, success: !!payload.success });
            }
          } else if (t === 'delegation_skipped') {
            const payload = (ev as any).data || {};
            try { onDelegationStart?.({ stepId: payload.stepId, agent: payload.agent, title: payload.title || 'Skipped', description: payload.reason || 'Blocked step skipped' }); } catch {}
            try { onDelegationEnd?.({ stepId: payload.stepId, agent: payload.agent, success: false }); } catch {}
            blockedIssues.push({ stepId: payload.stepId, agent: payload.agent, title: payload.title, reason: payload.reason });
          } else if (t === 'raw_model_stream_event') {
            const d = (ev as any).data || {};
            if (d.type === 'output_text_delta') {
              finalText += String(d.delta || '');
            } else if (d.type === 'output_text') {
              finalText += String(d.text || '');
            }
          } else if (t === 'tool_call') {
            try {
              const d = (ev as any).data || {};
              if (d && d.name) onInnerToolCall?.(d.name, d.args);
            } catch {}
          } else if (t === 'tool_result') {
            try {
              const d = (ev as any).data || {};
              if (d && d.name) onInnerToolResult?.(d.name, d.result);
            } catch {}
          } else if (t === 'run_item_stream_event' && (ev as any).item) {
            const item: any = (ev as any).item;
            if (item.type === 'tool_call_item') {
              const tName = item.name || item.tool_name || item.function?.name || item.rawItem?.name;
              const tArgs = item.arguments || item.args || item.function?.arguments || item.rawItem?.arguments;
              if (tName) try { onInnerToolCall?.(tName, tArgs); } catch {}
            } else if (item.type === 'tool_call_output' || item.type === 'tool_output') {
              const rName = item.name || item.tool_name || item.rawItem?.name;
              const rawVal = item.output ?? item.result ?? item.content ?? item.rawItem?.output;
              if (rName) try { onInnerToolResult?.(rName, rawVal); } catch {}
            }
          }
        }

        try { if (blockedIssues.length > 0) onPlanIssues?.(blockedIssues); } catch {}
        const text = finalText || (plan?.summary ? `Plan: ${plan.summary}` : '');
        return { type: 'delegated_orchestration', plan, stepResults, text, finalMessage: text };
      }
    })
  }
};

export const computerUseAgentDef: AgentDefinition = {
  name: 'ComputerUseAgent',
  description: 'Automates visible computer/browser actions the user can watch (headed mode).',
  model: 'gpt-5-codex',
  instructions: `You are Dante's Computer Use Specialist for visible, user-facing automation. Prefer browser-use tools for granular control and computer_use for higher-level automation. Uploaded attachments live under /uploads/<fileId.ext> with normalized names—use those /uploads URLs from the attachment metadata rather than the original filenames when working with files.`,
  maxSteps: 12,
  tools: {
    ...(config.computerUse.enabled ? { computer_use: computerUseTool } : {}),
    ...mapToolsByName(browserUseTools, { fallbackPrefix: 'browser_use' }),
  }
};

export const isolatedComputerUseAgentDef: AgentDefinition = {
  name: 'IsolatedComputerUseAgent',
  description: 'Automates background/headless sessions with monitoring and isolation.',
  model: 'gpt-5-codex',
  instructions: `Background automation with isolation. Use for monitoring and non-visual tasks.`,
  maxSteps: 10,
  tools: {
    ...mapToolsByName(browserUseTools, { fallbackPrefix: 'browser_use' }),
  }
};

export const weatherAgent: AgentDefinition = {
  name: 'WeatherAgent',
  description: 'Provides weather data and forecasts',
  model: 'gpt-5-mini',
  instructions: `Provide clear, accurate weather information and forecasts with relevant details.`,
  maxSteps: 5,
  tools: { weather: weatherTool }
};

export const codeReviewAgent: AgentDefinition = {
  name: 'CodeReviewAgent',
  description: 'Performs code reviews for quality, style, and correctness',
  model: 'gemini-2.5-flash',
  instructions: `You are a Code Review Specialist. Review code for correctness, readability, maintainability, performance, and security.`,
  maxSteps: 8,
  tools: {
    review_diffs: tool({
      description: 'Analyze unified diffs and return structured code review.',
      inputSchema: z.object({ diffs: z.union([z.string(), z.array(z.string())]) }),
      execute: async ({ diffs }: { diffs: string | string[] }) => {
        const { CodeReviewAgent } = await import('./CodeReviewAgent');
        const structuredReview = await CodeReviewAgent.execute({ diffs });
        return { structuredReview, text: JSON.stringify(structuredReview), ...(structuredReview as any) } as any;
      }
    }),
    run_file_analysis: runFileAnalysisTool,
    grep_code: grepCodeTool,
    read_file: readFileTool,
    read_files: readFilesTool,
    diagnose_file_syntax: diagnoseFileSyntaxTool,
    diagnose_files_syntax: diagnoseFilesSyntaxTool,
    list_modified_files: listModifiedFilesTool,
    diagnose_modified_files: diagnoseModifiedFilesTool,
    run_build_check: runBuildCheckTool,
    raise_build_failure_task: raiseBuildFailureTaskTool,
  }
};

export const fileAnalysisAgent: AgentDefinition = {
  name: 'FileAnalysisAgent',
  description: 'Performs deep inspections of modified files to surface build, security, and quality risks.',
  model: 'gpt-5-codex',
  instructions: `You are Dante's File Analysis Specialist.

Your objectives:
- Inspect modified files before finalizing a task.
- Use run_file_analysis to generate structured findings (risks, recommendations, context).
- Cross-reference code review output when available and highlight discrepancies or gaps.
- Call list_modified_files to discover changes; evaluate each critical file individually.
- Emphasize security, build stability, and regression risks.
- Provide actionable follow-up steps when issues are detected.
- Confirm the working directory with get_working_directory and call set_working_directory when adjustment is required.
`,
  maxSteps: 8,
  tools: {
    run_file_analysis: runFileAnalysisTool,
    read_file: readFileTool,
    read_files: readFilesTool,
    list_modified_files: listModifiedFilesTool,
    git_operation: gitOperationTool,
    diagnose_files_syntax: diagnoseFilesSyntaxTool,
    run_build_check: runBuildCheckTool,
    get_working_directory: getWorkingDirectoryTool,
    set_working_directory: setWorkingDirectoryTool,
  }
};

export const googleWorkspaceAgent: AgentDefinition = {
  name: 'GoogleWorkspaceAgent',
  description: 'Manages Gmail, Calendar, and Drive operations with OAuth authentication',
  model: 'gpt-5',
  instructions: `Google Workspace Specialist for Gmail, Calendar, and Drive. Always ensure authentication first using ensure_workspace_auth.`,
  maxSteps: 10,
  tools: {
    ensure_workspace_auth: ensureWorkspaceAuthTool,
    // Gmail
    search_gmail: searchGmail as any,
    get_recent_gmail_emails: getRecentGmailEmails as any,
    read_gmail_email: readGmailEmail as any,
    batch_read_gmail_emails: batchReadGmailEmails as any,
    // Calendar
    search_calendar_events: searchCalendarEvents as any,
    get_today_calendar_events: getTodayEvents as any,
    get_week_calendar_events: getWeekEvents as any,
    read_calendar_event: readCalendarEvent as any,
    get_upcoming_meetings: getUpcomingMeetings as any,
    // Drive
    search_google_drive: searchDrive as any,
    get_recent_drive_documents: getRecentDocuments as any,
    fetch_drive_file: fetchDriveFile as any,
    list_shared_drives: listSharedDrives as any,
    search_drive_by_type: searchDriveByType as any,
  }
};

// Export all agents as a collection and the registration helper
export const agents = {
  codeGeneration: codeGenerationAgent,
  research: researchAgent,
  debug: debugAgent,
  security: securityAgent,
  planning: planningAgent,
  taskOrchestrator: taskOrchestratorAgent,
  computerUse: computerUseAgentDef,
  isolatedComputerUse: isolatedComputerUseAgentDef,
  weather: weatherAgent,
  googleWorkspace: googleWorkspaceAgent,
  codeReview: codeReviewAgent,
  fileAnalysis: fileAnalysisAgent,
};

export function registerAllAgents(orchestrator: any): void {
  Object.values(agents).forEach(agent => {
    orchestrator.registerAgent(agent);
  });
  try { console.log(`✅ Registered ${Object.keys(agents).length} built-in agents`); } catch {}
}
