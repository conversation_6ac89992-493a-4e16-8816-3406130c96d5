import { tool } from 'ai';
import { z } from 'zod';
import * as path from 'path';
import { config } from '../utils/config';
import { memoryManager } from '../memory/MemoryManager';
import { getContextValue, setContextValue } from '../utils/requestContext';
import { executeCodeGeneration, type CodeGenerationOptions } from './vercel/codeGeneration';
import { interruptBus } from '../utils/interruptBus';
import { createCommonConfig } from './common-config';
import { OrchestratorOptions, ToolUsageStatsSnapshot } from '@/types/orchestrator-types';
import { DanteAIOrchestrator } from './DanteOrchestrator';
import { shouldSkipWriteGuardForAnalysis, inferLastAffectedPath } from './helpers/analysis';
import { getModelProvider, isModelAvailable } from './helpers/models';
import { ensureAnswerResult, extractCodegenAnswer } from './helpers/answers';

/**
 * Agent execution actions extracted from DanteOrchestrator
 * Contains the main executeWithAgent function and related utilities
 */

const MEMORY_TOOL_NAMES = new Set([
  'remember',
  'recall',
  'forget',
  'get_memory_stats',
  'contextual_memory_search',
  'learn_from_error'
]);

// Helper functions for error detection
const isOpenAIRateLimit = (err: any): boolean => {
  const msg = String(err?.message || '').toLowerCase();
  const code = String(err?.code || '').toLowerCase();
  return msg.includes('rate limit') || msg.includes('quota') || code === 'rate_limit_exceeded';
};

const isEmptyFinalGuard = (err: any): boolean => {
  return String(err?.code || '') === 'AGENT_EMPTY_FINAL';
};

const isNoWriteConfirmed = (err: any): boolean => {
  return String(err?.code || '') === 'NO_WRITE_CONFIRMED';
};

const isServerError = (err: any): boolean => {
  const msg = String(err?.message || '').toLowerCase();
  const status = err?.status || err?.statusCode || err?.response?.status;
  return (status >= 500 && status < 600) || msg.includes('500') || msg.includes('502') || msg.includes('503') || msg.includes('504');
};

const isInsufficientFunds = (err: any): boolean => {
  const msg = String(err?.message || '').toLowerCase();
  const code = String(err?.code || '').toLowerCase();
  return msg.includes('insufficient') || msg.includes('quota exceeded') || msg.includes('billing') || code === 'insufficient_quota';
};

const isModelUnavailable = (err: any): boolean => {
  const msg = String(err?.message || '').toLowerCase();
  const code = String(err?.code || '').toLowerCase();
  return msg.includes('model not found') || msg.includes('model is not available') || msg.includes('model does not exist') || code === 'model_not_found';
};

// Check if error is retryable with a different model
const isRetryableWithFallback = (err: any): boolean => {
  return isOpenAIRateLimit(err) || isEmptyFinalGuard(err) || isNoWriteConfirmed(err) ||
         isServerError(err) || isInsufficientFunds(err) || isModelUnavailable(err);
};

export interface AgentActionOptions {
  orchestratorInstance: DanteAIOrchestrator; // The DanteOrchestrator instance
}

export async function executeWithAgent(
  agentName: string,
  input: string,
  options: OrchestratorOptions = {},
  { orchestratorInstance }: AgentActionOptions
): Promise<any> {
  const agent = orchestratorInstance.agents.get(agentName);
  if (!agent) throw new Error(`Agent ${agentName} not found`);

  const callStartTime = Date.now();
  console.log(`🤖 Executing with agent: ${agentName}`);

  // Bind request-scoped working directory to orchestrator projectId for proper snapshot tagging
  let contextCwd: string | undefined;
  try {
    contextCwd = getContextValue<string>('cwd');
  } catch (e) {
    console.warn('Failed to read current request cwd from context:', e);
  }
  if (options.initialWorkingDirectory) {
    try {
      const absInitial = path.resolve(options.initialWorkingDirectory);
      if (contextCwd !== absInitial) {
        try { setContextValue('cwd', absInitial); contextCwd = absInitial; } catch (setErr) { console.warn('Failed to update context cwd:', setErr); }
      }
      if (process.cwd() !== absInitial) {
        try { process.chdir(absInitial); } catch (chdirErr) { console.warn('Failed to set process cwd:', chdirErr); }
      }
      orchestratorInstance.currentProjectId = absInitial;
    } catch (e) {
      console.warn('Failed to apply initial working directory:', e);
    }
  }
  if (contextCwd && typeof contextCwd === 'string' && contextCwd.length > 0) {
    orchestratorInstance.currentProjectId = contextCwd;
  }
  // Auto-initialize working directory for agents that need file operations
  const needsFileTools = ['CodeGenerationAgent', 'DebugAgent', 'SecurityAnalysisAgent', 'ComputerUseAgent', 'ResearchAgent'].includes(agentName);
  if (needsFileTools) {
    try {
      const projectRoot = orchestratorInstance.currentProjectId || process.cwd();
      const currentDir = getContextValue<string>('cwd') || process.cwd();

      // Only set working directory if it's different from the project root
      if (currentDir !== projectRoot) {
        console.log(`🔧 Auto-initializing working directory for ${agentName}: ${projectRoot}`);
        try { setContextValue('cwd', projectRoot); } catch (e) { console.warn('Failed to set context cwd:', e); }
        try { process.chdir(projectRoot); } catch (e) { console.warn('Failed to set process cwd:', e); }
      }
    } catch (e) {
      console.warn(`Failed to auto-initialize working directory for ${agentName}:`, e);
    }
  }

  const runToolStats: ToolUsageStatsSnapshot = {
    totalCalls: 0,
    readFile: {
      total: 0,
      paths: new Map<string, number>(),
      maxRepeat: 0,
    },
    listDirectory: {
      total: 0,
      paths: new Map<string, number>(),
      maxRepeat: 0,
    },
    fileEdit: 0,
  };

  // Orchestrator-driven model selection:
  // Centralized for code generation. Explicit fallback order is:
  // gpt-5 → gemini-2.5-flash → gemini-2.5-pro → gpt-5-mini.
  // Gemini gating is based solely on provider availability (API key/config), not an env flag.
  const agentModelKey = DanteAIOrchestrator.normalizeModelId(agent.model || orchestratorInstance.currentModel);
  const codegenChain = ['gpt-5', 'gemini-2.5-flash', 'gemini-2.5-pro', 'gpt-5-mini'];
  const availableCodegenChain = codegenChain.filter((m: string) => isModelAvailable(m));
  const selectedModelKey = agentName === 'CodeGenerationAgent'
    ? (availableCodegenChain[0] || codegenChain[0])
    : agentModelKey;
  const model = getModelProvider(selectedModelKey);

  // Special handling for agents that need file operation tools
  let agentTools = agent.tools || {};

  if (needsFileTools) {
    // Dynamically import file, diagnostics, and task tools to avoid circular dependencies
    try {
      const { fileOperationTools } = await import('../tools/fileOperations');
      const { codeDiagnosticsTools } = await import('../tools/codeDiagnostics');
      const { taskManagementTools } = await import('../tools/taskManagement');

      // Convert array tools to object format
      const fileTools = fileOperationTools.reduce((acc: any, tool: any) => {
        acc[tool.name] = tool;
        return acc;
      }, {});

      const diagTools = codeDiagnosticsTools.reduce((acc: any, tool: any) => {
        acc[tool.name] = tool;
        return acc;
      }, {});

      const taskTools = taskManagementTools.reduce((acc: any, tool: any) => {
        acc[tool.name] = tool;
        return acc;
      }, {});

      agentTools = { ...agentTools, ...fileTools, ...diagTools, ...taskTools };
      console.log(`📁 Added ${Object.keys(fileTools).length} file tools, ${Object.keys(diagTools).length} diagnostics tools and ${Object.keys(taskTools).length} task tools to ${agentName}`);
    } catch (e) {
      console.warn(`Failed to load tools for ${agentName}:`, e);
    }
  }

  const mergedTools = { ...orchestratorInstance.globalTools, ...agentTools };
  const baseTools = Object.fromEntries(
    Object.entries(mergedTools).filter(([name]) => !MEMORY_TOOL_NAMES.has(name))
  );

  // Wrap tools to consult prior error memories before execution
  const wrapWithErrorRecall = (toolsObj: Record<string, any>, agentForHints: string): Record<string, any> => {
    const wrapped: Record<string, any> = {};
    for (const [tName, t] of Object.entries(toolsObj)) {
      // Skip wrapping memory tools (already filtered) and trivial answer tool which we may add later
      if (!t || typeof (t as any).execute !== 'function') {
        wrapped[tName] = t;
        continue;
      }
      const originalExecute = (t as any).execute;
      wrapped[tName] = {
        ...t,
        async execute(args: any, options?: any) {
          // Build a lightweight query from tool name and common argument fields
          let query = tName;
          try {
            const pick = (o: any, keys: string[]) => {
              const out: any = {};
              for (const k of keys) if (o && o[k] != null) out[k] = o[k];
              return out;
            };
            const argObj = typeof args === 'string' ? (() => { try { return JSON.parse(args); } catch { return {}; } })() : (args || {});
            const interesting = pick(argObj, ['filePath','path','dirPath','directory','pattern','query','operation','language','framework','error','message']);
            query = `${tName} ${JSON.stringify(interesting)}`.slice(0, 500);
          } catch {}

          let preCallHints: Array<{ error: string; solution: string }> = [];
          try {
            const matches = await memoryManager.recallErrors(query);
            if (Array.isArray(matches) && matches.length > 0) {
              preCallHints = matches.slice(0, 3).map((m: any) => ({
                error: String(m?.content?.errorPattern || m?.content?.errorMessage || ''),
                solution: String(m?.content?.solution || '')
              })).filter(h => h.error || h.solution);
              if (preCallHints.length > 0) {
                console.log(`🧠 [Memory] Found ${preCallHints.length} prior error hints for ${agentForHints}:${tName}`);
              }
            }
          } catch (recallErr) {
            try { console.warn(`[Memory] recallErrors failed for ${tName}:`, recallErr); } catch {}
          }

          // Execute the original tool
          const result = await originalExecute.apply(t, [args, options]);

          // Attach advisory hints (non-breaking) for the LLM to consider in next steps
          try {
            if (preCallHints.length > 0) {
              if (result && typeof result === 'object') {
                (result as any).preCallMemoryHints = preCallHints;
              } else {
                // If primitive, wrap minimally without breaking typical consumption patterns
                return { result, preCallMemoryHints: preCallHints };
              }
            }
          } catch {}
          return result;
        }
      } as any;
    }
    return wrapped;
  };

  const tools = wrapWithErrorRecall(baseTools, agentName);

  // Inject finalization tool for CodeGenerationAgent to guarantee a final result
  let finalTools = tools;
  let forceToolChoice: any = undefined;
  if (agentName === 'CodeGenerationAgent') {
    const answerTool = tool({
      description: 'Provide the final code generation result in a structured format.',
      inputSchema: z.object({
        summary: z.string().describe('Concise summary of changes'),
        filesModified: z.array(
          z.object({
            path: z.string(),
            changeType: z.enum(['create', 'modify', 'delete']).optional(),
            rationale: z.string().optional(),
            diffHint: z.string().optional().describe('Short, human-readable summary of changes with line refs if known'),
            patchPreview: z.string().optional().describe('Unified diff preview (---/+++ headers) with essential hunks only'),
          })
        ).optional(),
        instructions: z.array(z.string()).optional().describe('Instructions to apply or verify changes'),
        commandsToRun: z.array(z.string()).optional().describe('Shell commands to validate/build/test'),
        notes: z.array(z.string()).optional(),
      }) as any,
    }) as any;
    finalTools = { ...tools, answer: answerTool };
    forceToolChoice = 'required' as any;
  }

  // Guard tuning flags (env-configurable)
  const writeGuardEnabled = String(process.env.DISABLE_NO_WRITE_GUARD || '').toLowerCase() !== 'true';
  // Prevent repeated auto-repair attempts across retries/fallbacks within the same executeWithAgent call
  let autoRepairTriggered = false;

  // Compose any injected steering context from interruptBus for this session
  let __steeringPreamble = '';
  try {
    if (options.sessionId) {
      const updates = interruptBus.consumeInjectedContext(options.sessionId) || [];
      const repl = interruptBus.consumeReplaceNext(options.sessionId);
      const lines: string[] = [];
      if (updates.length > 0) {
        lines.push('[User Steering Update]');
        for (const u of updates) {
          const parts: string[] = [];
          if (u.priority) parts.push(`priority: ${u.priority}`);
          if (u.message) parts.push(`message: ${u.message}`);
          if (u.constraints && Object.keys(u.constraints).length > 0) {
            try { parts.push(`constraints: ${JSON.stringify(u.constraints)}`); } catch (e) { console.warn('Failed to stringify steering constraints', e); }
          }
          if (parts.length > 0) lines.push(`- ${parts.join(' | ')}`);
        }
        lines.push('Apply these updates before continuing.');
      }
      if (repl) {
        lines.push('[Override Next Step]');
        const parts: string[] = [];
        if (repl.priority) parts.push(`priority: ${repl.priority}`);
        if (repl.message) parts.push(`next: ${repl.message}`);
        if (repl.constraints && Object.keys(repl.constraints).length > 0) {
          try { parts.push(`constraints: ${JSON.stringify(repl.constraints)}`); } catch (e) { console.warn('Failed to stringify replacement constraints', e); }
        }
        if (parts.length > 0) lines.push(`- ${parts.join(' | ')}`);
        lines.push('Discard any previously planned immediate next action and perform the override instruction instead.');
      }
      if (lines.length > 0) {
        __steeringPreamble = `\n\n${lines.join('\n')}`;
        try { orchestratorInstance.emit('steeringApplied', { sessionId: options.sessionId, override: !!repl, updates: updates.length }); } catch (e) { console.warn('Failed to emit steeringApplied event', e); }
      }
    }
  } catch (e) {
    console.warn('Failed to process steering context from interruptBus:', e);
  }

  const commonConfig = createCommonConfig({
    model,
    agent,
    input,
    steeringPreamble: __steeringPreamble,
    options,
    finalTools,
    forceToolChoice,
    agentName,
    runToolStats,
    orchestratorInstance
  });

  // Helper: build a short handoff preamble describing recent tool activity
  const buildHandoffPreamble = () => {
    console.log(`[LOG] Building handoff preamble for agent ${agentName}, recentToolArgs length: ${orchestratorInstance.recentToolArgs.length}, recentToolResults length: ${(orchestratorInstance.recentToolResultsByAgent.get(agentName) || []).length}`);
    try {
      const now = Date.now();
      const recent = orchestratorInstance.recentToolArgs
        .filter((e: any) => now - e.t < 60_000)
        .slice(-10);
      if (recent.length === 0) {
        console.log(`[LOG] No recent tools for preamble, returning empty`);
        return '';
      }
      const summary = Array.from(new Set(recent.map((e: any) => e.name))).join(', ');
      // Recent tool result previews (last 3)
      const resultsBuf = (orchestratorInstance.recentToolResultsByAgent.get(agentName) || []).slice(-3);
      const resultsLines = resultsBuf.map((r: any) => `- ${r.name}: ${r.preview}`).join('\n');
      // Last assistant text, if available
      const lastText = orchestratorInstance.lastAgentTextByAgent.get(agentName);
      const lastTextLine = lastText ? `\nLast assistant note: ${lastText.slice(0, 220)}${lastText.length > 220 ? '…' : ''}` : '';
      // Objective (trim original input to a single‑line compact form)
      const objective = (() => {
        const oneLine = String(input).replace(/\s+/g, ' ').trim();
        return oneLine.length > 220 ? oneLine.slice(0, 220) + '…' : oneLine;
      })();

      const nextHint = (() => {
        const lastRes = resultsBuf[resultsBuf.length - 1]?.name || recent[recent.length - 1]?.name;
        return lastRes ? `Avoid redoing '${lastRes}'. Continue with the next logical subtask.` : 'Continue with the next logical subtask.';
      })();

      // Check recent write status to guide auto-recovery
      let writeGuidance = '';
      try {
        const wb = (orchestratorInstance.recentWriteStatusByAgent.get(agentName) || []).slice(-3);
        const lastWrite = wb.reverse().find((w: any) => ['file_edit'].includes(w.name));
        const inferredPath = inferLastAffectedPath(
          orchestratorInstance.recentToolArgs,
          orchestratorInstance.recentWriteStatusByAgent.get(agentName) || []
        );
        if (lastWrite) {
          const targetPath = lastWrite.path || inferredPath;
          if (!lastWrite.ok) {
            const p = targetPath ? ` (${targetPath})` : '';
            writeGuidance = `\nDetected a recent ${lastWrite.name}${p} with failing diagnostics or formatting. Immediately run diagnose_file_syntax${targetPath ? ` on ${targetPath}` : ' on the affected file'}, apply fixes (format/syntax), then verify with read_file + assert_file_contains before concluding.`;
          } else if (lastWrite.ok && targetPath) {
            writeGuidance = `\nLast write ${lastWrite.name} succeeded with clean diagnostics for ${targetPath}. Continue from there.`;
          }
        } else if (inferredPath) {
          writeGuidance = `\nSuggested focus path: ${inferredPath}. Start by read_file, then diagnose_file_syntax if edits are planned.`;
        }
      } catch {}

      const preamble = `\n\n[System Handoff Note]\nTask Summary:\n- Objective: ${objective}\n- Recent tools: ${summary}\n${resultsLines ? `- Recent results:\n${resultsLines}\n` : ''}${lastTextLine}${writeGuidance}\nGuidance: Previous step did not finalize cleanly. ${nextHint} Use the current filesystem state and working directory.`;
      console.log(`[LOG] Handoff preamble built, length: ${preamble.length}, includes tools: ${recent.length > 0}`);
      return preamble;
    } catch (e) {
      console.log(`[LOG] Error building preamble: ${e}`);
      return '';
    }
  };

  // Helper: recall prior task snapshots from memory to avoid duplicate work
  const buildRecallPreamble = async () => {
    try {
      const preferences = await memoryManager.recallUserPreferences();
      if (!preferences || preferences.length === 0) return '';
      const lines: string[] = [];
      for (const pref of preferences.slice(0, 3)) {
        try {
          const category = String(pref?.content?.category || 'general');
          const preferenceSummary = (() => {
            const prefVal = pref?.content?.preference;
            if (typeof prefVal === 'string') return prefVal;
            if (prefVal && typeof prefVal === 'object') {
              const entries = Object.entries(prefVal).map(([k, v]) => `${k}: ${typeof v === 'string' ? v : JSON.stringify(v)}`);
              return entries.slice(0, 4).join('; ');
            }
            return JSON.stringify(prefVal ?? {});
          })();
          lines.push(`- ${category}: ${preferenceSummary}`);
        } catch {}
      }
      if (lines.length === 0) return '';
      return `\n\n[User Preferences]\n${lines.join('\n')}\nHonor these preferences while executing the task.`;
    } catch (err) {
      try { console.warn('Failed to recall user preferences for preamble:', err); } catch {}
      return '';
    }
  };

  // Execute with optional fallbacks for rate limits
  const tryExecute = async (modelKey: string, label?: string, extraPreamble?: string, opts?: { disableTools?: boolean }): Promise<any> => {
    const providerModel = getModelProvider(modelKey);
    // Prepend recall preamble if available (prevents duplicate work across sessions)
    const recall = await buildRecallPreamble();
    const promptWithRecall = label
      ? `${input}${recall}${buildHandoffPreamble()}${extraPreamble || ''}`
      : `${input}${recall}`;
    const cfg: any = {
      ...commonConfig,
      model: providerModel,
      prompt: promptWithRecall,
    };
    const disableTools = !!opts?.disableTools;
    if (disableTools) {
      cfg.tools = undefined;
      cfg.toolChoice = undefined;
    }

    const baseCodegenOptions: CodeGenerationOptions = {
      model: providerModel,
      maxSteps: options.maxSteps ?? agent.maxSteps ?? 10,
      onStepFinish: commonConfig.onStepFinish,
      context: commonConfig.context,
      instructions: agent.instructions,
      abortSignal: (commonConfig as any)?.abortSignal,
    };

    if (options.stream) {
      console.log(`📡 [Orchestrator] Streaming response for agent ${agentName}${label ? ` (${label})` : ''}`);
      orchestratorInstance.emit('modelAttempt', { agent: agentName, model: modelKey });
      if (agentName === 'CodeGenerationAgent' && !disableTools) {
        return await executeCodeGeneration(promptWithRecall, { ...baseCodegenOptions, stream: true });
      }
      return await orchestratorInstance.doGenerate(modelKey, cfg, true);
    }
    orchestratorInstance.emit('modelAttempt', { agent: agentName, model: modelKey });
    let res: any;
    let codegenStructuredAnswer: any | undefined;
    if (agentName === 'CodeGenerationAgent' && !disableTools) {
      res = await executeCodeGeneration(promptWithRecall, { ...baseCodegenOptions });
      const extraction = extractCodegenAnswer(res);
      codegenStructuredAnswer = extraction.structured;
      if ((typeof ((res as any)?.text) !== 'string' || String((res as any)?.text).trim().length === 0) && extraction.rendered) {
        (res as any).text = extraction.rendered;
      }
      if (codegenStructuredAnswer && typeof codegenStructuredAnswer === 'object') {
        (res as any).structuredCodegen = codegenStructuredAnswer;
      }
    } else {
      res = await orchestratorInstance.doGenerate(modelKey, cfg, false);
    }

    // Synthesize a final text from the 'answer' tool call for CodeGenerationAgent when needed
    try {
      if (agentName === 'CodeGenerationAgent' && !disableTools) {
        if (!codegenStructuredAnswer) {
          const extraction = extractCodegenAnswer(res);
          codegenStructuredAnswer = extraction.structured;
          if ((typeof ((res as any)?.text) !== 'string' || String((res as any)?.text).trim().length === 0) && extraction.rendered) {
            (res as any).text = extraction.rendered;
          }
          if (codegenStructuredAnswer && typeof codegenStructuredAnswer === 'object') {
            (res as any).structuredCodegen = codegenStructuredAnswer;
          }
        }
      }
    } catch {}

    ensureAnswerResult(res, agentName);

    // Guard: some agents must produce a final text answer. If we only have toolCalls
    // and no text, treat as transient failure so upstream can retry or fall back.
    try {
      const txt = (res as any)?.text;
      const tc = (res as any)?.toolCalls;
      const mustFinalize = ['CodeGenerationAgent', 'DebugAgent', 'SecurityAnalysisAgent'].includes(agentName);
      if (!options.stream && mustFinalize) {
        const emptyText = typeof txt !== 'string' || txt.trim().length === 0;
        if (emptyText && Array.isArray(tc) && tc.length > 0) {
          const err: any = new Error(`AGENT_EMPTY_FINAL: ${agentName} produced no final text after ${tc.length} tool calls on ${modelKey}. This likely indicates a partial or interrupted run. Retry is recommended.`);
          err.code = 'AGENT_EMPTY_FINAL';
          throw err;
        }
      }
    } catch (guardErr) {
      // Re-throw to outer handler for retry/fallback logic
      throw guardErr;
    }

    // Guard: require a confirmed write/edit success with clean diagnostics
    // Apply ONLY to CodeGenerationAgent. Research/Planning/etc. should never trigger this.
    let analysisKey: string | null = null;
    try {
      analysisKey = orchestratorInstance.analysisTurnKey(agentName, options, input);
      if (!writeGuardEnabled) {
        // Guard disabled via env; accept result
        orchestratorInstance.resetAnalysisProgress(analysisKey);
        return res;
      }
      if (agentName !== 'CodeGenerationAgent') {
        orchestratorInstance.resetAnalysisProgress(analysisKey);
        return res;
      }

      if (!codegenStructuredAnswer) {
        const extraction = extractCodegenAnswer(res);
        codegenStructuredAnswer = extraction.structured;
      }
      const answerArgs: any = codegenStructuredAnswer;
      const summaryText = typeof answerArgs?.summary === 'string' ? answerArgs.summary : '';
      const declaredFiles = Array.isArray(answerArgs?.filesModified) ? answerArgs.filesModified : [];
      const declaredPaths = declaredFiles
        .map((f: any) => (typeof f?.path === 'string' ? f.path.trim() : ''))
        .filter((p: string) => p.length > 0);
      const finalText = typeof (res as any)?.text === 'string' ? (res as any).text : '';

      const wb = (orchestratorInstance.recentWriteStatusByAgent.get(agentName) || []).slice(-10);
      const callWrites = wb.filter((w: any) => w.t >= callStartTime);
      const hadFileEditAttempt = callWrites.some((w: any) => w.name === 'file_edit');

      const skipWriteGuard = shouldSkipWriteGuardForAnalysis({
        input: String(input ?? ''),
        routeText: options.routeText,
        summary: summaryText,
        finalText,
        declaredPaths,
        hadFileEditAttempt,
        readFileCount: runToolStats.readFile.total,
        uniqueReadPaths: runToolStats.readFile.paths.size,
        readFileMaxRepeat: runToolStats.readFile.maxRepeat,
        listDirCount: runToolStats.listDirectory.total,
        totalToolCalls: runToolStats.totalCalls,
        // uniqueListPaths: runToolStats.listDirectory.paths.size,
        // listDirMaxRepeat: runToolStats.listDirectory.maxRepeat,
      });

      if (skipWriteGuard) {
        console.log(`✅ Write guard skipped for ${agentName} (analysis-only task)`);
        orchestratorInstance.resetAnalysisProgress(analysisKey);
        return res;
      }

      // Require at least one successful file_edit with clean diagnostics
      const successfulWrites = callWrites.filter((w: any) => w.name === 'file_edit' && w.ok);
      if (successfulWrites.length === 0 && hadFileEditAttempt) {
        const err: any = new Error(`NO_WRITE_CONFIRMED: ${agentName} attempted file edits but none succeeded with clean diagnostics. This indicates syntax errors, formatting issues, or write failures that need correction.`);
        err.code = 'NO_WRITE_CONFIRMED';
        throw err;
      }

      orchestratorInstance.resetAnalysisProgress(analysisKey);
      return res;
    } catch (guardErr: any) {
      if (analysisKey) orchestratorInstance.resetAnalysisProgress(analysisKey);
      throw guardErr;
    }
  };

  // Fallback order helper - Enhanced with more models and intelligent tiering
  const fallbackOrderFor = (primaryModel: string, agentName: string): string[] => {
    // Define fallback chains with multiple capable online models before offline
    const chains: Record<string, string[]> = {
      // GPT-5 fallbacks: try other powerful models first
      'gpt-5': ['gpt-5-mini', 'gemini-2.5-pro', 'gemini-2.5-flash', 'claude-3-5-sonnet'],

      // GPT-5-mini fallbacks: try Gemini and Claude alternatives
      'gpt-5-mini': ['gemini-2.5-flash', 'gpt-5', 'claude-3-5-sonnet', 'gpt-5-codex'],

      // Gemini 2.5 Flash fallbacks: try pro version, then GPT and Claude
      'gemini-2.5-flash': ['gemini-2.5-pro', 'gpt-5', 'claude-3-5-sonnet', 'gpt-5-mini'],

      // Gemini 2.5 Pro fallbacks: try flash, then other powerful models
      'gemini-2.5-pro': ['gemini-2.5-flash', 'gpt-5', 'claude-3-5-sonnet', 'gpt-5-mini'],

      // Claude fallbacks: try other Claude models, then GPT and Gemini
      'claude-3-5-sonnet': ['claude-3-5-haiku', 'gpt-5', 'gemini-2.5-pro', 'gpt-5-mini'],
      'claude-3-5-haiku': ['claude-3-5-sonnet', 'gemini-2.5-flash', 'gpt-5-mini'],
    };

    // Get base chain for the primary model
    const base = chains[primaryModel] || ['gpt-5-mini', 'gemini-2.5-flash', 'claude-3-5-sonnet'];

    // Filter to only available models
    const available = base.filter((m: string) => isModelAvailable(m));

    // Log the fallback chain for debugging
    if (available.length > 0) {
      console.log(`[Fallback] Chain for ${primaryModel} (${agentName}): ${available.join(' → ')}`);
    } else {
      console.warn(`[Fallback] No available fallback models for ${primaryModel} (${agentName})`);
    }

    return available;
  };

  try {
    const res = await tryExecute(selectedModelKey);
    const callEndTime = Date.now();
    const duration = callEndTime - callStartTime;
    console.log(`✅ Agent ${agentName} completed in ${duration}ms`);
    return res;
  } catch (err: any) {
    // Handle aborts as pause/cancel checkpoints
    try {
      const emsg = String(err?.message || '');
      const isAbort = (err?.name === 'AbortError') || /abort/i.test(emsg);
      if (isAbort) {
        try { await orchestratorInstance.persistTaskSnapshot(agentName, input, 'checkpoint'); } catch {}
        throw err; // bubble up; server will end stream quickly
      }
    } catch {}
    // Log error details
    console.log(`[LOG] Error in executeWithAgent for ${agentName} with model ${agent.model}: ${err.message}`);

    // Classify error type for better logging
    const errorType = isOpenAIRateLimit(err) ? 'rate-limit' :
                      isServerError(err) ? 'server-error' :
                      isInsufficientFunds(err) ? 'insufficient-funds' :
                      isModelUnavailable(err) ? 'model-unavailable' :
                      isEmptyFinalGuard(err) ? 'empty-final' :
                      isNoWriteConfirmed(err) ? 'no-write-confirmed' : 'unknown';
    console.log(`[LOG] Error type: ${errorType}`);

    // Check if error is retryable with fallback models
    if (isRetryableWithFallback(err)) {
      const chain = fallbackOrderFor(selectedModelKey, agentName);

      if (chain.length > 0) {
        console.warn(`⚠️ ${errorType} on ${agent.model} for ${agentName}. Attempting ${chain.length} fallback model(s)…`);

        // Persist a handoff snapshot so recovery has durable context
        try { await orchestratorInstance.persistTaskSnapshot(agentName, input, 'handoff'); } catch {}

        // Try each fallback model in sequence
        for (const fb of chain) {
          try {
            console.log(`🔄 [Fallback ${chain.indexOf(fb) + 1}/${chain.length}] Attempting model: ${fb}`);

            // Notify UI that the active agent context is switching models
            try { options.onAgentSelected?.({ agent: `${agentName} (${fb})`, confidence: 0.9 }); } catch {}

            const res = await tryExecute(fb, `fallback -> ${fb}`);
            console.log(`✅ [Fallback Success] Model ${fb} completed successfully`);

            // Emit event for logging model switch
            orchestratorInstance.emit('modelSwitched', {
              from: agent.model,
              to: fb,
              agent: agentName,
              reason: errorType
            });

            return res;
          } catch (err2: any) {
            const fallbackErrorType = isRetryableWithFallback(err2) ? 'retryable' : 'non-retryable';
            console.warn(`❌ [Fallback Failed] Model ${fb} failed (${fallbackErrorType}): ${err2?.message || err2}`);
            continue;
          }
        }

        console.warn(`⚠️ [Fallback Exhausted] All ${chain.length} online fallback model(s) failed for ${agentName}`);
      } else {
        console.warn(`⚠️ [No Fallbacks] No available fallback models for ${agentName} with primary model ${selectedModelKey}`);
      }
    } else {
      console.log(`[LOG] Error is not retryable with fallback models (${errorType})`);
    }

    // Final fallback: attempt offline model (ollama) as last resort
    // The offline model acts as a summarizer to inform the orchestrator what still needs to be done
    try {
      const offProv = String(config.orchestrator?.offlineProvider || '').toLowerCase();
      const offModel = String(config.orchestrator?.offlineModel || '');
      if (offProv === 'ollama' && offModel && config.ollama?.baseURL) {
        const offlineKey = `ollama:${offModel}`;
        console.warn(`🧰 [Last Resort] All online models failed. Attempting offline fallback via ${offlineKey}`);
        console.warn(`⚠️ Note: Offline model will act as a summarizer only, with limited capabilities`);

        // Add preamble to instruct offline model to act as a summarizer
        const offlinePreamble = `\n\n[SYSTEM NOTICE: You are running in offline mode with limited capabilities. All online models have failed. Your role is to:
1. Summarize what was requested
2. Explain what you attempted to do
3. List what still needs to be completed
4. Provide any partial results or context that might help
5. Suggest next steps for when online models become available

Do NOT attempt to complete the full task. Focus on providing a helpful summary and status report.]`;

        const res = await tryExecute(offlineKey, 'offline-fallback', offlinePreamble, { disableTools: true });

        // Mark the response as coming from offline fallback
        if (res && typeof res === 'object') {
          (res as any).offlineFallback = true;
          (res as any).offlineModel = offlineKey;
        }

        console.warn(`✅ [Offline Fallback] Completed with ${offlineKey} (summarizer mode)`);
        return res;
      }
    } catch (offErr: any) {
      console.error(`❌ [Offline Fallback Failed] ${offErr?.message || String(offErr)}`);
    }

    // If no fallback succeeded, rethrow the original error
    console.error(`❌ [All Fallbacks Failed] No models available for ${agentName}. Original error: ${err.message}`);
    throw err;
  }
  // Persist a durable task snapshot to memory so future sessions can resume or avoid duplication
}
