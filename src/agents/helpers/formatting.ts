export const formatAgentDisplayName = (internalName: string): string => {
  return internalName.replace(/Agent$/g, ' Agent').replace(/([a-z])([A-Z])/g, '$1 $2').replace(/\s+/g, ' ').trim();
};

export const safeJsonParse = <T = any>(value: unknown): T | undefined => {
    if (typeof value !== 'string') return undefined;
    try {
      return JSON.parse(value) as T;
    } catch {
      return undefined;
    }
  }

export const extractToolCallId = (data: any): string | undefined => {
  if (!data || typeof data !== 'object') return undefined;

  const normalize = (value: unknown): string | undefined => {
    if (typeof value === 'string') {
      const trimmed = value.trim();
      return trimmed.length > 0 ? trimmed : undefined;
    }
    if (typeof value === 'number' && Number.isFinite(value)) return String(value);
    if (typeof value === 'bigint') return value.toString();
    return undefined;
  };

  const candidates: unknown[] = [
    (data as any).id,
    (data as any).toolCallId,
    (data as any).tool_call_id,
    (data as any).callId,
    (data as any).call_id,
    (data as any).toolId,
    (data as any).tool_id,
    (data as any).itemId,
    (data as any).item_id,
    (data as any).tool?.id,
    (data as any).rawItem?.id,
    (data as any).rawItem?.toolCallId,
    (data as any).rawItem?.tool_call_id,
    (data as any).rawItem?.callId,
    (data as any).rawItem?.call_id,
    (data as any).raw?.id,
  ];

  for (const candidate of candidates) {
    const normalized = normalize(candidate);
    if (normalized) return normalized;
  }

  return undefined;
};
