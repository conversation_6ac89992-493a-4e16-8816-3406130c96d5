import { buildStepRoutingBasis, mapPlannedAgentToRegistered } from "./planning";
import { OrchestratorOptions } from "@/types/orchestrator-types";
import { generateText } from "ai";
import { executeWithAgent as executeWithAgentAction } from './agent-actions';
import { getModelProvider, isModelAvailable } from "./models";
import { config } from "@/utils/config";
import agents from "..";
import { normalizeAgentChoice } from "./normalizers";

export const resolveStepAgent = async (
  step: { title?: string; description?: string; input?: string; agent?: string; acceptanceCriteria?: string[]; dependsOn?: string[] },
  planSummary?: string,
): Promise<string> => {
  // Always perform intelligent routing based on step content, not just the planned agent label.
  // This ensures each step is assigned to the most appropriate specialized agent.
  const basis = buildStepRoutingBasis(step, planSummary);
  if (basis.trim().length > 0) {
    try {
      const routed = await routeToAgent(basis);
      const mapped = mapPlannedAgentToRegistered(routed.agent);
      // Accept any specialized agent from intelligent routing (not just non-coordinator agents)
      if (mapped && !['TaskOrchestrator', 'PlanningAgent'].includes(mapped)) {
        console.log(`🎯 Intelligent routing: Step "${step.title || step.description?.slice(0, 50)}" → ${mapped} (confidence: ${(routed.confidence * 100).toFixed(0)}%)`);
        return mapped;
      }
    } catch (e) {
      console.warn('Failed to resolve step agent via routeToAgent:', e);
    }
  }

  // Fallback 1: Use the planned agent if it's a valid specialized agent
  const direct = mapPlannedAgentToRegistered(step.agent);
  if (direct && !['TaskOrchestrator', 'PlanningAgent'].includes(direct)) {
    console.log(`📋 Using planned agent: Step "${step.title || step.description?.slice(0, 50)}" → ${direct}`);
    return direct;
  }

  // Fallback 2: Try heuristic normalization
  const heuristic = normalizeAgentChoice(step, direct);
  const mappedHeuristic = mapPlannedAgentToRegistered(heuristic);
  if (mappedHeuristic && !['TaskOrchestrator', 'PlanningAgent'].includes(mappedHeuristic)) {
    console.log(`🔍 Heuristic routing: Step "${step.title || step.description?.slice(0, 50)}" → ${mappedHeuristic}`);
    return mappedHeuristic;
  }

  // Final fallback: ResearchAgent
  console.log(`⚠️ Fallback routing: Step "${step.title || step.description?.slice(0, 50)}" → ResearchAgent`);
  return 'ResearchAgent';
};

export const  routeToAgent = async (
  input: string,
  configuration: typeof config = config,
): Promise<{ agent: string; confidence: number }> => {
    // Prefer configured offline model for routing; fall back to cost‑effective online model
    const offlineProv = String(configuration.orchestrator?.offlineProvider || '').toLowerCase();
    const offlineModel = String(configuration.orchestrator?.offlineModel || '');
  const availableAgents = agents.getAgentMap();

    const systemPrompt = `You are an agent router. Analyze the user's request and choose the best specialized agent.

Routing rules:
- Prefer specialized agents; do not pick generic coordinator agents.
- Choose ResearchAgent for web/code research and information gathering.
- Choose CodeGenerationAgent for code changes, refactors, or adding features.
- Choose DebugAgent for bug reproduction and fixes.
- Choose SecurityAnalysisAgent for security reviews.
- Choose PlanningAgent to produce plans only.
- Choose TaskOrchestrator for large, multi-step tasks, the task is unclear or when the user asks to install/setup/integrate/configure/deploy new tech.
- Choose ComputerUseAgent for visible, user-facing browser/computer automation.
- Choose GoogleWorkspaceAgent for Gmail/Calendar/Drive tasks.
- Do not invent names. If uncertain, output TaskOrchestrator.

Available agents:
${Array.from(availableAgents.entries()).map(([name, agent]) => `- ${name}: ${agent.description}`).join('\n')}
`;

    const runRouter = async (modelKey: string) => {
      const { text } = await generateText({
        model: getModelProvider(modelKey),
        system: systemPrompt,
        prompt: input,
      });
      return text;
    };

    let text: string | undefined;

    // 1) Try offline model when configured (e.g., Ollama)
    if (offlineProv === 'ollama' && offlineModel && config.ollama?.baseURL) {
      const offlineKey = `ollama:${offlineModel}`;
      try {
        text = await runRouter(offlineKey);
      } catch (e: any) {
        console.warn(`Offline routing model failed (${offlineKey}); falling back to online: ${e?.message || String(e)}`);
      }
    }

    // 2) Fallback to a low‑cost online model (prefer gpt-5-nano; check availability)
    if (!text) {
      const candidates = ['gpt-5-nano','gpt-5-mini', 'gemini-2.5-flash', 'gpt-5'];
      const chosen = candidates.find((m) => isModelAvailable(m)) || 'gpt-5-mini';
      text = await runRouter(chosen);
    }

    const selectedAgent = text.trim();
    if (availableAgents.has(selectedAgent)) {
      return { agent: selectedAgent, confidence: 0.9 };
    }

    // Fallback: prefer TaskOrchestrator for unknown outputs, otherwise first registered agent
    const fallbackAgent = availableAgents.has('TaskOrchestrator')
      ? 'TaskOrchestrator'
      : (availableAgents.has('ResearchAgent') ? 'ResearchAgent' : Array.from(availableAgents.keys())[0]);
    if (!fallbackAgent) {
      throw new Error('No agents registered with orchestrator');
    }
    return {
      agent: fallbackAgent,
      confidence: 0.3,
    };
  }

  export const executeWithAgent = async (
    agentName: string,
    input: string,
    options: OrchestratorOptions = {}
): Promise <any> =>{
  return await executeWithAgentAction(agentName, input, options);
};
