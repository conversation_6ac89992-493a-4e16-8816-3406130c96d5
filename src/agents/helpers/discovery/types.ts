/**
 * Types for the project-agnostic discovery system
 */

export type ProjectLanguage = 
  | 'typescript' 
  | 'javascript' 
  | 'dart' 
  | 'rust' 
  | 'java' 
  | 'kotlin'
  | 'python' 
  | 'go' 
  | 'csharp'
  | 'ruby'
  | 'php'
  | 'swift'
  | 'cpp'
  | 'unknown';

export type BuildSystem = 
  | 'npm' 
  | 'yarn' 
  | 'pnpm'
  | 'flutter'
  | 'cargo' 
  | 'maven' 
  | 'gradle'
  | 'pip'
  | 'poetry'
  | 'go-modules'
  | 'dotnet'
  | 'bundler'
  | 'composer'
  | 'swift-package-manager'
  | 'cmake'
  | 'make'
  | 'unknown';

export interface ProjectSignature {
  /** Marker files that indicate this project type */
  markerFiles: string[];
  /** File extensions commonly used */
  extensions: string[];
  /** Build system used */
  buildSystem: BuildSystem;
  /** Confidence score (0-1) */
  confidence: number;
}

export interface DirectoryPattern {
  /** Directory path pattern (relative to project root) */
  path: string;
  /** Purpose of this directory */
  purpose: 'source' | 'test' | 'config' | 'build' | 'docs' | 'assets' | 'platform';
  /** Priority for exploration (higher = more important) */
  priority: number;
  /** Whether this is a standard convention for the language */
  isStandard: boolean;
}

export interface FilePattern {
  /** Regex pattern to match files */
  pattern: string;
  /** Purpose of files matching this pattern */
  purpose: string;
  /** Example matches for documentation */
  examples: string[];
}

export interface ConceptMapping {
  /** Generic concept (e.g., "api", "auth", "database") */
  concept: string;
  /** Language-specific keywords to search for */
  keywords: string[];
  /** File name patterns that might contain this concept */
  filePatterns: string[];
  /** Directory patterns that might contain this concept */
  directoryPatterns: string[];
}

export interface LanguageConfig {
  /** Language identifier */
  language: ProjectLanguage;
  /** Display name */
  displayName: string;
  /** Project signatures for detection */
  signatures: ProjectSignature[];
  /** Standard directory patterns */
  directories: DirectoryPattern[];
  /** File patterns for code elements */
  filePatterns: {
    function: FilePattern;
    class: FilePattern;
    interface: FilePattern;
    import: FilePattern;
    test: FilePattern;
  };
  /** Concept mappings for this language */
  concepts: ConceptMapping[];
}

export interface ProjectAnalysis {
  /** Detected primary language */
  primaryLanguage: ProjectLanguage;
  /** Additional languages detected */
  secondaryLanguages: ProjectLanguage[];
  /** Build system detected */
  buildSystem: BuildSystem;
  /** Project root directory */
  rootDir: string;
  /** Detected source directories */
  sourceDirectories: string[];
  /** Detected test directories */
  testDirectories: string[];
  /** Detected configuration files */
  configFiles: string[];
  /** Language configuration to use */
  languageConfig: LanguageConfig;
  /** Confidence in detection (0-1) */
  confidence: number;
  /** Whether this is a monorepo */
  isMonorepo: boolean;
  /** Workspace/package directories if monorepo */
  workspaces?: string[];
}

export interface DiscoveryContext {
  /** Project analysis results */
  projectAnalysis: ProjectAnalysis;
  /** Relevant directories found */
  relevantDirectories: Array<{
    path: string;
    purpose: string;
    fileCount: number;
    structure: string;
  }>;
  /** Relevant files found */
  relevantFiles: string[];
  /** Search results from code search */
  searchResults: Array<{
    pattern: string;
    concept: string;
    context?: string;
    matches: Array<{ file: string; line: number; snippet: string }>;
  }>;
  /** Suggested scope files based on analysis */
  suggestedScopeFiles: string[];
  /** Suggested scope paths based on analysis */
  suggestedScopePaths: string[];
  /** Search hints for the agent */
  searchHints: Array<{
    pattern: string;
    context: string;
  }>;
}

export interface DiscoveryOptions {
  /** Maximum number of directories to explore */
  maxDirectories?: number;
  /** Maximum number of files to return */
  maxFiles?: number;
  /** Maximum number of search patterns to try */
  maxSearchPatterns?: number;
  /** Whether to include test files */
  includeTests?: boolean;
  /** Custom project root (defaults to cwd) */
  projectRoot?: string;
  /** Force a specific language (skip detection) */
  forceLanguage?: ProjectLanguage;
}

