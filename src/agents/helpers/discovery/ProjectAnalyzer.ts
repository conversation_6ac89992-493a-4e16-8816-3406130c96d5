/**
 * ProjectAnalyzer - Detects project language, structure, and conventions
 * 
 * This class analyzes a codebase to determine:
 * - Primary programming language(s)
 * - Build system and project structure
 * - Standard directories (source, test, config)
 * - Whether it's a monorepo
 * 
 * The analysis is used to configure language-specific discovery patterns.
 */

import { promises as fs } from 'fs';
import * as path from 'path';
import type { ProjectAnalysis, ProjectLanguage, LanguageConfig, BuildSystem } from './types';
import { LANGUAGE_CONFIGS, UNKNOWN_CONFIG } from './language-configs';

export class ProjectAnalyzer {
  private rootDir: string;

  constructor(rootDir: string = process.cwd()) {
    this.rootDir = path.resolve(rootDir);
  }

  /**
   * Analyze the project and return comprehensive analysis
   */
  async analyze(options?: { forceLanguage?: ProjectLanguage }): Promise<ProjectAnalysis> {
    // Detect languages by scanning for marker files
    const detectedLanguages = await this.detectLanguages();

    // Determine primary language (allow override)
    const primaryLanguage: ProjectLanguage = options?.forceLanguage || detectedLanguages[0] || 'unknown';
    const secondaryLanguages = (options?.forceLanguage
      ? detectedLanguages.filter(l => l !== options.forceLanguage)
      : detectedLanguages.slice(1));

    // Get language config (honor forced language)
    const languageConfig = LANGUAGE_CONFIGS[primaryLanguage] || UNKNOWN_CONFIG;

    // Detect build system
    const buildSystem = await this.detectBuildSystem();

    // Detect monorepo
    const { isMonorepo, workspaces } = await this.detectMonorepo();

    // Find source and test directories using the chosen language config
    const sourceDirectories = await this.findDirectories(languageConfig, 'source');
    const testDirectories = await this.findDirectories(languageConfig, 'test');

    // Find configuration files
    const configFiles = await this.findConfigFiles();

    // Calculate confidence based on detection quality
    const confidence = this.calculateConfidence(primaryLanguage, buildSystem, sourceDirectories);

    return {
      primaryLanguage,
      secondaryLanguages,
      buildSystem,
      rootDir: this.rootDir,
      sourceDirectories,
      testDirectories,
      configFiles,
      languageConfig,
      confidence,
      isMonorepo,
      workspaces,
    };
  }

  /**
   * Detect programming languages by looking for marker files
   */
  private async detectLanguages(): Promise<ProjectLanguage[]> {
    const detectedLanguages: Array<{ language: ProjectLanguage; confidence: number }> = [];

    for (const [langKey, config] of Object.entries(LANGUAGE_CONFIGS)) {
      for (const signature of config.signatures) {
        const hasMarkers = await this.hasMarkerFiles(signature.markerFiles);
        if (hasMarkers) {
          detectedLanguages.push({
            language: langKey as ProjectLanguage,
            confidence: signature.confidence,
          });
          break; // Found this language, move to next
        }
      }
    }

    // Sort by confidence
    detectedLanguages.sort((a, b) => b.confidence - a.confidence);

    return detectedLanguages.map(d => d.language);
  }

  /**
   * Check if marker files exist in the project root
   */
  private async hasMarkerFiles(markerFiles: string[]): Promise<boolean> {
    for (const file of markerFiles) {
      try {
        await fs.access(path.join(this.rootDir, file));
        return true; // At least one marker file exists
      } catch {
        // File doesn't exist, continue
      }
    }
    return false;
  }

  /**
   * Detect build system based on marker files
   */
  private async detectBuildSystem(): Promise<BuildSystem> {
    const buildSystemMarkers: Array<{ files: string[]; system: BuildSystem }> = [
      { files: ['package.json'], system: 'npm' },
      { files: ['yarn.lock'], system: 'yarn' },
      { files: ['pnpm-lock.yaml'], system: 'pnpm' },
      { files: ['pubspec.yaml'], system: 'flutter' },
      { files: ['Cargo.toml'], system: 'cargo' },
      { files: ['pom.xml'], system: 'maven' },
      { files: ['build.gradle', 'build.gradle.kts'], system: 'gradle' },
      { files: ['requirements.txt', 'setup.py'], system: 'pip' },
      { files: ['pyproject.toml'], system: 'poetry' },
      { files: ['go.mod'], system: 'go-modules' },
      { files: ['*.csproj', '*.sln'], system: 'dotnet' },
      { files: ['Gemfile'], system: 'bundler' },
      { files: ['composer.json'], system: 'composer' },
      { files: ['Package.swift'], system: 'swift-package-manager' },
      { files: ['CMakeLists.txt'], system: 'cmake' },
      { files: ['Makefile'], system: 'make' },
    ];

    for (const { files, system } of buildSystemMarkers) {
      if (await this.hasMarkerFiles(files)) {
        return system;
      }
    }

    return 'unknown';
  }

  /**
   * Detect if this is a monorepo and find workspaces
   */
  private async detectMonorepo(): Promise<{ isMonorepo: boolean; workspaces?: string[] }> {
    // Check for npm/yarn/pnpm workspaces
    try {
      const packageJsonPath = path.join(this.rootDir, 'package.json');
      const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf-8'));
      
      if (packageJson.workspaces) {
        const workspaces = Array.isArray(packageJson.workspaces)
          ? packageJson.workspaces
          : packageJson.workspaces.packages || [];
        return { isMonorepo: true, workspaces };
      }
    } catch {
      // Not a Node.js project or no workspaces
    }

    // Check for Cargo workspace
    try {
      const cargoTomlPath = path.join(this.rootDir, 'Cargo.toml');
      const cargoToml = await fs.readFile(cargoTomlPath, 'utf-8');
      if (cargoToml.includes('[workspace]')) {
        // Parse workspace members (simplified)
        const membersMatch = cargoToml.match(/members\s*=\s*\[([\s\S]*?)\]/);
        if (membersMatch) {
          const workspaces = membersMatch[1]
            .split(',')
            .map(m => m.trim().replace(/['"]/g, ''))
            .filter(Boolean);
          return { isMonorepo: true, workspaces };
        }
        return { isMonorepo: true };
      }
    } catch {
      // Not a Rust project or no workspace
    }

    // Check for common monorepo indicators
    const monorepoIndicators = ['packages', 'apps', 'libs', 'crates', 'modules'];
    for (const indicator of monorepoIndicators) {
      try {
        const indicatorPath = path.join(this.rootDir, indicator);
        const stat = await fs.stat(indicatorPath);
        if (stat.isDirectory()) {
          const entries = await fs.readdir(indicatorPath);
          // If it has multiple subdirectories, likely a monorepo
          const subdirs = await Promise.all(
            entries.map(async (entry) => {
              try {
                const stat = await fs.stat(path.join(indicatorPath, entry));
                return stat.isDirectory();
              } catch {
                return false;
              }
            })
          );
          if (subdirs.filter(Boolean).length > 1) {
            return { isMonorepo: true, workspaces: [indicator] };
          }
        }
      } catch {
        // Directory doesn't exist
      }
    }

    return { isMonorepo: false };
  }

  /**
   * Find directories matching a specific purpose
   */
  private async findDirectories(
    config: LanguageConfig,
    purpose: 'source' | 'test' | 'config' | 'build' | 'docs' | 'assets' | 'platform'
  ): Promise<string[]> {
    const directories: string[] = [];
    const patterns = config.directories.filter(d => d.purpose === purpose);

    for (const pattern of patterns) {
      try {
        const dirPath = path.join(this.rootDir, pattern.path);
        await fs.access(dirPath);
        directories.push(pattern.path);
      } catch {
        // Directory doesn't exist
      }
    }

    return directories;
  }

  /**
   * Find common configuration files
   */
  private async findConfigFiles(): Promise<string[]> {
    const commonConfigFiles = [
      '.env',
      '.env.example',
      'config.json',
      'config.yaml',
      'config.yml',
      'settings.json',
      'tsconfig.json',
      'jsconfig.json',
      'babel.config.js',
      'webpack.config.js',
      'vite.config.ts',
      'rollup.config.js',
      '.eslintrc',
      '.prettierrc',
      'pubspec.yaml',
      'Cargo.toml',
      'pom.xml',
      'build.gradle',
      'go.mod',
    ];

    const foundFiles: string[] = [];

    for (const file of commonConfigFiles) {
      try {
        await fs.access(path.join(this.rootDir, file));
        foundFiles.push(file);
      } catch {
        // File doesn't exist
      }
    }

    return foundFiles;
  }

  /**
   * Calculate confidence in the analysis
   */
  private calculateConfidence(
    language: ProjectLanguage,
    buildSystem: BuildSystem,
    sourceDirectories: string[]
  ): number {
    let confidence = 0;

    // Language detection confidence
    if (language !== 'unknown') {
      confidence += 0.4;
    }

    // Build system detection confidence
    if (buildSystem !== 'unknown') {
      confidence += 0.3;
    }

    // Source directory detection confidence
    if (sourceDirectories.length > 0) {
      confidence += 0.3;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * Get file extensions for the detected language
   */
  getFileExtensions(analysis: ProjectAnalysis): string[] {
    const config = analysis.languageConfig;
    const extensions: string[] = [];

    for (const signature of config.signatures) {
      extensions.push(...signature.extensions);
    }

    return [...new Set(extensions)]; // Remove duplicates
  }
}

