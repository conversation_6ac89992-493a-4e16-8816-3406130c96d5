/**
 * Language-specific configurations for project discovery
 * 
 * Each language config defines:
 * - How to detect the language (marker files, extensions)
 * - Standard directory structures
 * - Code patterns (functions, classes, imports)
 * - Concept mappings (API → routes/controllers, auth → authentication files)
 */

import type { LanguageConfig, ConceptMapping } from './types';

// Common concept mappings used across languages
const commonConcepts: ConceptMapping[] = [
  {
    concept: 'api',
    keywords: ['api', 'route', 'router', 'endpoint', 'controller', 'handler', 'server'],
    filePatterns: ['*route*', '*router*', '*controller*', '*handler*', '*api*', '*endpoint*', '*server*'],
    directoryPatterns: ['api', 'routes', 'controllers', 'handlers', 'endpoints', 'server'],
  },
  {
    concept: 'authentication',
    keywords: ['auth', 'login', 'jwt', 'oauth', 'token', 'session', 'security', 'credential'],
    filePatterns: ['*auth*', '*login*', '*jwt*', '*oauth*', '*token*', '*session*', '*security*'],
    directoryPatterns: ['auth', 'authentication', 'security', 'middleware'],
  },
  {
    concept: 'database',
    keywords: ['model', 'entity', 'schema', 'repository', 'dao', 'database', 'db', 'query'],
    filePatterns: ['*model*', '*entity*', '*schema*', '*repository*', '*dao*', '*database*', '*db*'],
    directoryPatterns: ['models', 'entities', 'schemas', 'repositories', 'dao', 'database', 'db', 'data'],
  },
  {
    concept: 'testing',
    keywords: ['test', 'spec', 'mock', 'fixture', 'assert'],
    filePatterns: ['*test*', '*spec*', '*.test.*', '*.spec.*', '*_test.*', '*_spec.*'],
    directoryPatterns: ['test', 'tests', '__tests__', 'spec', 'specs'],
  },
  {
    concept: 'configuration',
    keywords: ['config', 'settings', 'environment', 'env'],
    filePatterns: ['*config*', '*settings*', '*.env*', '*environment*'],
    directoryPatterns: ['config', 'configuration', 'settings'],
  },
  {
    concept: 'ui',
    keywords: ['component', 'view', 'page', 'screen', 'widget', 'ui', 'interface'],
    filePatterns: ['*component*', '*view*', '*page*', '*screen*', '*widget*', '*ui*'],
    directoryPatterns: ['components', 'views', 'pages', 'screens', 'widgets', 'ui'],
  },
  {
    concept: 'service',
    keywords: ['service', 'provider', 'manager', 'client', 'adapter'],
    filePatterns: ['*service*', '*provider*', '*manager*', '*client*', '*adapter*'],
    directoryPatterns: ['services', 'providers', 'managers', 'clients', 'adapters'],
  },
  {
    concept: 'utility',
    keywords: ['util', 'helper', 'common', 'shared', 'lib'],
    filePatterns: ['*util*', '*helper*', '*common*', '*shared*'],
    directoryPatterns: ['utils', 'utilities', 'helpers', 'common', 'shared', 'lib'],
  },
];

export const TYPESCRIPT_CONFIG: LanguageConfig = {
  language: 'typescript',
  displayName: 'TypeScript',
  signatures: [
    {
      markerFiles: ['tsconfig.json', 'package.json'],
      extensions: ['.ts', '.tsx'],
      buildSystem: 'npm',
      confidence: 0.9,
    },
  ],
  directories: [
    { path: 'src', purpose: 'source', priority: 10, isStandard: true },
    { path: 'lib', purpose: 'source', priority: 8, isStandard: true },
    { path: 'test', purpose: 'test', priority: 5, isStandard: true },
    { path: 'tests', purpose: 'test', priority: 5, isStandard: true },
    { path: '__tests__', purpose: 'test', priority: 5, isStandard: true },
    { path: 'dist', purpose: 'build', priority: 1, isStandard: true },
    { path: 'build', purpose: 'build', priority: 1, isStandard: true },
    { path: 'public', purpose: 'assets', priority: 3, isStandard: true },
    { path: 'docs', purpose: 'docs', priority: 2, isStandard: true },
  ],
  filePatterns: {
    function: {
      pattern: '(function\\s+\\w+|const\\s+\\w+\\s*=\\s*\\(|export\\s+(async\\s+)?function)',
      purpose: 'Function definitions',
      examples: ['function foo()', 'const bar = ()', 'export async function baz()'],
    },
    class: {
      pattern: '(class\\s+\\w+|export\\s+class\\s+\\w+)',
      purpose: 'Class definitions',
      examples: ['class Foo', 'export class Bar'],
    },
    interface: {
      pattern: '(interface\\s+\\w+|type\\s+\\w+\\s*=)',
      purpose: 'Type definitions',
      examples: ['interface Foo', 'type Bar ='],
    },
    import: {
      pattern: '(import\\s+.*from|import\\s*\\{)',
      purpose: 'Import statements',
      examples: ['import { foo } from', 'import * as bar from'],
    },
    test: {
      pattern: '(describe\\(|test\\(|it\\(|expect\\()',
      purpose: 'Test cases',
      examples: ['describe("test")', 'test("should")', 'it("works")'],
    },
  },
  concepts: commonConcepts,
};

export const DART_CONFIG: LanguageConfig = {
  language: 'dart',
  displayName: 'Dart/Flutter',
  signatures: [
    {
      markerFiles: ['pubspec.yaml', 'analysis_options.yaml'],
      extensions: ['.dart'],
      buildSystem: 'flutter',
      confidence: 0.95,
    },
  ],
  directories: [
    { path: 'lib', purpose: 'source', priority: 10, isStandard: true },
    { path: 'lib/src', purpose: 'source', priority: 9, isStandard: true },
    { path: 'test', purpose: 'test', priority: 5, isStandard: true },
    { path: 'lib/models', purpose: 'source', priority: 8, isStandard: false },
    { path: 'lib/services', purpose: 'source', priority: 8, isStandard: false },
    { path: 'lib/widgets', purpose: 'source', priority: 8, isStandard: false },
    { path: 'lib/screens', purpose: 'source', priority: 8, isStandard: false },
    { path: 'lib/providers', purpose: 'source', priority: 7, isStandard: false },
    { path: 'android', purpose: 'platform', priority: 2, isStandard: true },
    { path: 'ios', purpose: 'platform', priority: 2, isStandard: true },
    { path: 'web', purpose: 'platform', priority: 2, isStandard: true },
    { path: 'assets', purpose: 'assets', priority: 3, isStandard: true },
  ],
  filePatterns: {
    function: {
      pattern: '(\\w+\\s+\\w+\\s*\\(|Future<\\w+>\\s+\\w+\\s*\\()',
      purpose: 'Function definitions',
      examples: ['void foo()', 'Future<String> bar()', 'int baz()'],
    },
    class: {
      pattern: '(class\\s+\\w+|abstract\\s+class\\s+\\w+)',
      purpose: 'Class definitions',
      examples: ['class Foo', 'abstract class Bar'],
    },
    interface: {
      pattern: '(abstract\\s+class\\s+\\w+|mixin\\s+\\w+)',
      purpose: 'Interface/mixin definitions',
      examples: ['abstract class IFoo', 'mixin Bar'],
    },
    import: {
      pattern: '(import\\s+[\'"]|export\\s+[\'"])',
      purpose: 'Import statements',
      examples: ['import "package:foo/bar.dart"', 'export "lib/baz.dart"'],
    },
    test: {
      pattern: '(test\\(|group\\(|expect\\(|testWidgets\\()',
      purpose: 'Test cases',
      examples: ['test("should")', 'group("tests")', 'testWidgets("widget")'],
    },
  },
  concepts: [
    ...commonConcepts,
    {
      concept: 'widget',
      keywords: ['widget', 'stateless', 'stateful', 'build'],
      filePatterns: ['*widget*', '*screen*', '*page*'],
      directoryPatterns: ['widgets', 'screens', 'pages', 'views'],
    },
    {
      concept: 'provider',
      keywords: ['provider', 'notifier', 'state', 'bloc', 'cubit'],
      filePatterns: ['*provider*', '*notifier*', '*state*', '*bloc*', '*cubit*'],
      directoryPatterns: ['providers', 'state', 'bloc', 'cubit'],
    },
  ],
};

export const RUST_CONFIG: LanguageConfig = {
  language: 'rust',
  displayName: 'Rust',
  signatures: [
    {
      markerFiles: ['Cargo.toml', 'Cargo.lock'],
      extensions: ['.rs'],
      buildSystem: 'cargo',
      confidence: 0.95,
    },
  ],
  directories: [
    { path: 'src', purpose: 'source', priority: 10, isStandard: true },
    { path: 'tests', purpose: 'test', priority: 5, isStandard: true },
    { path: 'benches', purpose: 'test', priority: 4, isStandard: true },
    { path: 'examples', purpose: 'docs', priority: 3, isStandard: true },
    { path: 'target', purpose: 'build', priority: 1, isStandard: true },
  ],
  filePatterns: {
    function: {
      pattern: '(fn\\s+\\w+|pub\\s+fn\\s+\\w+|async\\s+fn\\s+\\w+)',
      purpose: 'Function definitions',
      examples: ['fn foo()', 'pub fn bar()', 'async fn baz()'],
    },
    class: {
      pattern: '(struct\\s+\\w+|enum\\s+\\w+|pub\\s+struct\\s+\\w+)',
      purpose: 'Type definitions',
      examples: ['struct Foo', 'enum Bar', 'pub struct Baz'],
    },
    interface: {
      pattern: '(trait\\s+\\w+|pub\\s+trait\\s+\\w+)',
      purpose: 'Trait definitions',
      examples: ['trait Foo', 'pub trait Bar'],
    },
    import: {
      pattern: '(use\\s+\\w+|use\\s+\\{)',
      purpose: 'Use statements',
      examples: ['use std::io', 'use { foo, bar }'],
    },
    test: {
      pattern: '(#\\[test\\]|#\\[cfg\\(test\\)\\])',
      purpose: 'Test cases',
      examples: ['#[test]', '#[cfg(test)]'],
    },
  },
  concepts: commonConcepts,
};

export const PYTHON_CONFIG: LanguageConfig = {
  language: 'python',
  displayName: 'Python',
  signatures: [
    {
      markerFiles: ['requirements.txt', 'setup.py', 'pyproject.toml', 'Pipfile'],
      extensions: ['.py'],
      buildSystem: 'pip',
      confidence: 0.85,
    },
  ],
  directories: [
    { path: 'src', purpose: 'source', priority: 10, isStandard: true },
    { path: 'tests', purpose: 'test', priority: 5, isStandard: true },
    { path: 'test', purpose: 'test', priority: 5, isStandard: true },
    { path: 'docs', purpose: 'docs', priority: 2, isStandard: true },
  ],
  filePatterns: {
    function: {
      pattern: '(def\\s+\\w+\\s*\\(|async\\s+def\\s+\\w+\\s*\\()',
      purpose: 'Function definitions',
      examples: ['def foo():', 'async def bar():'],
    },
    class: {
      pattern: '(class\\s+\\w+)',
      purpose: 'Class definitions',
      examples: ['class Foo:', 'class Bar(Base):'],
    },
    interface: {
      pattern: '(class\\s+\\w+.*Protocol|@abstractmethod)',
      purpose: 'Protocol/abstract definitions',
      examples: ['class IFoo(Protocol):', '@abstractmethod'],
    },
    import: {
      pattern: '(import\\s+\\w+|from\\s+\\w+\\s+import)',
      purpose: 'Import statements',
      examples: ['import foo', 'from bar import baz'],
    },
    test: {
      pattern: '(def\\s+test_|class\\s+Test\\w+|@pytest)',
      purpose: 'Test cases',
      examples: ['def test_foo():', 'class TestBar:', '@pytest.fixture'],
    },
  },
  concepts: commonConcepts,
};

// Export all configs as a map
export const LANGUAGE_CONFIGS: Record<string, LanguageConfig> = {
  typescript: TYPESCRIPT_CONFIG,
  javascript: { ...TYPESCRIPT_CONFIG, language: 'javascript', displayName: 'JavaScript' },
  dart: DART_CONFIG,
  rust: RUST_CONFIG,
  python: PYTHON_CONFIG,
};

// Default fallback config for unknown languages
export const UNKNOWN_CONFIG: LanguageConfig = {
  language: 'unknown',
  displayName: 'Unknown',
  signatures: [],
  directories: [
    { path: 'src', purpose: 'source', priority: 10, isStandard: false },
    { path: 'lib', purpose: 'source', priority: 8, isStandard: false },
    { path: 'test', purpose: 'test', priority: 5, isStandard: false },
    { path: 'tests', purpose: 'test', priority: 5, isStandard: false },
  ],
  filePatterns: {
    function: { pattern: '', purpose: 'Function definitions', examples: [] },
    class: { pattern: '', purpose: 'Class definitions', examples: [] },
    interface: { pattern: '', purpose: 'Interface definitions', examples: [] },
    import: { pattern: '', purpose: 'Import statements', examples: [] },
    test: { pattern: 'test', purpose: 'Test cases', examples: [] },
  },
  concepts: commonConcepts,
};

