/**
 * DiscoveryEngine - Project-agnostic code discovery system
 *
 * This engine:
 * 1. Analyzes the project to detect language and structure
 * 2. Maps task keywords to language-specific concepts
 * 3. Searches for relevant files and directories
 * 4. Generates scope suggestions for agents
 *
 * Works across TypeScript, Dart, Rust, Python, Java, and more.
 */

import { ProjectAnalyzer } from './ProjectAnalyzer';
import type { DiscoveryContext, DiscoveryOptions, ConceptMapping, ProjectAnalysis } from './types';
import { promises as fs } from 'fs';
import * as path from 'path';

export class DiscoveryEngine {
  private analyzer: ProjectAnalyzer;
  private projectAnalysis: ProjectAnalysis | null = null;

  constructor(projectRoot?: string) {
    this.analyzer = new ProjectAnalyzer(projectRoot);
  }

  /**
   * Discover relevant files and directories for a given task query
   */
  async discover(query: string, options: DiscoveryOptions = {}): Promise<DiscoveryContext> {
    // Analyze a project if not already done, or re-analyze when forcing language
    if (!this.projectAnalysis || options.forceLanguage) {
      this.projectAnalysis = await this.analyzer.analyze({ forceLanguage: options.forceLanguage });
    }

    const analysis = this.projectAnalysis;

    // Extract concepts from query
    const concepts = this.extractConcepts(query, analysis);

    // Discover directories based on concepts
    const relevantDirectories = await this.discoverDirectories(concepts, analysis, options);

    // Discover files based on concepts
    const relevantFiles = await this.discoverFiles(concepts, analysis, options);

    // Generate search results with actual file searches
    const searchResults = await this.generateSearchHints(concepts, analysis);

    // Generate scope suggestions
    const suggestedScopeFiles = this.suggestScopeFiles(relevantFiles, options);
    const suggestedScopePaths = this.suggestScopePaths(relevantDirectories, analysis);

    // Generate search hints for agents
    const searchHints = this.generateAgentSearchHints(concepts, analysis);

    return {
      projectAnalysis: analysis,
      relevantDirectories,
      relevantFiles,
      searchResults,
      suggestedScopeFiles,
      suggestedScopePaths,
      searchHints,
    };
  }

  /**
   * Extract concepts from the query using language-specific mappings
   */
  private extractConcepts(query: string, analysis: ProjectAnalysis): ConceptMapping[] {
    const queryLower = query.toLowerCase();
    const matchedConcepts: ConceptMapping[] = [];

    for (const concept of analysis.languageConfig.concepts) {
      // Check if any keywords match the query
      const hasMatch = concept.keywords.some(keyword =>
        queryLower.includes(keyword.toLowerCase())
      );

      if (hasMatch) {
        matchedConcepts.push(concept);
      }
    }

    return matchedConcepts;
  }

  /**
   * Discover relevant directories based on concepts
   */
  private async discoverDirectories(
    concepts: ConceptMapping[],
    analysis: ProjectAnalysis,
    options: DiscoveryOptions
  ): Promise<Array<{ path: string; purpose: string; fileCount: number; structure: string }>> {
    const directories: Array<{ path: string; purpose: string; fileCount: number; structure: string }> = [];
    const maxDirectories = options.maxDirectories || 5;

    // Get directory patterns from concepts
    const conceptDirPatterns = new Set<string>();
    for (const concept of concepts) {
      for (const dirPattern of concept.directoryPatterns) {
        conceptDirPatterns.add(dirPattern);
      }
    }

    // Always include standard source directories
    for (const sourceDir of analysis.sourceDirectories) {
      try {
        const dirPath = path.join(analysis.rootDir, sourceDir);
        const stat = await fs.stat(dirPath);

        if (stat.isDirectory()) {
          const entries = await fs.readdir(dirPath);
          const fileCount = entries.length;
          const structure = await this.buildDirectoryStructure(dirPath, entries.slice(0, 10));

          directories.push({
            path: sourceDir,
            purpose: 'Source code',
            fileCount,
            structure,
          });

          // Search for subdirectories matching concept patterns
          for (const entry of entries) {
            if (directories.length >= maxDirectories) break;

            const entryPath = path.join(dirPath, entry);
            try {
              const entryStat = await fs.stat(entryPath);
              if (entryStat.isDirectory()) {
                const entryLower = entry.toLowerCase();

                // Check if this subdirectory matches any concept pattern
                for (const pattern of conceptDirPatterns) {
                  if (entryLower.includes(pattern.toLowerCase())) {
                    const subEntries = await fs.readdir(entryPath);
                    const subStructure = await this.buildDirectoryStructure(entryPath, subEntries.slice(0, 10));

                    directories.push({
                      path: path.join(sourceDir, entry),
                      purpose: this.inferDirectoryPurpose(entry, analysis),
                      fileCount: subEntries.length,
                      structure: subStructure,
                    });
                    break; // Found a match, move to the next entry
                  }
                }
              }
            } catch {
              // Can't read subdirectory, skip it
            }
          }
        }
      } catch {
        // Can't read the source directory, skip it
      }
    }

    // Add test directories if requested
    if (options.includeTests && directories.length < maxDirectories) {
      for (const testDir of analysis.testDirectories) {
        if (directories.length >= maxDirectories) break;

        try {
          const dirPath = path.join(analysis.rootDir, testDir);
          const stat = await fs.stat(dirPath);

          if (stat.isDirectory()) {
            const entries = await fs.readdir(dirPath);
            const fileCount = entries.length;
            const structure = await this.buildDirectoryStructure(dirPath, entries.slice(0, 10));

            directories.push({
              path: testDir,
              purpose: 'Tests',
              fileCount,
              structure,
            });
          }
        } catch {
          // Can't read the test directory, skip it
        }
      }
    }

    return directories;
  }

  /**
   * Build a simple directory structure string
   */
  private async buildDirectoryStructure(dirPath: string, entries: string[]): Promise<string> {
    const lines: string[] = [path.basename(dirPath) + '/'];

    for (const entry of entries) {
      try {
        const entryPath = path.join(dirPath, entry);
        const stat = await fs.stat(entryPath);
        const prefix = stat.isDirectory() ? '📁 ' : '📄 ';
        lines.push(`  ${prefix}${entry}`);
      } catch {
        lines.push(`  ${entry}`);
      }
    }

    if (entries.length > 10) {
      lines.push('  ...');
    }

    return lines.join('\n');
  }

  /**
   * Discover relevant files based on concepts
   */
  private async discoverFiles(
    concepts: ConceptMapping[],
    analysis: ProjectAnalysis,
    options: DiscoveryOptions
  ): Promise<string[]> {
    const files = new Set<string>();
    const maxFiles = options.maxFiles || 20;

    // Get file patterns from concepts
    const filePatterns = new Set<string>();
    for (const concept of concepts) {
      for (const pattern of concept.filePatterns) {
        filePatterns.add(pattern);
      }
    }

    // Search for files matching patterns in source directories
    const patterns = Array.from(filePatterns).slice(0, options.maxSearchPatterns || 5);
    const extensions = this.analyzer.getFileExtensions(analysis);

    for (const sourceDir of analysis.sourceDirectories.slice(0, 3)) {
      const dirPath = path.join(analysis.rootDir, sourceDir);

      try {
        await fs.access(dirPath);

        // Search for files matching patterns
        for (const pattern of patterns) {
          const foundFiles = await this.searchFilesInDirectory(
            dirPath,
            pattern,
            extensions,
            Math.min(5, maxFiles - files.size)
          );

          for (const file of foundFiles) {
            const relativePath = path.relative(analysis.rootDir, file);
            files.add(relativePath);

            if (files.size >= maxFiles) {
              return Array.from(files);
            }
          }
        }
      } catch {
      }
    }

    return Array.from(files).slice(0, maxFiles);
  }

  /**
   * Search for files in a directory matching a pattern
   */
  private async searchFilesInDirectory(
    dirPath: string,
    pattern: string,
    extensions: string[],
    maxResults: number,
    depth: number = 0
  ): Promise<string[]> {
    const results: string[] = [];
    const cleanPattern = pattern.replace(/\*/g, '').toLowerCase();

    // Extract keywords from a pattern (split by common separators)
    const keywords = cleanPattern.split(/[_\-\/\\]/).filter(k => k.length > 2);

    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        if (results.length >= maxResults) break;

        // Skip hidden files and common ignore patterns
        if (entry.name.startsWith('.') || entry.name === 'node_modules') {
          continue;
        }

        const fullPath = path.join(dirPath, entry.name);

        if (entry.isDirectory()) {
          // Recursively search subdirectories (max depth 3)
          if (depth < 3) {
            const subResults = await this.searchFilesInDirectory(
              fullPath,
              pattern,
              extensions,
              maxResults - results.length,
              depth + 1
            );
            results.push(...subResults);
          }
        } else {
          // Check if filename matches pattern and has correct extension
          const nameLower = entry.name.toLowerCase();
          const ext = path.extname(entry.name);

          // Match if any keyword is found in the filename
          const matchesPattern = cleanPattern === '' ||
            keywords.length === 0 ||
            keywords.some(keyword => nameLower.includes(keyword));
          const matchesExtension = extensions.length === 0 || extensions.includes(ext);

          if (matchesPattern && matchesExtension) {
            results.push(fullPath);
          }
        }
      }
    } catch {
      // Can't read the directory, return what we have
    }

    return results;
  }

  /**
   * Generate search hints based on concepts
   */
  private async generateSearchHints(
    concepts: ConceptMapping[],
    analysis: ProjectAnalysis
  ): Promise<Array<{ pattern: string; concept: string; context: string; matches: Array<{ file: string; line: number; snippet: string }> }>> {
    const searchResults: Array<{ pattern: string; concept: string; context: string; matches: any[] }> = [];

    const primaryExt = this.getFileExtension(analysis);
    const langLabel = analysis.languageConfig.displayName || analysis.primaryLanguage;
    const buildLabel = analysis.buildSystem !== 'unknown' ? ` using ${analysis.buildSystem}` : '';

    for (const concept of concepts.slice(0, 3)) {
      // Limit to top 3 concepts
      const pattern = this.buildSearchPattern(concept, analysis);

      // Try to find actual matches in source files
      const matches = await this.searchForPattern(pattern, analysis);

      const extInfo = primaryExt ? ` (.${primaryExt})` : '';
      const context = `Finding ${concept.concept}-related code in ${langLabel}${extInfo}${buildLabel}`;

      searchResults.push({
        pattern,
        concept: concept.concept,
        context,
        matches: matches.slice(0, 5), // Limit to 5 matches per concept
      });
    }

    return searchResults;
  }

  /**
   * Search for a pattern in source files
   */
  private async searchForPattern(
    pattern: string,
    analysis: ProjectAnalysis
  ): Promise<Array<{ file: string; line: number; snippet: string }>> {
    const matches: Array<{ file: string; line: number; snippet: string }> = [];
    const extensions = this.analyzer.getFileExtensions(analysis);

    // Search in the first source directory only to keep it fast
    if (analysis.sourceDirectories.length === 0) {
      return matches;
    }

    const sourceDir = path.join(analysis.rootDir, analysis.sourceDirectories[0]);

    try {
      await fs.access(sourceDir);

      // Simple pattern search (case-insensitive)
      const keywords = pattern.split('|').slice(0, 3); // Take the first 3 keywords

      for (const keyword of keywords) {
        const found = await this.searchKeywordInDirectory(
          sourceDir,
          keyword,
          extensions,
          3 // Max 3 matches per keyword
        );
        matches.push(...found);

        if (matches.length >= 5) break;
      }
    } catch {
      // Can't access directory
    }

    return matches.slice(0, 5);
  }

  /**
   * Search for a keyword in a directory
   */
  private async searchKeywordInDirectory(
    dirPath: string,
    keyword: string,
    extensions: string[],
    maxResults: number,
    depth: number = 0
  ): Promise<Array<{ file: string; line: number; snippet: string }>> {
    const results: Array<{ file: string; line: number; snippet: string }> = [];
    const keywordLower = keyword.toLowerCase();

    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        if (results.length >= maxResults) break;

        if (entry.name.startsWith('.') || entry.name === 'node_modules') {
          continue;
        }

        const fullPath = path.join(dirPath, entry.name);

        if (entry.isDirectory()) {
          // Recursively search subdirectories (max depth 2)
          if (depth < 2) {
            const subResults = await this.searchKeywordInDirectory(
              fullPath,
              keyword,
              extensions,
              maxResults - results.length,
              depth + 1
            );
            results.push(...subResults);
          }
        } else {
          const ext = path.extname(entry.name);
          if (extensions.length > 0 && !extensions.includes(ext)) {
            continue;
          }

          try {
            const content = await fs.readFile(fullPath, 'utf-8');
            const lines = content.split('\n');

            for (let i = 0; i < lines.length && results.length < maxResults; i++) {
              const line = lines[i];
              if (line.toLowerCase().includes(keywordLower)) {
                results.push({
                  file: fullPath,
                  line: i + 1,
                  snippet: line.trim().slice(0, 100),
                });
              }
            }
          } catch {
            // Can't read a file, skip it
          }
        }
      }
    } catch {
      // Can't read directory
    }

    return results;
  }

  /**
   * Build a search pattern for a concept using language- and project-specific context
   */
  private buildSearchPattern(concept: ConceptMapping, analysis: ProjectAnalysis): string {
    // Base: top concept keywords
    const base = concept.keywords.slice(0, 3);

    // Primary language and extension
    const primaryExt = this.getFileExtension(analysis); // e.g., 'ts', 'js', 'py'
    const extTokens = primaryExt ? [primaryExt, `.${primaryExt}`] : [];

    // Include language-specific file and directory patterns as tokens
    const lang = analysis.languageConfig;
    const dirTokens = (concept.directoryPatterns || []).map(d => d.toLowerCase());
    const fileTokens = (concept.filePatterns || []).map(f => f.toLowerCase());

    // Add common file pattern tokens from the language config
    const languageFilePatternTokens = [
      lang.filePatterns.function?.pattern,
      lang.filePatterns.class?.pattern,
      lang.filePatterns.interface?.pattern,
      lang.filePatterns.import?.pattern,
      lang.filePatterns.test?.pattern,
    ]
      .filter(Boolean)
      .map(p => String(p).toLowerCase());

    // Merge and dedupe tokens
    const tokens = Array.from(
      new Set(
        [
          ...base,
          ...extTokens,
          ...dirTokens,
          ...fileTokens,
          ...languageFilePatternTokens,
        ]
          .map(t => t.trim())
          .filter(Boolean)
      )
    );

    // Keep pattern compact: prefer concept keywords + a light language hint
    const keywords = [
      ...tokens.slice(0, 3), // top concept tokens
      ...extTokens.slice(0, 1), // one extension hint
    ].slice(0, 5);

    // Escape regex special chars in tokens and join with OR
    const escaped = keywords.map(k => k.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'));
    return escaped.join('|');
  }

  /**
   * Suggest scope files from discovered files
   */
  private suggestScopeFiles(files: string[], options: DiscoveryOptions): string[] {
    const maxFiles = options.maxFiles || 10;
    return files.slice(0, maxFiles);
  }

  /**
   * Suggest scope paths from discovered directories
   */
  private suggestScopePaths(
    directories: Array<{ path: string; purpose: string }>,
    analysis: ProjectAnalysis
  ): string[] {
    // Prioritize source directories
    const paths = directories
      .filter(d => d.purpose === 'source' || d.purpose === 'Source code')
      .map(d => d.path);

    // Add a primary source directory if not already included
    if (analysis.sourceDirectories.length > 0 && !paths.includes(analysis.sourceDirectories[0])) {
      paths.unshift(analysis.sourceDirectories[0]);
    }

    return paths;
  }

  /**
   * Generate search hints for agents
   */
  private generateAgentSearchHints(
    concepts: ConceptMapping[],
    analysis: ProjectAnalysis
  ): Array<{ pattern: string; context: string }> {
    const hints: Array<{ pattern: string; context: string }> = [];

    const primaryExt = this.getFileExtension(analysis);
    const langLabel = analysis.languageConfig.displayName || analysis.primaryLanguage;
    const buildLabel = analysis.buildSystem !== 'unknown' ? ` using ${analysis.buildSystem}` : '';

    for (const concept of concepts) {
      const pattern = this.buildSearchPattern(concept, analysis);
      const extInfo = primaryExt ? ` (.${primaryExt})` : '';
      hints.push({
        pattern,
        context: `Finding ${concept.concept}-related code in ${langLabel}${extInfo}${buildLabel}`,
      });
    }

    return hints;
  }

  /**
   * Infer directory purpose from a path
   */
  private inferDirectoryPurpose(dirPath: string, analysis: ProjectAnalysis): string {
    const dirConfig = analysis.languageConfig.directories.find(d => d.path === dirPath);
    if (dirConfig) {
      const purposeMap: Record<string, string> = {
        source: 'Source code',
        test: 'Tests',
        config: 'Configuration',
        build: 'Build output',
        docs: 'Documentation',
        assets: 'Assets',
        platform: 'Platform-specific code',
      };
      return purposeMap[dirConfig.purpose] || dirConfig.purpose;
    }
    return 'Unknown';
  }

  /**
   * Get primary file extension for the language
   */
  private getFileExtension(analysis: ProjectAnalysis): string {
    const extensions = this.analyzer.getFileExtensions(analysis);
    return extensions[0]?.replace('.', '') || 'txt';
  }

  /**
   * Format discovery context for inclusion in a planning prompt
   */
  formatForPlanning(context: DiscoveryContext): string {
    const lines: string[] = [];

    lines.push('## Project Analysis');
    lines.push('');
    lines.push(`**Language**: ${context.projectAnalysis.languageConfig.displayName || context.projectAnalysis.primaryLanguage}`);
    lines.push(`**Build System**: ${context.projectAnalysis.buildSystem}`);
    lines.push(`**Confidence**: ${(context.projectAnalysis.confidence * 100).toFixed(0)}%`);

    if (context.projectAnalysis.isMonorepo) {
      lines.push(`**Structure**: Monorepo`);
    }
    lines.push('');

    if (context.suggestedScopePaths.length > 0) {
      lines.push('## Suggested Scope Paths');
      lines.push('');
      for (const scopePath of context.suggestedScopePaths) {
        lines.push(`- ${scopePath}/`);
      }
      lines.push('');
    }

    if (context.suggestedScopeFiles.length > 0) {
      lines.push('## Suggested Scope Files');
      lines.push('');
      for (const file of context.suggestedScopeFiles.slice(0, 10)) {
        lines.push(`- ${file}`);
      }
      lines.push('');
    }

    if (context.searchHints.length > 0) {
      lines.push('## Search Hints');
      lines.push('');
      for (const hint of context.searchHints) {
        lines.push(`- Pattern: \`${hint.pattern}\` (${hint.context})`);
      }
      lines.push('');
    }

    lines.push('**Instructions**: Use the suggested scope paths and files above to populate `scopeFiles` and `scopePaths` in your plan steps. This prevents agents from exhausting their tool budget on exploration.');

    return lines.join('\n');
  }
}
