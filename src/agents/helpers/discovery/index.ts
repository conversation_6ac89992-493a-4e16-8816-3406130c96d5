/**
 * Project-Agnostic Discovery System
 * 
 * Provides intelligent file and directory discovery for any programming language.
 * 
 * Usage:
 * ```typescript
 * import { discoverContext } from '@agents/helpers/discovery';
 * 
 * const context = await discoverContext('Add authentication to the API');
 * console.log(context.suggestedScopeFiles); // Files to include in plan
 * console.log(context.suggestedScopePaths); // Directories to explore
 * ```
 */

export * from './types';
export * from './language-configs';
export * from './ProjectAnalyzer';
export * from './DiscoveryEngine';

import { DiscoveryEngine } from './DiscoveryEngine';
import type { DiscoveryContext, DiscoveryOptions } from './types';

// Singleton instance for reuse across planning calls
let engineInstance: DiscoveryEngine | null = null;

/**
 * Discover relevant files and directories for a task query
 * 
 * This is the main entry point for the discovery system.
 * It analyzes the project once and caches the analysis for subsequent calls.
 * 
 * @param query - The task description (e.g., "Add authentication to the API")
 * @param options - Discovery options (max files, include tests, etc.)
 * @returns Discovery context with suggested scope files and paths
 * 
 * @example
 * ```typescript
 * const context = await discoverContext('Refactor the database layer');
 * 
 * // Use in planning
 * const scopeFiles = context.suggestedScopeFiles;
 * const scopePaths = context.suggestedScopePaths;
 * ```
 */
export async function discoverContext(
  query: string,
  options: DiscoveryOptions = {}
): Promise<DiscoveryContext> {
  // Create or reuse engine instance
  if (!engineInstance || options.projectRoot) {
    engineInstance = new DiscoveryEngine(options.projectRoot);
  }

  return await engineInstance.discover(query, options);
}

/**
 * Format discovery context for inclusion in planning prompts
 * 
 * @param context - Discovery context from discoverContext()
 * @returns Formatted markdown string for planning prompt
 * 
 * @example
 * ```typescript
 * const context = await discoverContext(query);
 * const formatted = formatDiscoveryContext(context);
 * 
 * const planningPrompt = `
 * ${formatted}
 * 
 * Create a plan for: ${query}
 * `;
 * ```
 */
export function formatDiscoveryContext(context: DiscoveryContext): string {
  if (!engineInstance) {
    engineInstance = new DiscoveryEngine();
  }
  return engineInstance.formatForPlanning(context);
}

/**
 * Reset the discovery engine (useful for testing or switching projects)
 */
export function resetDiscoveryEngine(): void {
  engineInstance = null;
}

