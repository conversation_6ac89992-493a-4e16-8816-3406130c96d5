export const normalizeAgentChoice = (
  step: {
    title?: string;
    description?: string;
    input?: string;
    instructions?: string[];
    acceptanceCriteria?: string[];
    expectedOutputs?: string[];
    handoffNotes?: string;
  },
  mapped: string,
): string => {
  const textParts: string[] = [step.title || '', step.description || '', step.input || ''];
  if (Array.isArray(step.instructions)) textParts.push(step.instructions.join(' '));
  if (Array.isArray(step.acceptanceCriteria)) textParts.push(step.acceptanceCriteria.join(' '));
  if (Array.isArray(step.expectedOutputs)) textParts.push(step.expectedOutputs.join(' '));
  if (step.handoffNotes) textParts.push(step.handoffNotes);
  const desc = textParts.join(' ').toLowerCase();
  const analysisKeywords = ['analyze', 'investigate', 'search', 'scan', 'list', 'identify', 'which file', 'find file', 'read file', 'grep', 'explore'];
  const codeChangeKeywords = [
    'write code',
    'implement',
    'refactor',
    'apply patch',
    'update file',
    'run command',
    'execute command',
    'edit file',
    'generate code',
    'modify',
    'change',
    'update',
    'patch',
    'fix',
    'create file',
    'replace',
    'build',
    'scaffold',
    'compose pipeline',
  ];
  const computerUseKeywords = ['open', 'navigate', 'go to', 'browser', 'click', 'type', 'play', 'scroll', 'form', 'submit', 'select'];
  const backgroundKeywords = ['monitor', 'watch for', 'when available', 'auto-add', 'headless', 'in the background', 'periodically'];
  const workspaceKeywords = ['email', 'emails', 'gmail', 'inbox', 'unread', 'calendar', 'drive', 'google drive', 'gdrive'];
  const websiteSearchHints = ['on youtube', 'on youtube music', 'on music.youtube.com', 'in the browser', 'search bar', 'site search'];
  const fileAnalysisHints = ['run_file_analysis', 'file analysis', 'file risk', 'post-change analysis', 'modified files overview'];
  if (fileAnalysisHints.some(k => desc.includes(k))) return 'FileAnalysisAgent';
  if (analysisKeywords.some(k => desc.includes(k)) && websiteSearchHints.some(k => desc.includes(k))) return 'ComputerUseAgent';
  if (workspaceKeywords.some(k => desc.includes(k))) return 'GoogleWorkspaceAgent';
  if (analysisKeywords.some(k => desc.includes(k))) return 'ResearchAgent';
  if (codeChangeKeywords.some(k => desc.includes(k))) return 'CodeGenerationAgent';
  if (computerUseKeywords.some(k => desc.includes(k))) return 'ComputerUseAgent';
  if (backgroundKeywords.some(k => desc.includes(k))) return 'IsolatedComputerUseAgent';
  return mapped;
};
