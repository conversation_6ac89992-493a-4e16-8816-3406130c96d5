import { MODEL_MAP } from "@/types/orchestrator-types";
import { DanteAIOrchestrator } from "../DanteOrchestrator";
import { anthropic } from "@ai-sdk/anthropic";
import { google } from "@ai-sdk/google";
import { ollama } from "ai-sdk-ollama";
import { openai } from "@ai-sdk/openai";
import { config } from "@/utils/config";

const PROVIDERS = { openai, google, anthropic, ollama } as const;

export const getModelProvider = (modelName?: string, currentModel?: string) => {
  const modelInput = DanteAIOrchestrator.normalizeModelId(modelName || currentModel);
  // Support inline provider prefix for offline usage, e.g. 'ollama:gemma3:12b'
  if (modelInput.startsWith('ollama:')) {
    const m = modelInput.slice('ollama:'.length);
    const prov = (PROVIDERS as any)['ollama'];
    if (!prov) throw new Error(`Provider ollama not found`);
    return prov(m);
  }
  const mapping = (MODEL_MAP as any)[modelInput] || (MODEL_MAP as any)['gpt-5'];
  const provider = (PROVIDERS as any)[mapping.provider];
  if (!provider) throw new Error(`Provider ${mapping.provider} not found`);
  return provider(mapping.model);
};

  // Provider/model availability gate. Skips models whose provider is not configured.

export const isModelAvailable = (modelKey: string): boolean => {
    try {
      const normalized = DanteAIOrchestrator.normalizeModelId(modelKey);
      // Inline Ollama prefix support
      if (normalized.startsWith('ollama:')) {
        return (String(config.orchestrator?.offlineProvider || '').toLowerCase() === 'ollama') && !!config.ollama?.baseURL;
      }
      const mapping = (MODEL_MAP as any)[normalized];
      if (!mapping) return false;
      const provider = mapping.provider as keyof typeof PROVIDERS;
      if (provider === 'openai') {
        return !!(config.openai?.apiKey || process.env.OPENAI_API_KEY);
      }
      if (provider === 'google') {
        // Support both env/config variants
        return !!(config.gemini?.apiKey || process.env.GEMINI_API_KEY || process.env.GOOGLE_GENERATIVE_AI_API_KEY);
      }
      if (provider === 'anthropic') {
        return !!(process.env.ANTHROPIC_API_KEY || (config as any)?.anthropic?.apiKey);
      }
      if (provider === 'ollama') {
        return !!config.ollama?.baseURL;
      }
      return false;
    } catch {
      return false;
    }
  };
