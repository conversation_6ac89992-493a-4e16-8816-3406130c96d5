import { structuredFieldForAgent, normalizeCodegenPayload, renderCodegenSummary } from './code-gen-payloads';

export const coerceAnswerPayload = (raw: any): { original: any; parsed?: any } => {
  if (raw == null) return { original: raw };
  if (typeof raw === 'string') {
    const trimmed = raw.trim();
    if (!trimmed) return { original: raw };
    try {
      const parsed = JSON.parse(trimmed);
      return { original: raw, parsed };
    } catch {
      return { original: raw };
    }
  }
  if (typeof raw === 'object') return { original: raw, parsed: raw };
  return { original: raw };
}

export const extractAnswerPayload = (res: any): { original: any; parsed?: any; call?: any } | undefined => {
    if (!res || typeof res !== 'object') return undefined;

    try {
      const toolCalls = Array.isArray((res as any).toolCalls) ? (res as any).toolCalls : [];
      for (let i = toolCalls.length - 1; i >= 0; i -= 1) {
        const call = toolCalls[i];
        const name = call?.toolName || call?.name;
        if (typeof name === 'string' && name.toLowerCase() === 'answer') {
          const raw = call?.args ?? call?.arguments ?? call?.input;
          const payload = coerceAnswerPayload(raw);
          if (payload.parsed && typeof raw === 'string') {
            try { call.args = payload.parsed; } catch {}
          }
          return { ...payload, call };
        }
      }

      const steps = Array.isArray((res as any).steps) ? (res as any).steps : [];
      for (let i = steps.length - 1; i >= 0; i -= 1) {
        const step = steps[i];
        if (!step || typeof step !== 'object') continue;
        const stepName = step?.toolName || step?.name;
        if (typeof stepName !== 'string' || stepName.toLowerCase() !== 'answer') continue;
        const stepType = String(step?.type ?? '').toLowerCase();
        if (stepType && stepType !== 'tool-call' && stepType !== 'tool_call' && stepType !== 'toolcall') continue;
        const raw = step?.args ?? step?.arguments ?? step?.input ?? step?.result ?? step?.output;
        const payload = coerceAnswerPayload(raw);
        const argsValue = payload.parsed ?? payload.original;
        if (argsValue === undefined) continue;
        const call: any = {
          toolName: 'answer',
          name: 'answer',
          args: argsValue,
          input: argsValue,
        };
        const callId = step?.toolCallId ?? step?.tool_call_id ?? step?.callId ?? step?.call_id ?? step?.id;
        if (callId != null) {
          const normalizedId = String(callId);
          call.toolCallId = normalizedId;
          call.id = normalizedId;
        }
        return { ...payload, call };
      }
    } catch (err) {
      try { console.warn('Failed to extract answer payload from result:', err); } catch {}
    }

    return undefined;
  }

export const ensureAnswerResult = (res: any, agentName: string): void => {
    if (!res || typeof res !== 'object') return;

    const payload = extractAnswerPayload(res);
    if (!payload) return;

    const { call, parsed, original } = payload;
    if (call) {
      const existing = Array.isArray((res as any).toolCalls) ? (res as any).toolCalls : [];
      const hasAnswer = existing.some((c: any) => {
        const name = c?.toolName || c?.name;
        return typeof name === 'string' && name.toLowerCase() === 'answer';
      });
      if (!hasAnswer) {
        try { (res as any).toolCalls = [...existing, call]; } catch {}
      } else if (!Array.isArray((res as any).toolCalls)) {
        try { (res as any).toolCalls = existing; } catch {}
      }
    }

    const structured = parsed && typeof parsed === 'object' ? parsed : undefined;
    const field = structuredFieldForAgent(agentName);
    if (structured) {
      if (field) {
        if (!(res as any)[field]) {
          try { (res as any)[field] = structured; } catch {}
        }
      } else if (!(res as any).structuredAnswer) {
        try { (res as any).structuredAnswer = structured; } catch {}
      }
    }

    const currentText = typeof (res as any).text === 'string' ? (res as any).text.trim() : '';
    if (!currentText) {
      if (structured && typeof structured.summary === 'string' && structured.summary.trim().length > 0) {
        try { (res as any).text = structured.summary.trim(); } catch {}
      } else if (typeof original === 'string' && original.trim().length > 0) {
        try { (res as any).text = original.trim(); } catch {}
      }
    }
  }

export const extractCodegenAnswer = (res: any): { structured?: any; rendered?: string } => {
      try {
        const calls = Array.isArray(res?.toolCalls) ? res.toolCalls : [];
        const steps = Array.isArray(res?.steps) ? res.steps : [];
        const answerCall = calls.find((c: any) => (c?.toolName || c?.name) === 'answer');
        const raw = answerCall?.args ?? answerCall?.arguments ?? answerCall?.input;
        let structured = normalizeCodegenPayload(raw);
        if (!structured) {
          for (let i = steps.length - 1; i >= 0; i -= 1) {
            const step = steps[i];
            const stepName = step?.toolName || step?.name;
            const stepType = step?.type;
            if (stepName === 'answer' && (stepType === 'tool-call' || stepType == null)) {
              const candidate = step?.args ?? step?.arguments ?? step?.input;
              structured = normalizeCodegenPayload(candidate);
              if (structured) break;
            }
          }
        }
        const rendered = renderCodegenSummary(structured);
        return {
          structured,
          rendered: rendered && rendered.trim().length > 0 ? rendered : undefined,
        };
      } catch {
        return { structured: undefined, rendered: undefined };
      }
    }
