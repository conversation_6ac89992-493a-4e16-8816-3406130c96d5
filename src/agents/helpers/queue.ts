// Generic concurrency queue helper extracted from DanteOrchestrator
// Schedules processors by priority and respects a dynamic concurrency limit

export type QueuedTask<T = any> = {
  id: string;
  priority: number; // higher runs first
  processor: () => Promise<T>;
};

export class ConcurrencyQueue {
  private requestQueue: QueuedTask[] = [];
  private isProcessing = false;

  constructor(private opts: {
    getActiveCount: () => number;            // e.g., orchestrator.activeRequests.size
    getMaxConcurrent: () => number;          // e.g., orchestrator.maxConcurrentRequests
    generateId: () => string;                // e.g., orchestrator.generateRequestId()
    onError?: (err: unknown) => void;        // optional error sink
  }) {}

  getQueueLength(): number {
    return this.requestQueue.length;
  }

  // Manually trigger a processing pass (useful when external capacity frees up)
  drain(): Promise<void> {
    return this.process();
  }

  private async process(): Promise<void> {
    if (this.isProcessing) return;
    this.isProcessing = true;
    try {
      // Sort: high priority first
      this.requestQueue.sort((a, b) => (b.priority || 1) - (a.priority || 1));
      const availableSlots = Math.max(0, this.opts.getMaxConcurrent() - this.opts.getActiveCount());
      if (availableSlots > 0 && this.requestQueue.length > 0) {
        const toProcess = this.requestQueue.splice(0, availableSlots);
        for (const next of toProcess) {
          Promise.resolve(next.processor())
            .catch((err) => {
              try { this.opts.onError?.(err); } catch { /* ignore */ }
            })
            .finally(() => {
              // When a queued task completes, try to drain the queue
              this.process().catch(() => {/* ignore */});
            });
        }
      }
    } finally {
      this.isProcessing = false;
    }
  }

  enqueue<T>(priority: number, processor: () => Promise<T>): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const id = this.opts.generateId();
      this.requestQueue.push({ id, priority: priority || 1, processor: async () => {
        try { const v = await processor(); resolve(v); } catch (e) { reject(e as any); }
      }});
      this.process().catch(() => {/* ignore */});
    });
  }
}
