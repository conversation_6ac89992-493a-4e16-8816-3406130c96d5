import { consolidateToolResult } from '@/utils/contextConsolidator';
import errorRecoveryMiddleware, { RecoveryContext } from '@/utils/errorRecoveryMiddleware';
import { DEFAULT_RETRY_CONFIG, analyzeToolError, formatToolErrorForUser } from '@/utils/toolErrorHandler';

const validateToolResult = (result: any, _toolName: string): any => {
  if (typeof result === 'string' && result.includes('An error occurred while running the tool')) {
    const errorMatch = result.match(/Error: Error: (.+)/);
    const errorMessage = (errorMatch && errorMatch[1]) ? errorMatch[1] : result;
    throw new Error(errorMessage);
  }
  if (typeof result === 'object' && result !== null) {
    if ((result as any).error || ((result as any).success === false && (result as any).message)) {
      const errorMessage = (result as any).error || (result as any).message || 'Tool execution failed';
      throw new Error(errorMessage);
    }
  }
  return result;
}
