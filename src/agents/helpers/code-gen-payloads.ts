const STRUCTURED_ANSWER_FIELD_BY_AGENT: Record<string, string> = {
  ResearchAgent: 'structuredResearch',
  DebugAgent: 'structuredDebug',
  GoogleWorkspaceAgent: 'structuredWorkspace',
  CodeGenerationAgent: 'structuredCodegen',
};

export const structuredFieldForAgent = (agentName: string): string | undefined => {
  if (!agentName) return undefined;
  if (Object.prototype.hasOwnProperty.call(STRUCTURED_ANSWER_FIELD_BY_AGENT, agentName)) {
    return STRUCTURED_ANSWER_FIELD_BY_AGENT[agentName as keyof typeof STRUCTURED_ANSWER_FIELD_BY_AGENT];
  }
  return undefined;
};

export const normalizeCodegenPayload = (raw: any): any => {
  if (raw == null) return undefined;
  if (typeof raw === 'string') {
    const trimmed = raw.trim();
    if (!trimmed) return undefined;
    try {
      const parsed = JSON.parse(trimmed);
      return normalizeCodegenPayload(parsed);
    } catch {
      return { summary: trimmed };
    }
  }
  if (typeof raw === 'object') return raw;
  return { summary: String(raw) };
};

export const renderCodegenSummary = (structured: any): string => {
  if (!structured) return '';
  if (typeof structured === 'string') return structured;
  if (typeof structured !== 'object') return String(structured ?? '');
  const out: string[] = [];
  if (structured.summary) out.push(String(structured.summary));
  if (Array.isArray(structured.filesModified) && structured.filesModified.length) {
    out.push('Files Modified:');
    for (const f of structured.filesModified) {
      if (!f) continue;
      const path = typeof f.path === 'string' ? f.path : '[unknown path]';
      const changeType = typeof f.changeType === 'string' ? f.changeType : 'modify';
      out.push(`- ${path} [${changeType}]`);
    }
  }
  if (Array.isArray(structured.notes) && structured.notes.length) {
    out.push('Notes:');
    for (const note of structured.notes) out.push(`- ${note}`);
  }
  return out.join('\n');
};
