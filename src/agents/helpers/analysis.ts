
  const looksLikePrepOnlyIntent = (text: string): boolean => {
      try {
        const lower = String(text || '').toLowerCase();
        if (!lower || lower.trim().length === 0) return false;
        const infoPhrases = ['retrieve', 'gather', 'collect', 'find', 'identify', 'inspect', 'review', 'understand', 'analyze', 'investigate', 'research', 'list', 'catalog', 'inventory', 'map', 'explore', 'scan', 'locate', 'determine', 'document', 'summarize', 'read'];
        const stageHints = ['step 1', 'step one', 'phase 1', 'analysis step', 'discovery step', 'initial research', 'pre-work', 'context gathering', 'analysis phase', 'prep step', 'discovery task'];
        const actionPhrases = ['apply', 'write', 'update', 'fix', 'modify', 'patch', 'change', 'implement', 'refactor', 'create', 'delete', 'replace', 'rename', 'convert', 'adjust', 'introduce'];
        const infoCue = infoPhrases.some((p) => lower.includes(p));
        const stageCue = stageHints.some((p) => lower.includes(p));
        const actionCue = actionPhrases.some((p) => lower.includes(p));
        if (lower.includes('do not modify') || lower.includes('do not edit') || lower.includes('read-only') || lower.includes('analysis only') || lower.includes('discovery only')) {
          return true;
        }
        if ((infoCue && stageCue) || (infoCue && !actionCue)) {
          return true;
        }
        return false;
      } catch {
        return false;
      }
    }


export const shouldSkipWriteGuardForAnalysis = (args: {
    input: string;
    routeText?: string;
    summary: string;
    finalText: string;
    declaredPaths: string[];
    hadFileEditAttempt: boolean;
    readFileCount: number;
    uniqueReadPaths: number;
    readFileMaxRepeat: number;
    listDirCount: number;
    totalToolCalls: number;
  }): boolean =>{
    /**
     * Purpose:
     * Decide whether to skip the NO_WRITE_CONFIRMED guard for CodeGenerationAgent.
     * We skip only when the run appears to be analysis/planning-only (no declared files, no edit attempts)
     * and the output language explicitly indicates "no changes", or clearly reflects analysis/planning intent.
     *
     * Inputs considered:
     * - args.declaredPaths: assistant-declared modified files (from 'answer' tool). If present, do NOT skip.
     * - args.hadFileEditAttempt: whether a file_edit tool was attempted. If yes, do NOT skip.
     * - args.summary/finalText: assistant's final content, scanned for explicit "no change" and analysis/planning cues.
     * - args.input/routeText: original intent text; used as a final fallback to detect prep-only requests.
     * - args.readFileCount/uniqueReadPaths/readFileMaxRepeat: volume + repetition of read_file usage (detects read loops).
     * - args.listDirCount/totalToolCalls: ensures guard is only skipped for light, read-focused analysis sessions.
     *
     * Return:
     * - true  => treat as analysis-only; skip NO_WRITE_CONFIRMED enforcement
     * - false => require confirmed write (or verification-based success) for apply/edit-type requests
     */
    try {
      // Short-circuit: if assistant declared any changed paths or actually attempted edits, enforce the guard.
      if (args.declaredPaths.length > 0) return false;
      if (args.hadFileEditAttempt) return false;

      // Relaxed read loop detection - increased thresholds to reduce false positives
      const readLoopSuspected = (() => {
        const heavyLoop = args.readFileCount >= 20 && (args.uniqueReadPaths <= 8 || args.readFileMaxRepeat >= 5);
        const dominantReads = args.totalToolCalls >= 15 && args.readFileCount / Math.max(1, args.totalToolCalls) >= 0.8;
        const directorySpinner = args.listDirCount >= 10 && (args.totalToolCalls - args.listDirCount) <= 3;
        return heavyLoop || dominantReads || directorySpinner;
      })();
      if (readLoopSuspected) return false;

      // Normalize the assistant's output we will scan for cues.
      const output = `${args.summary || ''}\n${args.finalText || ''}`.toLowerCase();
      const matchesAny = (patterns: RegExp[]) => patterns.some((re) => re.test(output));

      // Bucket 1 — Explicit "no-change" signals: direct statements that no edits/changes were made.
      const explicitNoChangePatterns: RegExp[] = [
        /\bno (?:code|file|files) changes?\b/i,
        /\bno edits?\b/i,
        /\bno modifications?\b/i,
        /\bdid(?:\s+not|n't) modify\b/i,
        /\bdid(?:\s+not|n't) change\b/i,
        /\bnothing changed\b/i,
        /\banalysis only\b/i,
        /\bread[-\s]?only\b/i,
        /\bjust reviewed\b/i,
        /\bonly reviewed\b/i,
        /\bcontext (?:gather|collection)\b/i,
        /\bdiscovery findings?\b/i,
        /\binvestigation summary\b/i,
        /\bno action needed\b/i,
        /\bno changes required\b/i,
        /\bexamination complete\b/i,
        /\bassessment complete\b/i,
        /\bmonitoring\b/i,
        /\bobservation\b/i,
      ];
      // If any explicit no-change phrase appears, skip enforcement.
      if (matchesAny(explicitNoChangePatterns)) return true;

      // Bucket 2 — Analysis stems: morphological roots to catch variants (e.g., analyze/analyzing/analysis).
      const analysisRoots = [
        'investigat', 'analyz', 'analysis', 'inspect', 'explor', 'assess', 'review', 'understand',
        'gather', 'identify', 'discover', 'context', 'survey', 'audit', 'inventory', 'map',
        'document', 'observe', 'scan', 'examin', 'catalog', 'outline', 'check', 'verify',
        'validate', 'test', 'debug', 'trace', 'monitor', 'log', 'search', 'find', 'locate'
      ];
      const analysisPatterns = analysisRoots.map((root) => new RegExp(`\\b${root}\\w*\\b`, 'i'));

      // Bucket 3 — Planning terms: forward-looking language that implies next steps rather than changes made now.
      const planningPatterns: RegExp[] = [
        /\bnext steps?\b/i,
        /\bup next\b/i,
        /\bfollow[-\s]?up\b/i,
        /\bplan\b/i,
        /\bplanned\b/i,
        /\broadmap\b/i,
        /\bto[-\s]?do\b/i,
        /\bpending\b/i,
        /\bprepar(?:e|ing)\b/i,
        /\bprep\b/i,
        /\bbefore (?:writing|editing|making changes)\b/i,
        /\binitial review\b/i,
        /\bdiscovery phase\b/i,
        /\banalysis phase\b/i,
        /\bcontext (?:gather|collection)\b/i,
        /\bstep\s*1\b/i,
        /\bphase\s*1\b/i,
        /\brecommendations?\b/i,
        /\bsuggestions?\b/i,
        /\bconsiderations?\b/i,
        /\boptions?\b/i,
        /\balternatives?\b/i,
      ];

      // Bucket 4 — Past-change terms: strong evidence the agent actually changed or produced modifications.
      // If any of these are present, we DO NOT skip enforcement.
      const pastChangePatterns: RegExp[] = [
        /\bupdated\b/i,
        /\bmodified\b/i,
        /\bimplemented\b/i,
        /\bapplied\b/i,
        /\badded\b/i,
        /\bcreated\b/i,
        /\brefactored\b/i,
        /\bfixed\b/i,
        /\bdeleted\b/i,
        /\brenamed\b/i,
        /\bpatched\b/i,
        /\breplaced\b/i,
        /\bwrote\b/i,
        /\bconverted\b/i,
        /\badjusted\b/i,
        /\bcommitted\b/i,
        /\bchanged\b/i,
        /\bintroduced\b/i,
        /\bedited\b/i,
        /\binserted\b/i,
        /\bremoved\b/i,
      ];

      // Heuristic decision:
      // If we detect analysis/planning cues and do NOT detect past-change cues, treat as analysis-only.
      const analysisCue = matchesAny(analysisPatterns);
      const planCue = matchesAny(planningPatterns);
      const changeCue = matchesAny(pastChangePatterns);
      if ((analysisCue || planCue) && !changeCue) return true;

      // Intent-level fallback: even if the output is ambiguous, if the original request clearly asked
      // for discovery/prep-only work, skip enforcement.
      const combinedIntent = `${args.input || ''} ${args.routeText || ''}`.toLowerCase();
      if (looksLikePrepOnlyIntent(combinedIntent)) {
        // Relaxed thresholds for legitimate analysis tasks
        if (args.totalToolCalls <= 15 && args.readFileCount <= 15) {
          return true;
        }
      }

      // Default: require write confirmation.
      return false;
    } catch (err) {
      // Defensive: on any evaluation error, log and fall back to requiring confirmation.
      try { console.warn('Failed to evaluate analysis-only guard skip:', err); } catch {}
      return false;
    }
  }


// Infer the most likely affected file path from recent tool usage and write outcomes
export const inferLastAffectedPath = (
  recentToolArgs: Array<{ name: string; args: any; t: number }>,
  recentWrites: Array<{ name: string; ok: boolean; path?: string; t: number }>
): string | undefined => {
  try {
    // Prefer most recent write/edit/patch that references a path
    const lastWrite = recentWrites
      .slice()
      .reverse()
      .find((w) => ['file_edit'].includes(w.name));
    if (lastWrite?.path && typeof lastWrite.path === 'string' && lastWrite.path.length > 0) {
      return lastWrite.path;
    }
    // Fallback: infer from recent tool arguments likely to contain a path
    const candNames = ['diagnose_file_syntax', 'read_file', 'file_edit'];
    for (const e of recentToolArgs.slice().reverse()) {
      if (!candNames.includes(e.name)) continue;
      try {
        const argObj = typeof e.args === 'string' ? JSON.parse(e.args) : (e.args || {});
        const p = (argObj as any)?.filePath || (argObj as any)?.path;
        if (typeof p === 'string' && p.length > 0) return p;
      } catch {}
    }
  } catch (e) {
    try { console.warn('Failed to infer path from tool args/writes:', e); } catch {}
  }
  return undefined;
};
