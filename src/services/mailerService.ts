import sgMail, { Mail<PERSON>ataRequired } from "@sendgrid/mail";

/**
 * SendGrid Mailer Service
 * - Initializes SendGrid from environment
 * - Sends emails with an unsubscribe footer and deliverability headers
 *
 * Required env vars:
 *  - SENDGRID_API_KEY: string
 *  - SENDGRID_FROM_EMAIL: default From address (can be overridden per call)
 *  - SENDGRID_FROM_NAME: optional default From name
 *  - UNSUBSCRIBE_BASE_URL: base URL for building unsubscribe links when only a token is provided
 *    Example: https://yourapp.com/unsubscribe
 */

type Address = string | { email: string; name?: string };

export type UnsubscribeOptions = {
  /** Full unsubscribe URL. If provided, it takes precedence. */
  url?: string;
  /** Token to append to the base URL as ?token=<token> */
  token?: string;
  /** Optional: base URL to use instead of UNSUBSCRIBE_BASE_URL env var */
  baseUrl?: string;
  /** Optional: mailto for List-Unsubscribe header (e.g., <EMAIL>) */
  mailto?: string;
};

export type SendEmailOptions = {
  to: Address | Address[];
  subject: string;
  html?: string;
  text?: string;
  fromEmail?: string;
  fromName?: string;
  headers?: Record<string, string>;
  categories?: string[];
  customArgs?: Record<string, string | number>;
  /** If your SendGrid account uses suppression groups */
  suppressionGroupId?: number;
  /** Tracking settings */
  clickTracking?: boolean; // default: false
  openTracking?: boolean;  // default: true
  sandboxMode?: boolean;   // default: false
  /** Optional message identifier to echo in headers */
  messageId?: string;
  /** High priority hints some providers respect */
  priority?: "high" | "normal";
  /** Unsubscribe configuration (mandatory for compliance) */
  unsubscribe: UnsubscribeOptions;
};

let initialized = false;

function ensureInitialized() {
  if (initialized) return;
  const apiKey = process.env.SENDGRID_API_KEY;
  if (!apiKey) {
    throw new Error("SENDGRID_API_KEY is not set in environment.");
  }
  sgMail.setApiKey(apiKey);
  initialized = true;
}

function resolveFrom(opts?: { fromEmail?: string; fromName?: string }): { email: string; name?: string } {
  const email = opts?.fromEmail || process.env.SENDGRID_FROM_EMAIL;
  const name = opts?.fromName || process.env.SENDGRID_FROM_NAME || undefined;
  if (!email) {
    throw new Error("SENDGRID_FROM_EMAIL is required (env var or per-call override).");
  }
  return { email, name };
}

function buildUnsubscribeUrl(unsub: UnsubscribeOptions): string {
  if (unsub.url) return unsub.url;
  const base = unsub.baseUrl || process.env.UNSUBSCRIBE_BASE_URL;
  if (!base) {
    throw new Error("UNSUBSCRIBE_BASE_URL missing and no unsubscribe.url provided.");
  }
  if (!unsub.token) {
    throw new Error("unsubscribe.token is required when unsubscribe.url is not provided.");
  }
  const sep = base.includes("?") ? "&" : "?";
  return `${base}${sep}token=${encodeURIComponent(unsub.token)}`;
}

function stripHtml(input: string): string {
  // Simple fallback for generating text version
  return input
    .replace(/<style[\s\S]*?<\/style>/gi, " ")
    .replace(/<script[\s\S]*?<\/script>/gi, " ")
    .replace(/<[^>]+>/g, " ")
    .replace(/\s+/g, " ")
    .trim();
}

function escapeHtml(str: string): string {
  return str
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#39;");
}

function withUnsubscribeFooter(htmlBody: string, unsubscribeUrl: string): string {
  const footer = `
    <hr style="margin:24px 0;border:none;border-top:1px solid #e5e7eb"/>
    <p style="font-size:12px;color:#6b7280;line-height:1.5;margin:0">
      You are receiving this email because you subscribed to updates. If you no longer wish to receive these emails,
      you can <a href="${unsubscribeUrl}" target="_blank" rel="noopener noreferrer">unsubscribe here</a>.
    </p>
  `;

  // Avoid duplicating footer if already present
  if (htmlBody.includes("unsubscribe here")) return htmlBody;
  return `${htmlBody}\n${footer}`;
}

function buildHeaders(unsubscribeUrl: string, opts: Pick<SendEmailOptions, "headers" | "messageId" | "priority"> & { mailto?: string }): Record<string, string> {
  const base: Record<string, string> = {
    ...(opts.headers || {}),
    // RFC 2369 headers for one-click unsubscribe in clients like Gmail/Outlook
    "List-Unsubscribe": opts.mailto
      ? `<mailto:${opts.mailto}>, <${unsubscribeUrl}>`
      : `<${unsubscribeUrl}>`,
    "List-Unsubscribe-Post": "List-Unsubscribe=One-Click",
  };
  if (opts.messageId) base["X-Entity-Ref-ID"] = String(opts.messageId);
  if (opts.priority === "high") base["X-Priority"] = "1 (Highest)";
  base["X-Transactional-Email"] = "true";
  return base;
}

export async function sendEmail(options: SendEmailOptions): Promise<{ messageId?: string; responseCode: number }> {
  ensureInitialized();

  const { to, subject, html, text, categories, customArgs, suppressionGroupId, clickTracking, openTracking, sandboxMode, messageId, priority } = options;

  const from = resolveFrom({ fromEmail: options.fromEmail, fromName: options.fromName });

  const unsubscribeUrl = buildUnsubscribeUrl(options.unsubscribe);
  const builtHtml = withUnsubscribeFooter(html || (text ? `<pre>${escapeHtml(text)}</pre>` : ""), unsubscribeUrl);
  const builtText = text || (html ? stripHtml(html) : stripHtml(builtHtml));

  const headers = buildHeaders(unsubscribeUrl, {
    headers: options.headers,
    messageId,
    priority,
    mailto: options.unsubscribe.mailto,
  });

  const msg: MailDataRequired = {
    to: to as any,
    from,
    subject,
    html: builtHtml,
    text: builtText,
    headers,
    categories,
    customArgs: customArgs as any,
    trackingSettings: {
      clickTracking: { enable: Boolean(clickTracking ?? false) },
      openTracking: { enable: Boolean(openTracking ?? true) },
    },
    mailSettings: sandboxMode ? { sandboxMode: { enable: true } } : undefined,
    asm: suppressionGroupId ? { groupId: suppressionGroupId } : undefined,
  } as MailDataRequired;

  const [res] = await sgMail.send(msg);
  const xMessageId = res.headers?.get ? (res.headers.get("x-message-id") || undefined) : (res.headers as any)?.["x-message-id"];
  return { messageId: xMessageId, responseCode: res.statusCode };
}

export default { sendEmail };
