import { describe, it, expect, mock, beforeEach, afterAll } from 'bun:test';
import { runWithContext } from '../../utils/requestContext';

type PlanArgs = { input: string; options: any };

const orchestratorStub = { id: 'orchestrator-test-stub' } as const;

const planAndDelegateMock = mock(async function (this: any, input: string, options: any) {
  throw new Error(`planAndDelegate mock not configured for input: ${input} with options: ${JSON.stringify(options)}`);
});

const executeWithAgentMock = mock(async () => ({
  structuredResearch: { summary: 'stub summary', keyFindings: ['item'] },
  text: 'stub text',
}));

mock.module('../../agents/DanteOrchestrator', () => ({
  orchestrator: orchestratorStub,
}));

mock.module('../../agents/helpers/planning', () => ({
  planAndDelegate: planAndDelegateMock,
}));

mock.module('../../agents/agent-actions', () => ({
  executeWithAgent: executeWithAgentMock,
}));

mock.module('../../utils/cancel', () => ({
  checkCancellation: () => undefined,
}));

const builtInsPromise = import('../../agents/BuiltInAgents');
const autoFollowUpPromise = import('../../agents/AutoFollowUpManager');

describe('TaskOrchestrator delegate_to_agents binding', () => {
  beforeEach(() => {
    planAndDelegateMock.mockReset();
  });

  it('binds planAndDelegate to the orchestrator for non-stream runs', async () => {
    const { taskOrchestratorAgent } = await builtInsPromise;
    const captured: { thisVal?: any; args?: PlanArgs } = {};
    const stubResult = { type: 'delegated_orchestration', text: 'ok' };

    planAndDelegateMock.mockImplementation(async function (this: any, input: string, options: any) {
      captured.thisVal = this;
      captured.args = { input, options };
      expect(options.stream).toBe(false);
      return stubResult;
    });

    const delegateTool = (taskOrchestratorAgent.tools as any).delegate_to_agents;
    const result = await delegateTool.execute({ request: 'test request' });

    expect(result).toBe(stubResult);
    expect(planAndDelegateMock).toHaveBeenCalledTimes(1);
    expect(captured.thisVal).toBe(orchestratorStub);
    expect(captured.args?.input).toBe('test request');
  });

  it('binds planAndDelegate to the orchestrator when streaming', async () => {
    const { taskOrchestratorAgent } = await builtInsPromise;
    const captured: { thisVal?: any; options?: any } = {};

    planAndDelegateMock.mockImplementation(function (this: any, _input: string, options: any) {
      captured.thisVal = this;
      captured.options = options;
      expect(options.stream).toBe(true);
      async function* iterator() {
        yield { type: 'plan_created', data: { summary: 'stub', steps: [] } };
        yield { type: 'delegation_end', data: { stepId: 'step-1', agent: 'Agent', success: true } };
      }
      return iterator();
    });

    const delegateTool = (taskOrchestratorAgent.tools as any).delegate_to_agents;

    const result = await runWithContext(
      {
        onPlanCreated: () => undefined,
        onDelegationStart: () => undefined,
        onDelegationEnd: () => undefined,
      },
      () => delegateTool.execute({ request: 'streaming request' })
    );

    expect(planAndDelegateMock).toHaveBeenCalledTimes(1);
    expect(captured.thisVal).toBe(orchestratorStub);
    expect(captured.options?.stream).toBe(true);
    expect(result).toEqual({
      type: 'delegated_orchestration',
      plan: { summary: 'stub', steps: [] },
      stepResults: [{ stepId: 'step-1', agent: 'Agent', success: true }],
      text: 'Plan: stub',
      finalMessage: 'Plan: stub',
    });
  });
});

describe('AutoFollowUpManager delegation binding', () => {
  beforeEach(() => {
    planAndDelegateMock.mockReset();
    executeWithAgentMock.mockClear();
  });

  it('invokes planAndDelegate with orchestrator as context', async () => {
    const { executeAutoFollowUp } = await autoFollowUpPromise;
    const stubPlan = { summary: 'auto plan', steps: [] };
    const stubResult = { type: 'delegated_orchestration', plan: stubPlan, stepResults: [], text: 'done', finalMessage: 'done' };

    const captured: { thisVal?: any; calledWith?: PlanArgs } = {};
    planAndDelegateMock.mockImplementation(async function (this: any, input: string, options: any) {
      captured.thisVal = this;
      captured.calledWith = { input, options };
      expect(options.stream).toBe(false);
      return stubResult;
    });

    const result = await executeAutoFollowUp('Test request', { context: { foo: 'bar' } });

    expect(planAndDelegateMock).toHaveBeenCalledTimes(1);
    expect(captured.thisVal).toBe(orchestratorStub);
    expect(captured.calledWith?.options.context).toEqual({ foo: 'bar' });
    expect(result.plan?.summary).toBe('auto plan');
  });
});

afterAll(() => {
  mock.restore();
  mock.clearAllMocks();
});
