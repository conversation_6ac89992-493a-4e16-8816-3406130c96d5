# Session Event Buffer Fix - Test Guide

## Issue Description

**Problem:** When leaving the app idle for a while and returning to an open conversation, the last agent-sent message would be rewritten with a shorter, worse response than the original.

**Root Cause:** The `EnhancedStreamHandler` was calling `sessionEventBus.reset()` on every new request, clearing the session's event history. When the UI reconnected its event stream, it would replay from an empty or partial buffer, causing messages to be regenerated incorrectly.

## Solution

1. **Removed automatic buffer reset** in `EnhancedStreamHandler` constructor
   - The buffer already has TTL (15 min) and size limits (500 events) to prevent unbounded growth
   - Events are now only cleared when user explicitly clears chat

2. **Added `/api/session/clear` endpoint** to explicitly clear session event buffer

3. **Updated `handleClearChat`** in App.tsx to call the clear endpoint before creating a new session

## Testing Steps

### Test 1: Verify messages persist correctly after idle time

1. Start the app:
   ```bash
   bun run dev:all
   ```

2. Send a message to <PERSON> that generates a detailed response (e.g., "Explain how the agent system works in this codebase")

3. Wait for the complete response to appear

4. **Leave the app idle for 5-10 minutes** (don't refresh, just let it sit)

5. Come back and observe the last assistant message

**Expected Result:** The message should remain unchanged with the full, detailed response

**Before Fix:** The message would be rewritten with a shorter version

### Test 2: Verify event buffer is cleared when clearing chat

1. Have a conversation with several messages

2. Click the sidebar menu and select "Clear Chat"

3. Send a new message

4. Check browser DevTools Network tab for:
   - POST to `/api/session/clear` (should succeed)
   - New session ID in subsequent requests

**Expected Result:** Old session events should not affect the new session

### Test 3: Verify event stream reconnection works correctly

1. Start a conversation

2. Open browser DevTools → Network tab

3. Find the EventSource connection to `/api/session/events`

4. Send a message and wait for response

5. Refresh the page (Cmd+R / F5)

6. Observe messages load from IndexedDB

7. Check the `/api/session/events` stream reconnects

**Expected Result:** Messages should load correctly from storage and match what you saw before refresh

### Test 4: Verify TTL and size limits still work

1. Start a very long conversation (50+ messages)

2. Wait 16+ minutes

3. Refresh the page

**Expected Result:** Events older than 15 minutes should be pruned automatically (TTL), and only the last 500 events should be kept (size limit)

## Technical Details

### Files Modified

1. **src/utils/enhancedStreamHandler.ts**
   - Removed `sessionEventBus.reset(this.sessionId)` from constructor
   - Added explanatory comment about why we don't reset

2. **src/api/server.ts**
   - Added POST `/api/session/clear` endpoint

3. **src/ui/App.tsx**
   - Made `handleClearChat` async
   - Added call to `/api/session/clear` before creating new session
   - Added `setAgentEvents([])` to clear UI state

### Event Buffer Lifecycle

```
┌─────────────────────────────────────────────────────────┐
│  Active Streaming (currentAbortRef is set)              │
│  ┌──────────────────────────────────────────┐          │
│  │ User sends message                       │          │
│  │ • AbortController created                │          │
│  │ • isActiveStreaming = true               │          │
│  └──────────────────────────────────────────┘          │
│                      │                                  │
│                      ▼                                  │
│  ┌──────────────────────────────────────────┐          │
│  │ Stream events processed:                 │          │
│  │ • thinking: clear buffer                 │          │
│  │ • message: append to buffer              │          │
│  │ • complete: finalize message             │          │
│  └──────────────────────────────────────────┘          │
│                      │                                  │
│                      ▼                                  │
│  ┌──────────────────────────────────────────┐          │
│  │ Events recorded to sessionEventBus       │          │
│  │ • Max 500 events                         │          │
│  │ • 15 min TTL                             │          │
│  └──────────────────────────────────────────┘          │
│                      │                                  │
│                      ▼                                  │
│  ┌──────────────────────────────────────────┐          │
│  │ Complete message saved to IndexedDB      │          │
│  │ via sessionManager                       │          │
│  └──────────────────────────────────────────┘          │
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│  Idle/Reconnect (currentAbortRef is null)               │
│  ┌──────────────────────────────────────────┐          │
│  │ Session event stream reconnects          │          │
│  │ • isActiveStreaming = false              │          │
│  └──────────────────────────────────────────┘          │
│                      │                                  │
│                      ▼                                  │
│  ┌──────────────────────────────────────────┐          │
│  │ Messages loaded from IndexedDB           │          │
│  │ • Full, complete message content         │          │
│  │ • NOT reconstructed from events          │          │
│  └──────────────────────────────────────────┘          │
│                      │                                  │
│                      ▼                                  │
│  ┌──────────────────────────────────────────┐          │
│  │ Replayed events processed:               │          │
│  │ ✅ Update agent events panel             │          │
│  │ ✅ Update orchestration stores           │          │
│  │ ❌ DO NOT reconstruct messages           │          │
│  └──────────────────────────────────────────┘          │
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│  User Clears Chat                                       │
│  ┌──────────────────────────────────────────┐          │
│  │ handleClearChat() called                 │          │
│  └──────────────────────────────────────────┘          │
│                      │                                  │
│                      ▼                                  │
│  ┌──────────────────────────────────────────┐          │
│  │ POST /api/session/clear                  │          │
│  │ ✅ sessionEventBus.reset(sessionId)      │          │
│  └──────────────────────────────────────────┘          │
│                      │                                  │
│                      ▼                                  │
│  ┌──────────────────────────────────────────┐          │
│  │ New session created                      │          │
│  │ • Fresh session ID                       │          │
│  │ • Empty event buffer                     │          │
│  └──────────────────────────────────────────┘          │
└─────────────────────────────────────────────────────────┘
```

## Verification Checklist

- [ ] Messages remain unchanged after idle time
- [ ] Clear chat properly resets event buffer
- [ ] Page refresh loads correct messages from storage
- [ ] Long conversations respect 500 event limit
- [ ] Events older than 15 minutes are pruned
- [ ] No console errors related to session events
- [ ] SSE connections reconnect properly after network issues

## Rollback Plan

If issues arise, you can revert by:

1. Restore the `sessionEventBus.reset()` call in `EnhancedStreamHandler` constructor
2. Remove the `/api/session/clear` endpoint
3. Revert `handleClearChat` changes

However, this will bring back the original issue of message rewrites after idle time.
