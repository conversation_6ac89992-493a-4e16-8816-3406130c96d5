import { describe, test, expect } from 'bun:test';
import { ProjectAnalyzer } from '../agents/helpers/discovery/ProjectAnalyzer';
import { DiscoveryEngine } from '../agents/helpers/discovery/DiscoveryEngine';
import { discoverContext, formatDiscoveryContext } from '../agents/helpers/discovery';
import { TYPESCRIPT_CONFIG, DART_CONFIG, RUST_CONFIG } from '../agents/helpers/discovery/language-configs';

describe('Project-Agnostic Discovery System', () => {
  describe('Language Configs', () => {
    test('should have TypeScript config with correct patterns', () => {
      expect(TYPESCRIPT_CONFIG.language).toBe('typescript');
      expect(TYPESCRIPT_CONFIG.directories.length).toBeGreaterThan(0);
      expect(TYPESCRIPT_CONFIG.concepts.length).toBeGreaterThan(0);

      // Check for standard directories
      const srcDir = TYPESCRIPT_CONFIG.directories.find(d => d.path === 'src');
      expect(srcDir).toBeDefined();
      expect(srcDir?.purpose).toBe('source');
      expect(srcDir?.isStandard).toBe(true);
    });

    test('should have Dart/Flutter config with correct patterns', () => {
      expect(DART_CONFIG.language).toBe('dart');
      expect(DART_CONFIG.signatures[0].buildSystem).toBe('flutter');

      // Check for Flutter-specific directories
      const libDir = DART_CONFIG.directories.find(d => d.path === 'lib');
      expect(libDir).toBeDefined();
      expect(libDir?.purpose).toBe('source');

      // Check for Flutter-specific concepts
      const widgetConcept = DART_CONFIG.concepts.find(c => c.concept === 'widget');
      expect(widgetConcept).toBeDefined();
      expect(widgetConcept?.keywords).toContain('widget');
    });

    test('should have Rust config with correct patterns', () => {
      expect(RUST_CONFIG.language).toBe('rust');
      expect(RUST_CONFIG.signatures[0].buildSystem).toBe('cargo');

      // Check for Rust-specific file patterns
      expect(RUST_CONFIG.filePatterns.function.pattern).toContain('fn');
      expect(RUST_CONFIG.filePatterns.interface.pattern).toContain('trait');
    });

    test('should have common concepts across all languages', () => {
      const languages = [TYPESCRIPT_CONFIG, DART_CONFIG, RUST_CONFIG];

      for (const lang of languages) {
        const apiConcept = lang.concepts.find(c => c.concept === 'api');
        expect(apiConcept).toBeDefined();
        expect(apiConcept?.keywords).toContain('api');

        const authConcept = lang.concepts.find(c => c.concept === 'authentication');
        expect(authConcept).toBeDefined();
        expect(authConcept?.keywords).toContain('auth');
      }
    });
  });

  describe('ProjectAnalyzer', () => {
    test('should detect TypeScript project', async () => {
      const analyzer = new ProjectAnalyzer(process.cwd());
      const analysis = await analyzer.analyze();

      // This project is TypeScript
      expect(analysis.primaryLanguage).toBe('typescript');
      expect(analysis.buildSystem).toMatch(/npm|yarn|pnpm/);
      expect(analysis.sourceDirectories).toContain('src');
      expect(analysis.confidence).toBeGreaterThan(0.5);
    });

    test('should find source directories', async () => {
      const analyzer = new ProjectAnalyzer(process.cwd());
      const analysis = await analyzer.analyze();

      expect(analysis.sourceDirectories.length).toBeGreaterThan(0);
      expect(analysis.sourceDirectories).toContain('src');
    });

    test('should find test directories', async () => {
      const analyzer = new ProjectAnalyzer(process.cwd());
      const analysis = await analyzer.analyze();

      // This project has src/tests
      expect(analysis.testDirectories.length).toBeGreaterThan(0);
    });

    test('should find configuration files', async () => {
      const analyzer = new ProjectAnalyzer(process.cwd());
      const analysis = await analyzer.analyze();

      expect(analysis.configFiles.length).toBeGreaterThan(0);
      expect(analysis.configFiles).toContain('tsconfig.json');
    });

    test('should get file extensions for detected language', async () => {
      const analyzer = new ProjectAnalyzer(process.cwd());
      const analysis = await analyzer.analyze();

      const extensions = analyzer.getFileExtensions(analysis);
      expect(extensions).toContain('.ts');
      expect(extensions).toContain('.tsx');
    });
  });

  describe('DiscoveryEngine', () => {
    test('should discover context for API-related query', async () => {
      const engine = new DiscoveryEngine(process.cwd());
      const context = await engine.discover('Add authentication to the API endpoints');

      expect(context.projectAnalysis).toBeDefined();
      expect(context.projectAnalysis.primaryLanguage).toBe('typescript');

      // Should suggest relevant directories
      expect(context.suggestedScopePaths.length).toBeGreaterThan(0);

      // Should have search hints
      expect(context.searchHints.length).toBeGreaterThan(0);

      // Should detect auth and api concepts
      const authHint = context.searchHints.find(h => h.context.includes('authentication'));
      expect(authHint).toBeDefined();
    });

    test('should discover context for database-related query', async () => {
      const engine = new DiscoveryEngine(process.cwd());
      const context = await engine.discover('Refactor the database models');

      // Should detect database concept
      const dbHint = context.searchHints.find(h => h.context.includes('database'));
      expect(dbHint).toBeDefined();
    });

    test('should discover context for testing query', async () => {
      const engine = new DiscoveryEngine(process.cwd());
      const context = await engine.discover('Add tests for the authentication module', {
        includeTests: true,
      });

      // Should include test directories
      expect(context.projectAnalysis.testDirectories.length).toBeGreaterThan(0);
    });

    test('should limit results based on options', async () => {
      const engine = new DiscoveryEngine(process.cwd());
      const context = await engine.discover('Refactor the codebase', {
        maxFiles: 5,
        maxDirectories: 2,
      });

      expect(context.suggestedScopeFiles.length).toBeLessThanOrEqual(5);
      expect(context.relevantDirectories.length).toBeLessThanOrEqual(2);
    });

    test('should format context for planning', async () => {
      const engine = new DiscoveryEngine(process.cwd());
      const context = await engine.discover('Add API authentication');

      const formatted = engine.formatForPlanning(context);

      expect(formatted).toContain('Project Analysis');
      expect(formatted).toContain('Language');
      expect(formatted).toContain('typescript');
      expect(formatted).toContain('Suggested Scope');
    });
  });

  describe('Discovery API', () => {
    test('should provide simple discoverContext function', async () => {
      const context = await discoverContext('Add authentication to the API');

      expect(context).toBeDefined();
      expect(context.projectAnalysis).toBeDefined();
      expect(context.suggestedScopeFiles).toBeDefined();
      expect(context.suggestedScopePaths).toBeDefined();
      expect(context.searchHints).toBeDefined();
    });

    test('should format discovery context', async () => {
      const context = await discoverContext('Refactor the agent system');
      const formatted = formatDiscoveryContext(context);

      expect(formatted).toContain('Project Analysis');
      expect(formatted).toContain('Instructions');
      expect(formatted).toContain('scopeFiles');
    });

    test('should cache project analysis across calls', async () => {
      const context1 = await discoverContext('Task 1');
      const context2 = await discoverContext('Task 2');

      // Should reuse the same project analysis
      expect(context1.projectAnalysis.primaryLanguage).toBe(context2.projectAnalysis.primaryLanguage);
      expect(context1.projectAnalysis.rootDir).toBe(context2.projectAnalysis.rootDir);
    });
  });

  describe('Language-Agnostic Behavior', () => {
    test('should handle unknown project gracefully', async () => {
      // Create engine with a non-existent directory
      const engine = new DiscoveryEngine('/tmp/nonexistent-project-xyz');
      const context = await engine.discover('Add a feature');

      // Should fall back to unknown language
      expect(context.projectAnalysis.primaryLanguage).toBe('unknown');
      expect(context.projectAnalysis.confidence).toBeLessThan(0.5);

      // Should still provide fallback suggestions
      expect(context.suggestedScopePaths.length).toBeGreaterThanOrEqual(0);
    });

    test('should extract multiple concepts from complex query', async () => {
      const context = await discoverContext(
        'Add JWT authentication to the API endpoints and update the database models'
      );

      // Should detect multiple concepts: auth, api, database
      expect(context.searchHints.length).toBeGreaterThan(1);

      const concepts = context.searchHints.map(h => h.context.toLowerCase());
      const hasAuth = concepts.some(c => c.includes('authentication'));
      const hasDb = concepts.some(c => c.includes('database'));

      expect(hasAuth || hasDb).toBe(true);
    });
  });

  describe('Integration with Planning', () => {
    test('should provide data structure compatible with planning schema', async () => {
      const context = await discoverContext('Implement user authentication');

      // Check that the structure matches what planning expects
      expect(Array.isArray(context.suggestedScopeFiles)).toBe(true);
      expect(Array.isArray(context.suggestedScopePaths)).toBe(true);
      expect(Array.isArray(context.searchHints)).toBe(true);

      // Check search hints structure
      if (context.searchHints.length > 0) {
        const hint = context.searchHints[0];
        expect(hint).toHaveProperty('pattern');
        expect(hint).toHaveProperty('context');
        expect(typeof hint.pattern).toBe('string');
        expect(typeof hint.context).toBe('string');
      }
    });

    test('should generate formatted output suitable for LLM consumption', async () => {
      const context = await discoverContext('Add API rate limiting');
      const formatted = formatDiscoveryContext(context);

      // Should be well-structured markdown
      expect(formatted).toContain('##');
      expect(formatted).toContain('-');

      // Should provide clear instructions
      expect(formatted).toContain('scopeFiles');
      expect(formatted).toContain('scopePaths');
      expect(formatted).toContain('tool budget');
    });
  });
});
