import { describe, test, expect } from 'bun:test';
import { config } from 'dotenv';

config();

// Note: These tests verify the integration structure but don't call the actual API
// to avoid API costs. For full integration testing, run with OPENAI_API_KEY set.

describe('Planning Integration with Discovery', () => {
  test('should import discovery functions successfully', async () => {
    const { discoverContext, formatDiscoveryContext } = await import('../agents/helpers/discovery');

    expect(discoverContext).toBeDefined();
    expect(typeof discoverContext).toBe('function');
    expect(formatDiscoveryContext).toBeDefined();
    expect(typeof formatDiscoveryContext).toBe('function');
  });

  test('should import PlanningAgent with discovery integration', async () => {
    const { createStructuredPlan } = await import('../agents/PlanningAgent');

    expect(createStructuredPlan).toBeDefined();
    expect(typeof createStructuredPlan).toBe('function');
  });

  test('should run discovery and format context', async () => {
    const { discoverContext, formatDiscoveryContext } = await import('../agents/helpers/discovery');

    const context = await discoverContext('Add authentication to the API');
    expect(context).toBeDefined();
    expect(context.projectAnalysis).toBeDefined();
    expect(context.suggestedScopeFiles).toBeDefined();
    expect(context.suggestedScopePaths).toBeDefined();

    const formatted = formatDiscoveryContext(context);
    expect(formatted).toBeDefined();
    expect(typeof formatted).toBe('string');
    expect(formatted).toContain('Project Analysis');
    expect(formatted).toContain('scopeFiles');
  });

  test('should format discovery context with all required sections', async () => {
    const { discoverContext, formatDiscoveryContext } = await import('../agents/helpers/discovery');

    const context = await discoverContext('Refactor the database layer');
    const formatted = formatDiscoveryContext(context);

    // Check for required sections
    expect(formatted).toContain('## Project Analysis');
    expect(formatted).toContain('Language');
    expect(formatted).toContain('Build System');
    expect(formatted).toContain('Confidence');

    // Check for instructions
    expect(formatted).toContain('scopeFiles');
    expect(formatted).toContain('scopePaths');
    expect(formatted).toContain('tool budget');
  });

  test('should discover context for different task types', async () => {
    const { discoverContext } = await import('../agents/helpers/discovery');

    // API task
    const apiContext = await discoverContext('Add REST API endpoints');
    expect(apiContext.searchHints.length).toBeGreaterThan(0);

    // Auth task
    const authContext = await discoverContext('Implement JWT authentication');
    expect(authContext.searchHints.length).toBeGreaterThan(0);

    // Database task
    const dbContext = await discoverContext('Update database schema');
    expect(dbContext.searchHints.length).toBeGreaterThan(0);

    // UI task
    const uiContext = await discoverContext('Create new component');
    expect(uiContext.searchHints.length).toBeGreaterThan(0);
  });

  test('should respect discovery options', async () => {
    const { discoverContext } = await import('../agents/helpers/discovery');

    const context = await discoverContext('Add tests', {
      maxFiles: 5,
      maxDirectories: 2,
      includeTests: true,
    });

    expect(context.suggestedScopeFiles.length).toBeLessThanOrEqual(5);
    expect(context.relevantDirectories.length).toBeLessThanOrEqual(2);
  });

  test('should handle discovery errors gracefully', async () => {
    const { discoverContext } = await import('../agents/helpers/discovery');

    // Discovery with invalid project root should not throw
    try {
      const context = await discoverContext('Test task', {
        projectRoot: '/nonexistent/path/xyz',
      });

      // Should return context with unknown language
      expect(context.projectAnalysis.primaryLanguage).toBe('unknown');
      expect(context.projectAnalysis.confidence).toBeLessThan(0.5);
    } catch (error) {
      // If it throws, that's also acceptable as long as planning can handle it
      expect(error).toBeDefined();
    }
  });

  test('should generate language-appropriate suggestions', async () => {
    const { discoverContext } = await import('../agents/helpers/discovery');

    // This TypeScript project
    const context = await discoverContext('Add API middleware');

    expect(context.projectAnalysis.primaryLanguage).toBe('typescript');

    // Should suggest TypeScript-appropriate paths
    const hasSrcPath = context.suggestedScopePaths.some(p => p.includes('src'));
    expect(hasSrcPath).toBe(true);
  });

  test('should provide search hints with context', async () => {
    const { discoverContext } = await import('../agents/helpers/discovery');

    const context = await discoverContext('Add authentication middleware');

    expect(context.searchHints.length).toBeGreaterThan(0);

    // Each hint should have pattern and context
    for (const hint of context.searchHints) {
      expect(hint.pattern).toBeDefined();
      expect(typeof hint.pattern).toBe('string');
      expect(hint.context).toBeDefined();
      expect(typeof hint.context).toBe('string');
    }
  });

  test('should cache project analysis across multiple discoveries', async () => {
    const { discoverContext } = await import('../agents/helpers/discovery');

    const start1 = Date.now();
    const context1 = await discoverContext('Task 1');
    const time1 = Date.now() - start1;

    const start2 = Date.now();
    const context2 = await discoverContext('Task 2');
    const time2 = Date.now() - start2;

    // Second call should be faster (cached analysis)
    expect(time2).toBeLessThanOrEqual(time1);

    // Should have same project analysis
    expect(context1.projectAnalysis.primaryLanguage).toBe(context2.projectAnalysis.primaryLanguage);
    expect(context1.projectAnalysis.rootDir).toBe(context2.projectAnalysis.rootDir);
  });

  test('should format context suitable for LLM consumption', async () => {
    const { discoverContext, formatDiscoveryContext } = await import('../agents/helpers/discovery');

    const context = await discoverContext('Implement user registration');
    const formatted = formatDiscoveryContext(context);

    // Should be well-structured markdown
    expect(formatted).toMatch(/##\s+/); // Has headings
    expect(formatted).toMatch(/\n-\s+/); // Has lists

    // Should have clear instructions
    expect(formatted.toLowerCase()).toContain('instructions');
    expect(formatted).toContain('scopeFiles');
    expect(formatted).toContain('scopePaths');

    // Should mention tool budget
    expect(formatted.toLowerCase()).toContain('tool budget');
  });

  test('should integrate with planning schema', async () => {
    const { discoverContext } = await import('../agents/helpers/discovery');

    const context = await discoverContext('Add API rate limiting');

    // Verify structure matches planning schema expectations
    expect(Array.isArray(context.suggestedScopeFiles)).toBe(true);
    expect(Array.isArray(context.suggestedScopePaths)).toBe(true);
    expect(Array.isArray(context.searchHints)).toBe(true);

    // Verify search hints match schema
    if (context.searchHints.length > 0) {
      const hint = context.searchHints[0];
      expect(hint).toHaveProperty('pattern');
      expect(hint).toHaveProperty('context');

      // These should be strings as expected by planning schema
      expect(typeof hint.pattern).toBe('string');
      expect(typeof hint.context).toBe('string');
    }
  });

  test('should provide meaningful suggestions for code generation tasks', async () => {
    const { discoverContext } = await import('../agents/helpers/discovery');

    const context = await discoverContext('Refactor the API routes and services');

    // Should provide scope files for code generation (API routes exist in this project)
    expect(context.suggestedScopeFiles.length).toBeGreaterThan(0);

    // Should provide scope paths
    expect(context.suggestedScopePaths.length).toBeGreaterThan(0);

    // Should provide search hints
    expect(context.searchHints.length).toBeGreaterThan(0);
  });

  test('should handle multi-concept queries', async () => {
    const { discoverContext } = await import('../agents/helpers/discovery');

    const context = await discoverContext(
      'Add API endpoints for the agent system and update the tool handlers'
    );

    // Should detect multiple concepts
    expect(context.searchHints.length).toBeGreaterThan(1);

    // Should provide comprehensive scope (API and agents exist in this project)
    expect(context.suggestedScopeFiles.length).toBeGreaterThan(0);
    expect(context.suggestedScopePaths.length).toBeGreaterThan(0);
  });
});
