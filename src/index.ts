// Vercel AI SDK - no longer need OpenAI Agents SDK imports
export interface RunContext {
  [key: string]: any;
}

export type AgentInputItem = string | { role: string; content: string };
import DanteOrchestrator from "./agents/DanteOrchestrator";
import { orchestrator } from "./agents/DanteOrchestrator";
import * as path from 'path';
// Re-enabled MCP lifecycle with Vercel AI SDK integration
import { initializeMCPServers, cleanupMCP } from "./agents";
import { config, validateConfig, validateGeminiConfig } from "./utils/config";
import { memoryManager } from "./memory/MemoryManager";
import { mcpConfig } from "./config/mcpConfig";
import { mcpServerManager } from "./mcp/MCPServerManager";
import { getMCPStatus as coreGetMCPStatus } from "./mcp";
// import { mcpToolFactory } from './tools'; // Temporarily disabled
import { initializeTraceProcessor } from "./utils/traceProcessor";

// Initialize configuration
validateConfig();
validateGeminiConfig();

// Initialize trace processor for local trace capture
initializeTraceProcessor();

// Initialize MCP servers if enabled
let mcpInitialized = false;
async function initializeMCP() {
  if (mcpInitialized || !mcpConfig.enabled) return;
  try {
    console.log("🔧 Initializing MCP servers...");
    await initializeMCPServers();
    mcpInitialized = true;
    console.log("✅ MCP initialized");
  } catch (err) {
    console.warn("❌ MCP initialization failed:", err);
  }
}

// Initialize MCP on startup (do not block startup)
void initializeMCP();

// Graceful shutdown handling
process.on("SIGINT", async () => {
  console.log("Shutting down gracefully...");
  try {
    await cleanupMCP();
  } catch (err) {
    console.warn("MCP cleanup on SIGINT failed:", err);
  }
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("Shutting down gracefully...");
  try {
    await cleanupMCP();
  } catch (err) {
    console.warn("MCP cleanup on SIGTERM failed:", err);
  }
  process.exit(0);
});

/**
 * Main function to run Dante with the unified agent architecture
 * Supports seamless model switching while preserving context
 */
export async function runDante(
  input: string | AgentInputItem[],
  options?: {
    stream?: boolean;
    context?: RunContext;
    maxTurns?: number;
    model?: string;
    useMemory?: boolean;
    useMCP?: boolean;
    taskType?: "orchestration" | "analysis" | "coding" | "simple";
    sessionId?: string;
    userId?: string;
    forceWorker?: string;
    voiceMode?: boolean;
    voiceOutputCallback?: (
      text: string,
      options: { voice?: string },
    ) => Promise<void>;
    onStepFinish?: (step: any) => void; // Vercel AI SDK step callback for richer events
    onAgentSelected?: (payload: { agent: string; confidence: number }) => void; // emit when router picks agent
    onDelegationStart?: (payload: {
      stepId: string;
      agent: string;
      title?: string;
      description?: string;
    }) => void;
    onDelegationEnd?: (payload: {
      stepId: string;
      agent: string;
      success: boolean;
    }) => void;
    onPlanCreated?: (payload: { summary?: string; steps: any[] }) => void;
    onToolCall?: (
      toolName: string,
      args: unknown,
      result?: unknown,
    ) => void | Promise<void>;
    onAgentSwitch?: (agentName: string) => void | Promise<void>;
    onText?: (text: string) => void | Promise<void>;
    workingDirectory?: string;
  },
): Promise<any> {
  const {
    stream = false,
    context,
    maxTurns = config.app.maxTurns,
    model,
    taskType = "orchestration",
    sessionId,
    userId,
    forceWorker,
    voiceMode = false,
    voiceOutputCallback,
    onStepFinish,
    onAgentSelected,
    onDelegationStart,
    onDelegationEnd,
    onPlanCreated,
    workingDirectory,
  } = options || {};

  const explicitWorkingDir = workingDirectory
    ?? (context && typeof (context as any).workingDirectory === 'string' ? (context as any).workingDirectory : undefined);
  const normalizedWorkingDirectory = explicitWorkingDir ? path.resolve(explicitWorkingDir) : undefined;

  // Normalize various result shapes to a plain string for non-streaming callers
  const normalizeToText = (res: any): string => {
    try {
      if (res == null) return "";
      if (typeof res === "string") return res;
      const paths: Array<Array<string | number>> = [
        ["text"],
        ["content"],
        ["finalMessage"],
        ["message", "content"],
        ["output", "content"],
        ["choices", 0, "message", "content"],
      ];
      for (const p of paths) {
        try {
          let v: any = res;
          for (const key of p) {
            if (v == null) break;
            v = typeof key === "number" ? v[key] : v[key];
          }
          if (typeof v === "string" && v.trim().length > 0) return v;
        } catch {}
      }
      return typeof res === "object" ? JSON.stringify(res) : String(res);
    } catch {
      try {
        return JSON.stringify(res);
      } catch {
        return String(res);
      }
    }
  };

  try {
    // Keep the Vercel AI orchestrator in sync with the requested model
    if (model && typeof (DanteOrchestrator as any).setModel === "function") {
      const normalize = (id: string) =>
        id.startsWith("models/") ? id.slice("models/".length) : id;
      try {
        (DanteOrchestrator as any).setModel(normalize(model));
      } catch {}
    }
    // Use the unified orchestrator for intelligent request processing
    const flattened = Array.isArray(input)
      ? input
          .map((i) => (typeof i === "string" ? i : (i as any).content))
          .join("\n")
      : typeof input === "string"
        ? input
        : String(input);
    const result = await orchestrator.process(flattened, {
      stream,
      maxSteps: maxTurns,
      onStepFinish,
      onAgentSelected,
      routeText: Array.isArray(input)
        ? (input as any[])
            .filter((m) => (m as any).role === "user")
            .slice(-1)[0]?.content
        : undefined,
      sessionId,
      initialWorkingDirectory: normalizedWorkingDirectory,
    });

    // For non-streaming executions, normalize to string for simple/orchestration/analysis tasks
    if (!stream) {
      const isStreamLike =
        result &&
        (typeof (result as any)[Symbol.asyncIterator] === "function" ||
          (result as any)?.textStream);
      if (!isStreamLike && taskType !== "coding") {
        return normalizeToText(result);
      }
    }
    return result;
  } catch (error) {
    console.error("Error in unified Dante system:", error);

    // Attempt graceful degradation - try with a fallback model
    if (model && model !== config.openai.defaultModel) {
      // Fallback handling: ensure we actually switch models before retrying.
      // Previously, the retry reused the original failing model because the orchestrator’s
      // current model wasn’t updated. This explicitly switches to config.openai.defaultModel
      // so the retry runs on the intended fallback.
      console.warn(`⚠️ Attempting fallback to ${config.openai.defaultModel}...`);
      try {
        // Ensure the orchestrator actually switches to the fallback model
        try {
          const normalize = (id: string) => id.startsWith('models/') ? id.slice('models/'.length) : id;
          (DanteOrchestrator as any).setModel(normalize(config.openai.defaultModel));
        } catch (switchErr) {
          console.warn('Model switch for fallback failed (continuing):', switchErr);
        }
        const fbFlattened = Array.isArray(input)
          ? input
              .map((i) => (typeof i === "string" ? i : (i as any).content))
              .join("\n")
          : typeof input === "string"
            ? input
            : String(input);
        const fbResult = await orchestrator.process(fbFlattened, {
          stream,
          maxSteps: maxTurns,
        });
        // Normalize fallback result to a plain string for compatibility with tests/consumers
        return normalizeToText(fbResult);
      } catch (fallbackError) {
        console.error("Fallback also failed:", fallbackError);
      }
    }

    throw error;
  }
}

/**
 * Get comprehensive status of the unified Dante system
 */
export function getDanteStatus(): {
  agent: any;
  orchestrator: ReturnType<typeof orchestrator.getStatus>;
  memory: { initialized: boolean; status: any };
  mcp: {
    enabled: boolean;
    initialized: boolean;
    serverCount: number;
    connectedCount?: number;
  };
  availableModels: string[];
} {
  // Get dynamic MCP status from core integration (no stubs)
  const coreStatus = coreGetMCPStatus();
  const mcpStatus = {
    enabled: coreStatus.enabled,
    initialized: coreStatus.initialized,
    serverCount: coreStatus.serverCount,
    connectedCount: coreStatus.connectedCount,
  };

  // Get orchestrator metrics for more dynamic agent info
  const orchestratorMetrics = orchestrator.getStatus();

  return {
    agent: {
      name: DanteOrchestrator.name,
      model: DanteOrchestrator.model,
      // Dynamic status based on orchestrator health and activity
      isInitialized: coreStatus.initialized || !mcpConfig.enabled,
      status: orchestratorMetrics.systemHealth,
      activeRequests: orchestratorMetrics.activeRequests,
      totalRequestsProcessed: orchestratorMetrics.totalRequestsProcessed,
      // Agent is ready if system is not critical and MCP is properly initialized
      isReady:
        orchestratorMetrics.systemHealth !== "critical" &&
        (!mcpConfig.enabled || coreStatus.initialized),
    },
    orchestrator: orchestratorMetrics,
    memory: {
      initialized: memoryManager.getInitializationStatus(),
      status: memoryManager.getInitializationStatus() ? "active" : "inactive",
    },
    mcp: mcpStatus,
    availableModels: [String(DanteOrchestrator.model)],
  };
}

// Private helper functions
function calculateRequestPriority(
  input: string | AgentInputItem[],
  taskType?: string,
): number {
  const inputStr = Array.isArray(input)
    ? input
        .map((i) => (typeof i === "string" ? i : JSON.stringify(i)))
        .join(" ")
    : input;

  const inputLower = inputStr.toLowerCase();

  // High priority indicators
  if (
    inputLower.includes("urgent") ||
    inputLower.includes("emergency") ||
    inputLower.includes("critical")
  ) {
    return 3;
  }

  // Medium priority indicators
  if (
    inputLower.includes("quick") ||
    inputLower.includes("fast") ||
    taskType === "simple"
  ) {
    return 2;
  }

  // Default priority
  return 1;
}

// Export for backward compatibility and testing
export {
  DanteOrchestrator,
  memoryManager,
  orchestrator,
  // cleanupMCP temporarily removed during migration
};

// Export MCP status function for CLI compatibility
// Delegate MCP status to core MCP integration (no stub values)
export function getMCPStatus() {
  return coreGetMCPStatus();
}

// Export additional components for CLI compatibility
export { mcpServerManager, mcpConfig };

// Export model orchestrator methods for API server
export function switchModel(
  newModel: string,
  reason?: string,
  _sessionId?: string, // Prefixed with _ to indicate intentionally unused
): { success: boolean; model: string; message: string } {
  try {
    // Model switching is handled through the processRequest method
    // Check if DanteOrchestrator has a setModel method
    if (typeof (DanteOrchestrator as any).setModel === "function") {
      (DanteOrchestrator as any).setModel(newModel);
    } else {
      // Fallback to directly setting the model property
      // Note: sessionId could be used for session-specific model switching in future
      (DanteOrchestrator as any).model = newModel;
    }

    return {
      success: true,
      model: newModel,
      message: `Successfully switched to model: ${newModel}${reason ? ` - Reason: ${reason}` : ""}`,
    };
  } catch (error) {
    return {
      success: false,
      model: String(DanteOrchestrator.model),
      message: `Failed to switch model: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}

export function getModelRecommendation(
  input: string,
  taskType?: string,
  availableModels?: string[],
): { recommendedModel: string; reason: string; confidence: number } {
  const current = String(DanteOrchestrator.model);
  return {
    recommendedModel: current,
    reason: "Selected by unified orchestrator",
    confidence: 0.8,
  };
}

// Types are already exported above, no need to re-export
