# Briefing Runbook

This document details the operation, scheduling, and troubleshooting of the Dante-GPT Flutter Briefing MVP.

## Prerequisites

To run the briefing scripts, ensure the following are in place:

*   **Bun Version**: The project requires Bun for runtime. Ensure you have a compatible version installed.
*   **Environment Variables**:
    *   `BRIEFING_DB_PATH`: Path to the SQLite database file for storing briefing data (e.g., `briefing.db`).
    *   `BRIEFING_RSS_TIMEOUT_MS`: Timeout in milliseconds for RSS feed ingestion (e.g., `5000` for 5 seconds).

## Ingest

The ingestion process fetches and processes RSS feeds to populate the briefing database.

*   **Command**: `bun run briefing:ingest`
*   **Description**: This script connects to configured RSS feeds, fetches new articles, and stores relevant information in the database. It handles duplicate entries and updates existing ones.
*   **Expected Logs**:
    *   `[Briefing] Ingesting RSS feeds...`
    *   `[Briefing] Fetched X articles from Y feeds.`
    *   `[Briefing] New articles added: Z`
    *   `[Briefing] Articles updated: A`

## Compose

The composition process generates the daily or weekly briefing content based on the ingested data.

*   **Commands**:
    *   `bun run briefing:compose:daily`: Generates the daily briefing.
    *   `bun run briefing:compose:weekly`: Generates the weekly briefing.
*   **Outputs**: Generated briefing content (e.g., HTML files) will be found in the `out/` directory.

## Scheduling Strategy

The briefing ingestion and composition are designed to run automatically using a process manager like PM2.

*   **Daily Briefing**:
    *   **Frequency**: Once every day.
    *   **Time**: 08:00 AM (e.g., local time).
    *   **PM2 Command Example**:
        ```bash
        pm2 start "bun run briefing:ingest && bun run briefing:compose:daily" --name "briefing-daily" --cron "0 8 * * *" --no-autorestart
        ```
*   **Weekly Briefing**:
    *   **Frequency**: Once every week on Monday.
    *   **Time**: 08:00 AM (e.g., local time).
    *   **PM2 Command Example**:
        ```bash
        pm2 start "bun run briefing:ingest && bun run briefing:compose:weekly" --name "briefing-weekly" --cron "0 8 * * 1" --no-autorestart
        ```

## Next Steps

Future enhancements and integrations for the briefing system include:

*   **Email Provider Integration**: Integrate with an email service (e.g., SendGrid, Mailgun) to send out the generated briefings.
*   **Unsubscribe Functionality**: Implement a mechanism for users to unsubscribe from briefings.
*   **DKIM/SPF Configuration**: Set up DomainKeys Identified Mail (DKIM) and Sender Policy Framework (SPF) for improved email deliverability.
*   **Stripe Gating**: Implement Stripe integration for premium access or subscription-based briefings.

## Troubleshooting

Common issues and their potential solutions:

*   **RSS Feed Failures**:
    *   **Symptom**: Ingestion reports 0 articles fetched from certain feeds.
    *   **Action**: Check the RSS feed URLs for correctness, ensure the feed is online, and verify the `BRIEFING_RSS_TIMEOUT_MS` is sufficient.
*   **Rate Limits**:
    *   **Symptom**: Specific RSS feeds or APIs return errors related to too many requests.
    *   **Action**: Implement exponential backoff or increase delays between requests to external services.
*   **Database Locks**:
    *   **Symptom**: Scripts fail with SQLite database lock errors.
    *   **Action**: Ensure only one instance of the `ingest` or `compose` script is running at a time. Review database access patterns to minimize contention.
*   **Bun Fetch Timeouts**:
    *   **Symptom**: Ingestion fails with network timeout errors.
    *   **Action**: Increase the `BRIEFING_RSS_TIMEOUT_MS` environment variable to allow more time for fetching content from slow sources. Check network connectivity.