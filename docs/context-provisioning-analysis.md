# Context Provisioning Analysis: Are Agents Receiving Sufficient Information?

## Executive Summary

**Finding**: There is a **significant gap** between the tool call limits we've implemented (3 list_directory, 2 grep_code) and the contextual information provided to agents before execution. Agents are likely to exhaust their exploration budget before finding what they need.

**Risk Level**: 🔴 **HIGH** - The new guardrails may cause agents to fail tasks they could previously complete, not because of actual context overflow, but because they lack upfront guidance.

**Recommendation**: Implement enhanced context provisioning in the planning and orchestration phases to provide agents with a "map" of relevant files before they begin execution.

---

## 1. Context Packaging Analysis

### Current State: ContextPackager

**Location**: `src/agents/orchestration/context/ContextPackager.ts`

#### What It Provides ✅
- **File slices**: Full content (if <16KB) or header/footer slices (120/60 lines)
- **Interface snippets**: Up to 3 exported declarations per file (±15 lines)
- **Prior summaries**: WorkUnit description (if ≤500 chars)
- **Retrieval keys**: File paths, module names, components, directory segments
- **Token cap**: Hard limit of 6000 tokens (default)

#### What It DOESN'T Provide ❌
- **No directory structure**: Agents don't know what files exist outside their scope
- **No search results**: No pre-computed grep results for relevant patterns
- **No file relationships**: No import/export graph or dependency information
- **No recent changes**: No git history or recently modified files
- **No scope boundaries**: No explicit "these directories are in-scope, these are not"

### Gap Analysis

The ContextPackager **only packages files already identified in `workUnit.scope.files`**. This creates a chicken-and-egg problem:

```typescript
// From ContextPackager.ts:128
const filesInScope = (workUnit?.scope?.files ?? []).filter(Boolean);

// Early return if no files
if (!filesInScope.length) {
  return minimal; // Just retrieval keys, no actual content
}
```

**Problem**: If the planner doesn't identify specific files, agents receive **zero file content** and must discover everything themselves using their limited tool budget.

---

## 2. Planning Quality Analysis

### Current State: PlanningAgent

**Location**: `src/agents/PlanningAgent.ts`

#### What It Has Access To ✅
- **Tools**: `taskManagementTools` + `fileOperationTools` (lines 95)
- **Can explore**: Has access to list_directory, grep_code, read_file during planning
- **Structured output**: Creates steps with `references`, `handoffNotes`, `instructions`

#### What It Actually Does ❌

Looking at the plan schema (lines 138-151):

```typescript
steps: z.array(z.object({
  id: z.string(),
  title: z.string().optional(),
  focus: z.string().optional(),
  description: z.string(),
  agent: z.string(),
  instructions: z.array(z.string()).min(1),
  input: z.string().optional(),
  dependsOn: z.array(z.string()).optional(),
  expectedOutputs: z.array(z.string()).optional(),
  acceptanceCriteria: z.array(z.string()).optional(),
  handoffNotes: z.string().optional(),
  references: z.array(z.string()).optional(), // ⚠️ This is the key field
}))
```

**The `references` field exists but is optional and rarely populated with specific file paths.**

### Planning Instructions Analysis

From lines 179-193:

```
Rules:
- Provide 3–8 minimal, actionable steps.
- Instructions must describe exactly what the assigned agent should do
- Include handoffNotes when later steps need to inspect artifacts
- Use dependsOn to encode ordering
- Choose the agent by capability
- For discovery tasks use agent='research'
- Only pick agent='code-generation' when the step must modify code
```

**Missing**: No instruction to **identify specific files** during planning and include them in `references` or `scope.files`.

---

## 3. Agent Input Construction

### Current State: buildAgentInput()

**Location**: `src/agents/PlanningAgent.ts:362-447`

#### What It Includes ✅
- Step title, focus, description
- Plan summary context
- Dependency summaries from previous steps
- Step-specific instructions
- Expected outputs / acceptance criteria
- Handoff notes
- References (if provided)
- **Suggested Focus Path** (lines 430-443) - inferred from recent tool usage

#### What's Missing ❌
- **No directory structure**: Agents don't know "src/agents/ contains X files"
- **No file discovery results**: No pre-computed list of relevant files
- **No scope boundaries**: No "only modify files in src/agents/, not src/tools/"

### The "Suggested Focus Path" Feature

Lines 430-443 show an attempt to provide context:

```typescript
const suggestedPath = inferLastAffectedPath(orchestrator.recentToolArgs, recentWrites);
if (suggestedPath) {
  lines.push('Suggested Focus Path:');
  lines.push(`- ${suggestedPath}`);
  lines.push('If making changes, start with read_file...');
}
```

**This is reactive, not proactive**: It only works if previous steps have already touched files. For the first step or independent tasks, there's no guidance.

---

## 4. Gap Analysis: Tool Budget vs. Context Needs

### Scenario: "Add authentication to the API"

#### What the Agent Receives
```
Step: Implement Authentication
Primary Objective: Add JWT-based authentication to the API endpoints

Step Instructions:
1. Identify the API route handlers
2. Add authentication middleware
3. Update existing endpoints to use the middleware
4. Add tests for authenticated routes

Completion Requirements:
- All API endpoints require authentication
- JWT tokens are validated correctly
- Tests pass
```

#### What the Agent Needs to Discover
1. **Where are the API routes?** → Needs `list_directory` or `grep_code`
2. **What files define routes?** → Needs `read_file` on multiple candidates
3. **Where should middleware go?** → Needs to understand project structure
4. **What's the testing pattern?** → Needs to find existing tests

#### Tool Budget Reality
- **3 list_directory calls**: Maybe enough to find `src/api/`, `src/middleware/`, `src/tests/`
- **2 grep_code calls**: One for "router" or "app.use", one for "test" patterns
- **5 read_files calls**: Barely enough to read the files found

**Result**: Agent might exhaust budget just finding files, leaving no room for actual implementation exploration.

---

## 5. Comparison: Fine-Grained Orchestration vs. Current Planning

### Fine-Grained Orchestration (Designed But Not Used)

The codebase has a sophisticated orchestration system that **could** provide better context:

**From `src/agents/orchestration/types.ts:149-181`**:

```typescript
interface WorkUnit {
  scope: {
    files?: string[];      // ✅ Specific files
    modules?: string[];    // ✅ Module names
    components?: string[]; // ✅ Component names
    paths?: string[];      // ✅ Directory paths
  };
  acceptanceCriteria: string[];
  // ...
}
```

**From `AssignmentManager.ts:199-234`**:

The `composeScope()` method formats file lists, components, modules, and paths into the instruction template.

**Problem**: This system is designed for fine-grained orchestration but **the current PlanningAgent doesn't populate these fields** with specific file paths.

---

## 6. Recommendations

### Priority 1: Enhanced Planning Phase Discovery (CRITICAL)

**Modify PlanningAgent to perform upfront discovery**:

```typescript
// In createStructuredPlan(), before calling the plan tool:

1. If task mentions "API" or "routes":
   - Call list_directory_lite('src/api', maxDepth: 2)
   - Call grep_code(pattern: 'router|app\\.use', searchPath: 'src')
   - Include results in plan context

2. If task mentions "authentication" or "middleware":
   - Call list_directory_lite('src/middleware', maxDepth: 1)
   - Call grep_code(pattern: 'auth|jwt|token', searchPath: 'src')
   
3. If task mentions specific file types:
   - Call list_directory_lite with appropriate paths
   - Include file counts and structure in plan summary
```

**Implementation**: Add a `discoverContext()` function that runs before planning:

```typescript
async function discoverContext(query: string): Promise<DiscoveryContext> {
  const discoveries: DiscoveryContext = {
    relevantDirectories: [],
    relevantFiles: [],
    searchResults: [],
  };
  
  // Pattern-based discovery
  if (/api|route|endpoint/i.test(query)) {
    const apiStructure = await listDirectoryLiteTool.execute({
      dirPath: 'src/api',
      maxDepth: 2,
    });
    discoveries.relevantDirectories.push(apiStructure);
  }
  
  // Keyword-based search
  const keywords = extractKeywords(query);
  for (const keyword of keywords.slice(0, 2)) { // Limit to 2 searches
    const results = await grepCodeTool.execute({
      pattern: keyword,
      searchPath: 'src',
      maxResults: 50,
    });
    discoveries.searchResults.push(results);
  }
  
  return discoveries;
}
```

### Priority 2: Populate WorkUnit.scope.files in Planning

**Modify the plan tool schema to require file identification**:

```typescript
// In createStructuredPlan(), update the plan tool schema:
steps: z.array(z.object({
  // ... existing fields ...
  scopeFiles: z.array(z.string()).optional()
    .describe('Specific files this step will read or modify'),
  scopePaths: z.array(z.string()).optional()
    .describe('Directories relevant to this step'),
  searchHints: z.array(z.object({
    pattern: z.string(),
    context: z.string(),
  })).optional()
    .describe('Patterns to search for if agent needs more context'),
}))
```

**Update planning instructions**:

```
CRITICAL: For code-generation steps, you MUST identify specific files:
- Use the discovery context provided above
- List 3-10 specific file paths in scopeFiles
- If uncertain, include scopePaths (directories) and searchHints (patterns)
- Example: scopeFiles: ["src/api/routes.ts", "src/middleware/auth.ts"]
```

### Priority 3: Pre-Execution Context Enrichment

**Add a context enrichment step in `buildAgentInput()`**:

```typescript
// After line 429 in PlanningAgent.ts:

if (step.scopeFiles && step.scopeFiles.length > 0) {
  lines.push('');
  lines.push('Files In Scope:');
  for (const file of step.scopeFiles) {
    lines.push(`- ${file}`);
  }
  lines.push('Start by reading these files with read_file before making changes.');
}

if (step.scopePaths && step.scopePaths.length > 0) {
  lines.push('');
  lines.push('Relevant Directories:');
  for (const dir of step.scopePaths) {
    lines.push(`- ${dir}/`);
  }
  lines.push('Use list_directory_lite on these paths if you need to explore further.');
}

if (step.searchHints && step.searchHints.length > 0) {
  lines.push('');
  lines.push('Search Hints (if you need more context):');
  for (const hint of step.searchHints) {
    lines.push(`- Pattern: "${hint.pattern}" (${hint.context})`);
  }
}
```

### Priority 4: Handoff Enrichment

**Enhance dependency summaries to include file paths**:

```typescript
// In buildAgentInput(), modify lines 382-390:

if (context.dependencySummaries && context.dependencySummaries.length > 0) {
  lines.push('Existing Progress From Previous Steps:');
  for (const dep of context.dependencySummaries) {
    const summary = dep.summary || 'No summary';
    const handoff = dep.handoffNotes || '';
    
    // NEW: Extract file paths from handoff notes
    const filePaths = extractFilePathsFromText(handoff);
    if (filePaths.length > 0) {
      lines.push(`- ${dep.id}: ${summary}`);
      lines.push(`  Files modified: ${filePaths.join(', ')}`);
    } else {
      lines.push(`- ${dep.id}: ${summary}`);
    }
  }
}
```

---

## 7. Implementation Priority

### Phase 1: Quick Wins (1-2 days)
1. ✅ Update PlanningAgent instructions to emphasize file identification
2. ✅ Add `scopeFiles` and `scopePaths` to plan schema
3. ✅ Enhance `buildAgentInput()` to display scope files prominently

### Phase 2: Discovery Integration (3-5 days)
1. ✅ Implement `discoverContext()` function
2. ✅ Integrate discovery results into planning prompt
3. ✅ Update ContextPackager to use discovered files

### Phase 3: Advanced Features (1-2 weeks)
1. ✅ File relationship graph (imports/exports)
2. ✅ Git history integration (recently changed files)
3. ✅ Semantic code search (beyond grep)
4. ✅ Caching of discovery results

---

## 8. Success Metrics

### Before Enhancement
- **Agent exploration calls**: 5-8 per task (exhausting budget)
- **False failures**: ~30% of tasks fail due to "couldn't find files"
- **Token waste**: 20-40% of tokens spent on exploration

### After Enhancement
- **Agent exploration calls**: 1-3 per task (within budget)
- **False failures**: <10% (only genuine issues)
- **Token efficiency**: 80%+ of tokens spent on actual work

---

## 9. Conclusion

**The new tool call limits are correct and necessary**, but they expose a fundamental gap in our orchestration: **agents are expected to be surgeons but are given no map of the patient**.

The solution is not to relax the limits, but to **enhance the planning and context provisioning phases** to give agents the information they need upfront. This aligns with the fine-grained orchestration architecture already present in the codebase but not fully utilized.

**Next Steps**:
1. Implement Phase 1 quick wins immediately
2. Test with real tasks to measure improvement
3. Iterate on discovery heuristics based on results
4. Consider enabling fine-grained orchestration for complex tasks

