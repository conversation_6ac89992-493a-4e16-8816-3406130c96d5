# Agent Loop Prevention Fixes

## Problem Summary

Multiple agents were entering infinite loops where they would successfully complete their tasks, but then immediately restart the entire process, repeating indefinitely.

## Affected Agents

1. **TaskOrchestrator** - Would complete orchestration via `delegate_to_agents`, then restart
2. **CodeGenerationAgent** - Would complete code changes and call `answer` tool, then restart

## Root Cause

Both agents had instructions that told them to perform certain actions but **never told them when to stop**:

### TaskOrchestrator
- Told to call `delegate_to_agents` as first action ✅
- Never told to stop after successful completion ❌
- With `maxSteps: 20`, would continue and call the tool again

### CodeGenerationAgent  
- Told to "Always start by calling get_working_directory" ✅
- Never told to stop after calling `answer` tool ❌
- With `maxSteps: 12`, would "always start" again after finishing

## Solutions Applied

### Fix #1: TaskOrchestrator (Line 212 in `src/agents/BuiltInAgents.ts`)

**Added:**
```typescript
- IMPORTANT: Once delegate_to_agents completes successfully (returns with type='delegated_orchestration' and stepResults), your job is done. Present a brief summary of the results and FINISH. Do NOT call delegate_to_agents again or continue processing.
```

### Fix #2: CodeGenerationAgent (Line 83 in `src/agents/BuiltInAgents.ts`)

**Changed:**
```typescript
// BEFORE (caused loop):
- Always start by calling get_working_directory to confirm...

// AFTER (prevents loop):
- At the start of your work, call get_working_directory to confirm...
```

**Added:**
```typescript
CRITICAL: Once you have completed all code changes and called the 'answer' tool with your structured summary, your work is DONE. Do NOT start over, do NOT call get_working_directory again, do NOT read files again. Simply finish and let the orchestrator handle the response.
```

## Expected Behavior After Fixes

### TaskOrchestrator
```
1. Receives request
2. Calls delegate_to_agents once
3. Orchestration completes successfully
4. Presents summary and FINISHES ✅
5. No loop, no restart
```

### CodeGenerationAgent
```
1. Receives code task
2. Calls get_working_directory (once)
3. Reads files, makes changes
4. Calls answer tool with summary
5. FINISHES ✅
6. No restart, no re-reading files
```

## Log Patterns

### Before Fixes (Loop Detected) ❌
```
✅ Agent CodeGenerationAgent completed
📤 Sending tool call event for: answer
🛠️ Tool call | Agent: CodeGenerationAgent | Tool: answer
Emitting successful final response
✅ Agent CodeGenerationAgent completed in 31ms
📤 Sending tool call event for: get_working_directory  ← LOOP!
🛠️ Tool call | Agent: CodeGenerationAgent | Tool: read_file  ← LOOP!
```

### After Fixes (Clean Completion) ✅
```
✅ Agent CodeGenerationAgent completed
📤 Sending tool call event for: answer
🛠️ Tool call | Agent: CodeGenerationAgent | Tool: answer
Emitting successful final response
✅ Agent CodeGenerationAgent completed in 31ms
[No further tool calls - agent finishes cleanly]
```

## Testing

1. **Test TaskOrchestrator:**
   ```bash
   bun run dev:all
   ```
   Make a complex request:
   ```
   "Integrate Claude Sonnet 4.5 with environment variables, helper module, and tests"
   ```
   
   Watch for:
   - ✅ Single call to `delegate_to_agents`
   - ✅ "— Orchestration complete —"
   - ✅ NO second call to `delegate_to_agents`

2. **Test CodeGenerationAgent:**
   Make a direct code request:
   ```
   "Add Claude Sonnet 4.5 pricing to tokenStore.ts"
   ```
   
   Watch for:
   - ✅ Single call to `get_working_directory`
   - ✅ Code changes made
   - ✅ `answer` tool called with summary
   - ✅ NO restart, NO re-reading files

## Prevention Guidelines

To prevent similar issues in future agent definitions:

1. **Always include explicit termination conditions** when agents use tools that complete workflows
2. **Avoid "always" language** in instructions (e.g., "Always start by...")
3. **Use "at the start" or "initially"** instead of "always"
4. **Add CRITICAL termination instructions** after tool completion
5. **Test agents with maxSteps > 1** to ensure they don't loop
6. **Monitor logs** for repeated identical tool calls

## Related Files

- `src/agents/BuiltInAgents.ts` - Agent definitions (FIXED)
- `src/agents/vercel/codeGeneration.ts` - CodeGeneration implementation
- `src/agents/helpers/planning.ts` - Orchestration delegation logic

## Additional Notes

- Both fixes preserve retry capability on errors
- `maxSteps` settings remain appropriate for legitimate retries
- Fixes do not affect other agents or orchestration flows
- The `answer` tool in CodeGenerationAgent is the natural completion signal

## Combined with Intelligent Routing Fix

These loop prevention fixes work together with the intelligent agent routing fix (see `INTELLIGENT_AGENT_ROUTING_FIX.md`) to ensure:

1. ✅ Orchestration completes without looping
2. ✅ Each step is routed to the correct specialized agent
3. ✅ Agents complete their work and finish cleanly
4. ✅ No unnecessary restarts or repeated work

