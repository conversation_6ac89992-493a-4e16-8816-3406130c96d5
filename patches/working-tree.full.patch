diff --git a/data/items.json b/data/items.json
new file mode 100644
index 0000000..ccc4575
--- /dev/null
+++ b/data/items.json
@@ -0,0 +1,2347 @@
+[
+  {
+    "id": "tag:github.com,2008:Repository/31792824/3.37.0-0.1.pre",
+    "title": "3.37.0-0.1.pre",
+    "url": "https://github.com/flutter/flutter/releases/tag/3.37.0-0.1.pre",
+    "source": "flutter_releases",
+    "section": "Releases",
+    "publishedAt": "2025-09-16T18:55:17.000Z",
+    "summary": "engine version update for 3.37.0-0.1 (#175445)"
+  },
+  {
+    "id": "tag:github.com,2008:Repository/31792824/3.35.4",
+    "title": "3.35.4: engine version update (#175422)",
+    "url": "https://github.com/flutter/flutter/releases/tag/3.35.4",
+    "source": "flutter_releases",
+    "section": "Releases",
+    "publishedAt": "2025-09-16T14:27:41.000Z",
+    "summary": "https://ci.chromium.org/ui/p/dart-internal/builders/flutter/Linux%20flutter_release/168/overview\nRelease engine version check failed."
+  },
+  {
+    "id": "tag:github.com,2008:Repository/31792824/3.37.0-0.0.pre",
+    "title": "3.37.0-0.0.pre",
+    "url": "https://github.com/flutter/flutter/releases/tag/3.37.0-0.0.pre",
+    "source": "flutter_releases",
+    "section": "Releases",
+    "publishedAt": "2025-09-08T18:47:19.000Z",
+    "summary": "Bump engine version for Flutter 3.37 (#175081)"
+  },
+  {
+    "id": "tag:github.com,2008:Repository/31792824/3.35.3",
+    "title": "3.35.3",
+    "url": "https://github.com/flutter/flutter/releases/tag/3.35.3",
+    "source": "flutter_releases",
+    "section": "Releases",
+    "publishedAt": "2025-09-03T21:54:31.000Z",
+    "summary": "Update engine version for Flutter 3.35.3 stable hotfix. (#174901)"
+  },
+  {
+    "id": "tag:github.com,2008:Repository/31792824/3.36.0-0.4.pre",
+    "title": "3.36.0-0.4.pre: [3.36 CP] Add a 'bad' commit to the beta, to be reverted. (#174658)",
+    "url": "https://github.com/flutter/flutter/releases/tag/3.36.0-0.4.pre",
+    "source": "flutter_releases",
+    "section": "Releases",
+    "publishedAt": "2025-08-28T18:29:08.000Z",
+    "summary": "This is innocuous, but we can download it from the SDK archive to ensure the file exists/does not exist.\nTowards #172011."
+  },
+  {
+    "id": "tag:github.com,2008:Repository/31792824/3.36.0-0.5.pre",
+    "title": "3.36.0-0.5.pre: [3.36] CP #174459 (Fix bug in test_golden_comparator) (#174603)",
+    "url": "https://github.com/flutter/flutter/releases/tag/3.36.0-0.5.pre",
+    "source": "flutter_releases",
+    "section": "Releases",
+    "publishedAt": "2025-08-27T23:02:04.000Z",
+    "summary": "Manual CP for #174459."
+  },
+  {
+    "id": "tag:github.com,2008:Repository/31792824/3.36.0-0.3.pre",
+    "title": "3.36.0-0.3.pre: [3.36] CP #174459 (Fix bug in test_golden_comparator) (#174603)",
+    "url": "https://github.com/flutter/flutter/releases/tag/3.36.0-0.3.pre",
+    "source": "flutter_releases",
+    "section": "Releases",
+    "publishedAt": "2025-08-27T23:02:04.000Z",
+    "summary": "Manual CP for #174459."
+  },
+  {
+    "id": "tag:github.com,2008:Repository/31792824/3.36.0-0.2.pre",
+    "title": "3.36.0-0.2.pre: Prepare hotfixes for `3.36.X` (#174380)",
+    "url": "https://github.com/flutter/flutter/releases/tag/3.36.0-0.2.pre",
+    "source": "flutter_releases",
+    "section": "Releases",
+    "publishedAt": "2025-08-25T17:21:35.000Z",
+    "summary": "... just an engine.version is needed (today)."
+  },
+  {
+    "id": "tag:github.com,2008:Repository/31792824/3.35.2",
+    "title": "Prepare to publish `3.35.2` (#174377)",
+    "url": "https://github.com/flutter/flutter/releases/tag/3.35.2",
+    "source": "flutter_releases",
+    "section": "Releases",
+    "publishedAt": "2025-08-25T17:21:35.000Z",
+    "summary": "... more CHANGELOG.md updates, another bump to engine.revision."
+  },
+  {
+    "id": "tag:github.com,2008:Repository/31792824/3.36.0-0.1.pre",
+    "title": "3.36.0-0.1.pre: [3.36] Create `engine.version` (#173749)",
+    "url": "https://github.com/flutter/flutter/releases/tag/3.36.0-0.1.pre",
+    "source": "flutter_releases",
+    "section": "Releases",
+    "publishedAt": "2025-08-14T00:21:27.000Z",
+    "summary": "Pointing at 877970c."
+  },
+  {
+    "id": "tag:github.com,2008:Repository/35726310/3.10.0-254.0.dev",
+    "title": "3.10.0-254.0.dev",
+    "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-254.0.dev",
+    "source": "dart_releases",
+    "section": "Releases",
+    "publishedAt": "2025-09-28T00:03:06.000Z",
+    "summary": "3.10.0-254.0.dev"
+  },
+  {
+    "id": "tag:github.com,2008:Repository/35726310/3.10.0-253.0.dev",
+    "title": "3.10.0-253.0.dev",
+    "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-253.0.dev",
+    "source": "dart_releases",
+    "section": "Releases",
+    "publishedAt": "2025-09-27T16:03:16.000Z",
+    "summary": "3.10.0-253.0.dev"
+  },
+  {
+    "id": "tag:github.com,2008:Repository/35726310/3.10.0-252.0.dev",
+    "title": "3.10.0-252.0.dev",
+    "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-252.0.dev",
+    "source": "dart_releases",
+    "section": "Releases",
+    "publishedAt": "2025-09-27T00:02:59.000Z",
+    "summary": "3.10.0-252.0.dev"
+  },
+  {
+    "id": "tag:github.com,2008:Repository/35726310/3.10.0-251.0.dev",
+    "title": "3.10.0-251.0.dev",
+    "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-251.0.dev",
+    "source": "dart_releases",
+    "section": "Releases",
+    "publishedAt": "2025-09-26T20:09:44.000Z",
+    "summary": "3.10.0-251.0.dev"
+  },
+  {
+    "id": "tag:github.com,2008:Repository/35726310/3.10.0-250.0.dev",
+    "title": "3.10.0-250.0.dev",
+    "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-250.0.dev",
+    "source": "dart_releases",
+    "section": "Releases",
+    "publishedAt": "2025-09-26T16:04:24.000Z",
+    "summary": "3.10.0-250.0.dev"
+  },
+  {
+    "id": "tag:github.com,2008:Repository/35726310/3.10.0-249.0.dev",
+    "title": "3.10.0-249.0.dev",
+    "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-249.0.dev",
+    "source": "dart_releases",
+    "section": "Releases",
+    "publishedAt": "2025-09-26T12:03:09.000Z",
+    "summary": "3.10.0-249.0.dev"
+  },
+  {
+    "id": "tag:github.com,2008:Repository/35726310/3.10.0-248.0.dev",
+    "title": "3.10.0-248.0.dev",
+    "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-248.0.dev",
+    "source": "dart_releases",
+    "section": "Releases",
+    "publishedAt": "2025-09-26T00:03:21.000Z",
+    "summary": "3.10.0-248.0.dev"
+  },
+  {
+    "id": "tag:github.com,2008:Repository/35726310/3.10.0-247.0.dev",
+    "title": "3.10.0-247.0.dev",
+    "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-247.0.dev",
+    "source": "dart_releases",
+    "section": "Releases",
+    "publishedAt": "2025-09-25T20:09:08.000Z",
+    "summary": "3.10.0-247.0.dev"
+  },
+  {
+    "id": "tag:github.com,2008:Repository/35726310/3.10.0-246.0.dev",
+    "title": "3.10.0-246.0.dev",
+    "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-246.0.dev",
+    "source": "dart_releases",
+    "section": "Releases",
+    "publishedAt": "2025-09-25T16:03:25.000Z",
+    "summary": "3.10.0-246.0.dev"
+  },
+  {
+    "id": "tag:github.com,2008:Repository/35726310/3.10.0-245.0.dev",
+    "title": "3.10.0-245.0.dev",
+    "url": "https://github.com/dart-lang/sdk/releases/tag/3.10.0-245.0.dev",
+    "source": "dart_releases",
+    "section": "Releases",
+    "publishedAt": "2025-09-25T12:09:25.000Z",
+    "summary": "3.10.0-245.0.dev"
+  },
+  {
+    "id": "urn:uuid:5a6669cf-ae24-4e56-bf1f-aad9045c0a0b",
+    "title": "v0.11.3 of flutter_gemma",
+    "url": "https://pub.dev/packages/flutter_gemma",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T17:09:57.384Z",
+    "summary": "The plugin allows running the Gemma AI model locally on a device from a Flutter application. Includes support for Gemma 3 Nano models with optimized MediaPipe GenAI v0.10.24.\n\nChangelog excerpt:\n- 🌐 **Web Multimodal Support**: Added full multimodal image processing support for web platform\n- 📚 **MediaPipe 0.10.25**: Updated to MediaPipe GenAI v0.10.25 for web compatibility\n- 📦 **LiterTLM Format Support**: Added support for `.litertlm`model files optimized for web platform"
+  },
+  {
+    "id": "urn:uuid:533340e1-0810-4c80-878f-4ee8efb517b3",
+    "title": "v1.9.1 of simple_ui",
+    "url": "https://pub.dev/packages/simple_ui",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T16:49:19.084Z",
+    "summary": "自定义简单 UI 组件"
+  },
+  {
+    "id": "urn:uuid:ae71cc12-194c-453b-bfaa-e8ea2dbc76fd",
+    "title": "v4.4.12 of flutter_slick",
+    "url": "https://pub.dev/packages/flutter_slick",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T16:48:35.489Z",
+    "summary": "Slick patterns, components, navigation and services for Flutter.\n\nChangelog excerpt:\n- Added loading indicator for while rewrite code runs"
+  },
+  {
+    "id": "urn:uuid:e694a80a-1229-4d80-bc42-483513ee116c",
+    "title": "v0.0.1 of puzzlify",
+    "url": "https://pub.dev/packages/puzzlify",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T16:41:46.641Z",
+    "summary": "A Flutter widget that turns any image into an interactive puzzle game.\n\nChangelog excerpt:\n### Added\n\n- Initial release of Puzzlify 🎉\n- Support for creating puzzles from local images (Uint8List).\n- Puzzle grid with customizable rows and columns.\n- Controller with `recreate`, `derange`, and `check`methods.\n- Basic shimmer loading state while image is being processed."
+  },
+  {
+    "id": "urn:uuid:3514ba47-58df-4d30-bdb2-9118dcee911b",
+    "title": "v4.4.11 of flutter_slick",
+    "url": "https://pub.dev/packages/flutter_slick",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T16:40:34.556Z",
+    "summary": "Slick patterns, components, navigation and services for Flutter.\n\nChangelog excerpt:\n- Added loading indicator for while rewrite code runs"
+  },
+  {
+    "id": "urn:uuid:66f70ac3-659e-4589-9820-e6a2f80d0a0d",
+    "title": "v0.0.9 of hivez",
+    "url": "https://pub.dev/packages/hivez",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T16:31:54.089Z",
+    "summary": "The cleanest way to use Hive in production. Type-safe, concurrency-safe, boilerplate-free. (Using hive_ce)\n\nChangelog excerpt:\n- Improved API structure, type safety and made unnecessary public members private\n- Improved logging performance by using a function builder instead of a string literal\n- Added basic logs to `initialize`, `flush`, `compact`, `deleteFromDisk`, and `closeBox`operations\n- Added extensive tests for backup extension methods for all box types testing both JSON and compressed backups and many more tests for all box types\n- Fixed missing exports for extension methods\n- To improve the auto-completion and[...]"
+  },
+  {
+    "id": "urn:uuid:afddb9f7-4adb-4507-a241-728c2db2f655",
+    "title": "v4.4.10 of flutter_slick",
+    "url": "https://pub.dev/packages/flutter_slick",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T16:29:16.575Z",
+    "summary": "Slick patterns, components, navigation and services for Flutter.\n\nChangelog excerpt:\n- Added loading indicator for while rewrite code runs"
+  },
+  {
+    "id": "urn:uuid:c14e30ff-ec86-46f6-b70f-f2c0f5b65198",
+    "title": "v0.1.1 of nexusevent_flutter",
+    "url": "https://pub.dev/packages/nexusevent_flutter",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T16:28:07.010Z",
+    "summary": "NexusEvent Flutter SDK - 跨平台消息推送SDK，支持Discord、Slack等多种平台\n\nChangelog excerpt:\n### Added\n\n- Initial Flutter SDK release\n- Discord webhook support with rich embed functionality\n- Slack webhook and Bot API integration\n- Telegram Bot API support\n- Cross-platform compatibility (iOS, Android, Web, Desktop)\n- Comprehensive error handling and validation\n- Asynchronous API design\n- Type-safe configuration classes\n\n### Features\n\n- `NexusEventClient`main client class\n- `DiscordWebhookConfig`for Discord webhook configuration\n- `SlackWebhookConfig`for Slack webhook configuration\n- `Te[...]"
+  },
+  {
+    "id": "urn:uuid:1bae1588-9cdc-49eb-8964-b40424d36204",
+    "title": "v1.9.0 of simple_ui",
+    "url": "https://pub.dev/packages/simple_ui",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T16:26:15.026Z",
+    "summary": "自定义简单 UI 组件"
+  },
+  {
+    "id": "urn:uuid:8072d625-9002-4927-b631-84aa1b92e02f",
+    "title": "v0.2.3 of flutter_bloc_form_plus",
+    "url": "https://pub.dev/packages/flutter_bloc_form_plus",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T16:25:57.629Z",
+    "summary": "Create Beautiful Forms in Flutter. The easiest way to Prefill, Async Validation, Update Form Fields, and Show Progress, Failures or Navigate by Reacting to the Form State.\n\nChangelog excerpt:\n### Added\n\n- **`showTitle`property**to `StepperFormBlocBuilder`   - Default: `true`(backwards compatible).\n  - Allows hiding stepper titles when set to `false`.\n \n\n## Adds flexibility for cleaner UIs (e.g., when using a progress bar or when step labels are unnecessary).\n\n### Example\n\n```\n`StepperFormBlocBuilder( formBloc: context.read(), type: StepperType.horizontal, showTitle: false, // hides step titles );````"
+  },
+  {
+    "id": "urn:uuid:203d52bd-8a84-4354-b9c1-b6676b48fca9",
+    "title": "v0.2.3 of bloc_form_plus",
+    "url": "https://pub.dev/packages/bloc_form_plus",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T16:25:03.725Z",
+    "summary": "Easy Form State Management using BLoC pattern. Separate the Form State and Business Logic from the User Interface. Async Validation, Progress, Dynamic fields, and more.\n\nChangelog excerpt:\n- **Minor fix of changelog arrangement**"
+  },
+  {
+    "id": "urn:uuid:2be3c35e-8943-45d5-b343-9e1b8136bec9",
+    "title": "v0.5.0 of openai_webrtc",
+    "url": "https://pub.dev/packages/openai_webrtc",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T15:59:03.079Z",
+    "summary": "webrtc support for openai_core and the OpenAI realtime api\n\nChangelog excerpt:\nUpdate to 0.8.0 openai_core"
+  },
+  {
+    "id": "urn:uuid:851b9c27-f79d-4b53-b484-82f0131b7828",
+    "title": "v0.8.0 of openai_core",
+    "url": "https://pub.dev/packages/openai_core",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T15:57:46.780Z",
+    "summary": "openai support for dart including the responses api, the realtime api, and more.\n\nChangelog excerpt:\nAdd support for realtime usage, add more realtime events"
+  },
+  {
+    "id": "urn:uuid:5483ec23-d6c9-432c-ace0-a6f0010e283c",
+    "title": "v0.4.0 of tailwindcss_build",
+    "url": "https://pub.dev/packages/tailwindcss_build",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T15:54:55.209Z",
+    "summary": "A comprehensive Flutter package that brings the power and convenience of Tailwind CSS utility classes to Flutter development."
+  },
+  {
+    "id": "urn:uuid:c8ec7a4b-64f7-4493-aa36-7533184214c7",
+    "title": "v0.7.0 of openai_core",
+    "url": "https://pub.dev/packages/openai_core",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T15:54:16.670Z",
+    "summary": "openai support for dart including the responses api, the realtime api, and more.\n\nChangelog excerpt:\nImprove support for GA Realtime API"
+  },
+  {
+    "id": "urn:uuid:89baf20d-2b06-4f3a-b19c-1e00224a4016",
+    "title": "v1.5.7 of taboola_sdk_beta",
+    "url": "https://pub.dev/packages/taboola_sdk_beta",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T15:48:04.888Z",
+    "summary": "Taboola's Flutter Core SDK. This SDK provides access to Taboola's Widget and Feed integrations for Flutter applications."
+  },
+  {
+    "id": "urn:uuid:82d91749-af08-47a0-9735-0350767c9077",
+    "title": "v1.0.0 of radar_chart_plus",
+    "url": "https://pub.dev/packages/radar_chart_plus",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T15:37:59.750Z",
+    "summary": "A versatile Flutter package for creating beautiful and customizable radar charts. Easily visualize multivariate data for comparisons and analysis.\n\nChangelog excerpt:\n- Fixed warnings and improved README\n- Added example folder\n- Added LICENSE file\n- Minor improvements for pub.dev scoring"
+  },
+  {
+    "id": "urn:uuid:63b1c25b-54c7-4678-99f3-96db45ac2e7b",
+    "title": "v1.1.2 of nui_database",
+    "url": "https://pub.dev/packages/nui_database",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T15:30:39.417Z",
+    "summary": "A new Flutter package."
+  },
+  {
+    "id": "urn:uuid:d8886021-9b37-4cae-a6cf-1a0c18e528c2",
+    "title": "v7.2.0 of squadron",
+    "url": "https://pub.dev/packages/squadron",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T15:24:35.257Z",
+    "summary": "Multithreading and worker thread pool for Dart / Flutter, to offload CPU-bound and heavy I/O tasks to Isolate or Web Worker threads.\n\nChangelog excerpt:\n- Support sending Dart `DateTime`/ JS `Date`instances to/from Workers.\n- Fix warnings from [pub.dev](https://pub.dev/packages/squadron/score)."
+  },
+  {
+    "id": "urn:uuid:2774a166-9bb2-41ac-8915-7abca5c6d738",
+    "title": "v0.0.1 of shelf_sse",
+    "url": "https://pub.dev/packages/shelf_sse",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T15:15:20.760Z",
+    "summary": "A shelf handler that wires up a listener for every connection.\n\nChangelog excerpt:\n- Introduce `sseHandler`for lifting Shelf requests into `SseChannel`s.\n- Provide a server/client example demonstrating bi-directional SSE.\n- Add unit tests covering server-to-client messaging and validation logic."
+  },
+  {
+    "id": "urn:uuid:62542638-941e-40af-8844-7e4b5047a88e",
+    "title": "v1.5.6 of taboola_sdk_beta",
+    "url": "https://pub.dev/packages/taboola_sdk_beta",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T15:13:22.824Z",
+    "summary": "Taboola's Flutter Core SDK. This SDK provides access to Taboola's Widget and Feed integrations for Flutter applications."
+  },
+  {
+    "id": "urn:uuid:f0e767c5-702f-425a-8ae8-73f83b0fffb5",
+    "title": "v1.0.1 of nui_error_handler",
+    "url": "https://pub.dev/packages/nui_error_handler",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T15:11:32.472Z",
+    "summary": "A new Flutter package for error handling"
+  },
+  {
+    "id": "urn:uuid:da96feea-a140-4379-85c2-6efa2a9df23a",
+    "title": "v2.1.2 of ecache",
+    "url": "https://pub.dev/packages/ecache",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T15:06:45.470Z",
+    "summary": "Provide a dart cache library usable on web, flutter and server side\n\n\nChangelog excerpt:\n- Fix: prevent exception if cache is disposed() and async entry finishes afterwards"
+  },
+  {
+    "id": "urn:uuid:5ff9fe52-7b81-4e89-9757-12c43363db77",
+    "title": "v1.3.2 of ai_providers",
+    "url": "https://pub.dev/packages/ai_providers",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T15:00:49.985Z",
+    "summary": "Unified AI provider integration for Flutter - OpenAI, Google AI, xAI, Android native TTS with consistent API.\n\nChangelog excerpt:\n### 🔧 Mejoras en API\n\n- **`AiAudioParams.audioFormat`con valor por defecto**: Ahora es `String`(no nullable) con valor por defecto `'pcm'`\n- **Uso simplificado**: `AiAudioParams()`sin parámetros funciona perfectamente para casos comunes\n- **PCM universal**: Formato recomendado compatible con todos los proveedores\n\n### ⚡ Breaking Changes Menores\n\n- `audioFormat`cambió de `String?`a `String`con valor por defecto `'pcm'`\n- Eliminadas verificaciones de null innecesarias en providers\n\n### 📚 Documen[...]"
+  },
+  {
+    "id": "urn:uuid:db2829ef-eaef-444c-9e5d-086f67457941",
+    "title": "v0.0.1+1-alpha of fdevs_pops",
+    "url": "https://pub.dev/packages/fdevs_pops",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T14:57:32.072Z",
+    "summary": "A powerful and flexible Flutter package for managing overlays such as dialogs, snackbars, and custom overlays with unique identifiers."
+  },
+  {
+    "id": "urn:uuid:eb5bff8b-18b3-428c-aa97-408274ff7e2a",
+    "title": "v1.0.1 of nui_map",
+    "url": "https://pub.dev/packages/nui_map",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T14:54:47.850Z",
+    "summary": "A new Flutter package for Google Map"
+  },
+  {
+    "id": "urn:uuid:fe99f613-5c4c-42e9-9fc1-f24ca4bf7413",
+    "title": "v0.0.15 of openvidu_flutter",
+    "url": "https://pub.dev/packages/openvidu_flutter",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T14:50:13.784Z",
+    "summary": "Migration from openvidu-android to Flutter. The package contains a working example that targets OpenVidu 2.29.0.\n\nChangelog excerpt:\n### Fixed\n\n- Fixed API design issues in request_config.dart by making private types public\n- Removed unreachable switch default case in Config._getMethod()\n- Fixed duplicate @override annotations in PlainResponse class\n- Resolved all library_private_types_in_public_api warnings\n- Improved code quality and adherence to Dart best practices"
+  },
+  {
+    "id": "urn:uuid:cbd9e9c6-84c7-4905-a3c0-45ad53732d49",
+    "title": "v1.0.2 of iversioning",
+    "url": "https://pub.dev/packages/iversioning",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T14:47:22.379Z",
+    "summary": "A new Flutter package."
+  },
+  {
+    "id": "urn:uuid:9d51150e-f3c7-4e5d-872c-341c07842aba",
+    "title": "v1.3.1 of ai_providers",
+    "url": "https://pub.dev/packages/ai_providers",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T14:43:05.096Z",
+    "summary": "Unified AI provider integration for Flutter - OpenAI, Google AI, xAI, Android native TTS with consistent API.\n\nChangelog excerpt:\n### 🧹 Simplificación y Mejoras\n\n- **Eliminado `TranscribeInstructions`**: Simplificamos la arquitectura de audio eliminando la clase `TranscribeInstructions`que tenía características no utilizadas (anti-hallucination). Ahora `AI.listen()`usa directamente `AISystemPrompt`.\n- **`AiAudioParams`clarificado**: La documentación ahora especifica claramente que `AiAudioParams`es exclusivamente para **síntesis de voz (TTS)**con `AI.speak()`, no para transcripción.\n- **Demo actualizado**: El ejemplo `aud[...]"
+  },
+  {
+    "id": "urn:uuid:ae3da23c-85fb-463b-959a-7f80945a0ed0",
+    "title": "v1.3.0 of enroll_plugin",
+    "url": "https://pub.dev/packages/enroll_plugin",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T14:37:54.381Z",
+    "summary": "eNROLL is a compliance solution that prevents identity fraud and phishing. Powered by AI, it reduces errors and speeds up identification, ensuring secure verification.\n\nChangelog excerpt:\n- Updating Innovatrics to be 8.11.0."
+  },
+  {
+    "id": "urn:uuid:4f189a97-c825-46c6-8059-a7b047098ecd",
+    "title": "v0.6.1 of pars_validator",
+    "url": "https://pub.dev/packages/pars_validator",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T14:37:07.412Z",
+    "summary": "A lightweight Flutter library for validating Persian-specific data formats like national codes,bank card numbers, phone numbers, Shamsi dates, and more.\n\nChangelog excerpt:\nString options improvements\n\n- Added `General.isNumeric('123')`and `'123'.isNumeric()`utility:   - Supports English, Persian and Arabic-Indic digits.\n  - Can validate both integers and decimal numbers (using `.`, `,`, or `٫`as decimal separator)."
+  },
+  {
+    "id": "urn:uuid:34d26d14-a40d-49a8-bea5-3f2ce3f486b2",
+    "title": "v2.0.2 of signals_async",
+    "url": "https://pub.dev/packages/signals_async",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T14:09:07.005Z",
+    "summary": "A reactive asynchronous signal library that extends the signals package with ComputedFuture and ComputedStream for handling async operations and streams reactively.\n\nChangelog excerpt:\n- Skip loading state on chained initial values"
+  },
+  {
+    "id": "urn:uuid:3bd02f74-5367-4fb4-993d-dc6c4d93937d",
+    "title": "v1.5.5 of taboola_sdk_beta",
+    "url": "https://pub.dev/packages/taboola_sdk_beta",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:56:49.555Z",
+    "summary": "Taboola's Flutter Core SDK. This SDK provides access to Taboola's Widget and Feed integrations for Flutter applications."
+  },
+  {
+    "id": "urn:uuid:bddae768-6580-40a5-a0d6-a5989fc86279",
+    "title": "v0.0.1 of sketchura_ui",
+    "url": "https://pub.dev/packages/sketchura_ui",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:53:44.799Z",
+    "summary": "Sketchura UI is a Flutter UI builder package that helps you reduce boilerplate code and build modern interfaces faster. It provides simple, reusable, and customizable components to speed up your devel [...]\n\nChangelog excerpt:\n- Initial release of Sketchura UI.\n- Added CustomButton widget."
+  },
+  {
+    "id": "urn:uuid:d45efa16-48a5-46e1-9c55-63b07b25436f",
+    "title": "v0.0.1 of sketchura",
+    "url": "https://pub.dev/packages/sketchura",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:48:14.304Z",
+    "summary": "Sketchura UI is a Flutter UI builder package that helps you reduce boilerplate code and build modern interfaces faster. It provides simple, reusable, and customizable components to speed up your devel [...]\n\nChangelog excerpt:\n- Initial release of Sketchura UI.\n- Added CustomButton widget."
+  },
+  {
+    "id": "urn:uuid:e82f6ac8-1395-4965-811c-13bfb1ce180f",
+    "title": "v1.0.6 of linklytics_flutter",
+    "url": "https://pub.dev/packages/linklytics_flutter",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:41:40.726Z",
+    "summary": "A Flutter plugin for integrating Linklytics analytics into your Flutter applications."
+  },
+  {
+    "id": "urn:uuid:16a643f6-c908-4c21-b0f8-2fe6bbb9213e",
+    "title": "v0.0.13 of todo_flutter",
+    "url": "https://pub.dev/packages/todo_flutter",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:38:15.745Z",
+    "summary": "Flutter project development scaffold\n\nChangelog excerpt:\n- fix BaseRequest bugs, when params = {}"
+  },
+  {
+    "id": "urn:uuid:257c2a47-26a1-4236-955a-d794cc26c4e2",
+    "title": "v0.3.0 of enhanced_preferences",
+    "url": "https://pub.dev/packages/enhanced_preferences",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:37:47.240Z",
+    "summary": "Wraps platform-specific persistent storage for simple data.\n\nChangelog excerpt:\n- feat: Implements `keys()`.\n- fix(darwin): Support CocoaPods.\n- ci: Improve tests.\n- ci: Add GitHub Actions.\n- chore: Bump flutter_lints.\n- chore: Remove unused import.\n- doc: Add badges.\n- doc(darwin): Add macOS.\n- doc(darwin): Update podspec document."
+  },
+  {
+    "id": "urn:uuid:f44a702c-9285-4e33-9277-504bdc797928",
+    "title": "v0.0.2 of one_firebase",
+    "url": "https://pub.dev/packages/one_firebase",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:36:02.189Z",
+    "summary": "A comprehensive Firebase service wrapper for Flutter with Riverpod integration, providing type-safe CRUD operations, batch updates, pagination, and real-time data streaming for Firestore."
+  },
+  {
+    "id": "urn:uuid:f2c05469-f05e-43db-9481-7644af0cf00b",
+    "title": "v2.0.2 of amount_to_word",
+    "url": "https://pub.dev/packages/amount_to_word",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:32:13.191Z",
+    "summary": "A comprehensive Flutter package for converting numbers and amounts to words in multiple languages (Persian, English, Turkish) with full currency support and smart pluralization.\n\nChangelog excerpt:\n### Fixed\n\n- **Turkish Currency**: Fixed \"kurus\" to \"kuruş\" in Turkish Lira sub-unit name\n- **App Name**: Updated partner app name to \"Fida Invoice\" in English"
+  },
+  {
+    "id": "urn:uuid:248dd9d4-**************-11d97eaa3905",
+    "title": "v0.0.1 of linklytics_feedback",
+    "url": "https://pub.dev/packages/linklytics_feedback",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:29:25.031Z",
+    "summary": "A Flutter package for collecting and managing user feedback with built-in analytics and reporting capabilities.\n\nChangelog excerpt:\n### Added\n\n- Initial release of Linklytics Feedback package\n- Core feedback collection widgets\n- Basic analytics integration\n- Customizable feedback forms\n- Support for multiple feedback types (ratings, comments, suggestions)\n- Real-time feedback reporting capabilities\n- User-friendly interface components\n\n### Features\n\n- Easy integration with existing Flutter applications\n- Lightweight and performant feedback collection\n- Built-in analytics for feedback tracking\n- Customizable UI components to [...]"
+  },
+  {
+    "id": "urn:uuid:f564219d-1bbc-4066-9593-24c90fc8942c",
+    "title": "v0.0.1 of one_firebase",
+    "url": "https://pub.dev/packages/one_firebase",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:28:07.305Z",
+    "summary": "A comprehensive Firebase service wrapper for Flutter with Riverpod integration, providing type-safe CRUD operations, batch updates, pagination, and real-time data streaming for Firestore.\n\nChangelog excerpt:\n- TODO: Describe initial release."
+  },
+  {
+    "id": "urn:uuid:33b69ccd-431c-4f23-b487-09b2eb3e825a",
+    "title": "v0.0.1 of linklytics_apps",
+    "url": "https://pub.dev/packages/linklytics_apps",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:25:54.505Z",
+    "summary": "Package to get User apps from Linklytics\n\nChangelog excerpt:\n### Added\n\n- Initial release of Linklytics Apps package\n- Core `LinklyticsApps`class for managing app listings\n- `App`model with support for Google Play Store and App Store URLs\n- `StoreData`model for handling app store analytics data\n- `AppsList`widget for displaying apps in a list format\n- `AppItem`widget for individual app display\n- `ApiService`for handling API communications\n- `AppListController`for state management using GetX\n- Customizable `AppListStrings`for UI text localization\n- Platfor[...]"
+  },
+  {
+    "id": "urn:uuid:dbd2fd5b-b0d0-45fb-84df-aa977291f37f",
+    "title": "v0.0.14 of openvidu_flutter",
+    "url": "https://pub.dev/packages/openvidu_flutter",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:23:44.511Z",
+    "summary": "Migration from openvidu-android to Flutter. The package contains a working example that targets OpenVidu 2.29.0.\n\nChangelog excerpt:\n### Fixed\n\n- Added missing type annotations across all source files\n- Fixed missing return types for methods in RemoteParticipant, CustomWebSocket, and ApiClient\n- Improved type safety with explicit variable declarations\n- Enhanced code maintainability and IDE support"
+  },
+  {
+    "id": "urn:uuid:353d6c93-d82f-407b-9cce-3df3acbac013",
+    "title": "v1.0.3 of change_project_name",
+    "url": "https://pub.dev/packages/change_project_name",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:17:06.484Z",
+    "summary": "A CLI tool to rename Flutter/Dart projects and update all package references automatically"
+  },
+  {
+    "id": "urn:uuid:15628523-4eb2-40c9-99e0-5e4b65988563",
+    "title": "v0.0.62 of juggle_im",
+    "url": "https://pub.dev/packages/juggle_im",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:16:57.609Z",
+    "summary": "Juggle IM SDK"
+  },
+  {
+    "id": "urn:uuid:fcf6862e-**************-8a2890b4962b",
+    "title": "v2.0.1 of amount_to_word",
+    "url": "https://pub.dev/packages/amount_to_word",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:15:26.240Z",
+    "summary": "A comprehensive Flutter package for converting numbers and amounts to words in multiple languages (Persian, English, Turkish) with full currency support and smart pluralization.\n\nChangelog excerpt:\n### Added\n\n- **Support Section**: Added support section for فاکتور فیدا app in README\n- **Partnership**: Official partnership with فاکتور فیدا - professional invoice application"
+  },
+  {
+    "id": "urn:uuid:70551625-bbf4-4eae-af5f-8ca1979e2c39",
+    "title": "v1.1.0 of loredart_tensor",
+    "url": "https://pub.dev/packages/loredart_tensor",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T13:05:53.434Z",
+    "summary": "A Dart-pure package for manipulation with tensors (multidimensional arrays of data) inspired by the TensorFlow API.\n\nChangelog excerpt:\n- Update dependencies to Dart version 3"
+  },
+  {
+    "id": "urn:uuid:b73b0ccb-7813-4d9c-aac5-e4f3cefff6e6",
+    "title": "v2.0.0 of amount_to_word",
+    "url": "https://pub.dev/packages/amount_to_word",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T12:52:32.278Z",
+    "summary": "A comprehensive Flutter package for converting numbers and amounts to words in multiple languages (Persian, English, Turkish) with full currency support and smart pluralization.\n\nChangelog excerpt:\n### Added\n\n- **Turkish Language Support**: Complete number-to-word conversion for Turkish language\n- **Multi-language Currency Units**: Currency units now support Persian, English, and Turkish\n- **Smart Pluralization**: Automatic singular/plural handling for English currencies\n- **Extensible Architecture**: Easy to add new languages via `addLanguage()`method\n- **Comprehensive Currency Support**: 7 predefined currencies with proper localization\n- **Enhanced Testing**: 100% test coverage with edge[...]"
+  },
+  {
+    "id": "urn:uuid:4f2c3ea8-a481-49fd-a029-1cba5d7f3423",
+    "title": "v0.0.13 of openvidu_flutter",
+    "url": "https://pub.dev/packages/openvidu_flutter",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T12:45:27.627Z",
+    "summary": "Migration from openvidu-android to Flutter. The package contains a working example that targets OpenVidu 2.29.0.\n\nChangelog excerpt:\n### Changed\n\n- Updated Android Gradle Plugin from 7.3.0 → 8.6.1\n- Updated Kotlin version from 1.7.10 → 2.1.0\n- Updated Gradle version from 7.6.3 → 8.7\n- Updated compileSdk from flutter.compileSdkVersion → 36\n- Updated targetSdkVersion from flutter.targetSdkVersion → 36\n- Enhanced JVM arguments with better memory management\n- Added compatibility flags for Android builds\n- Enabled Gradle caching and parallel builds for better performance\n\n### Fixed\n\n- Resolved Kotlin compilation errors in Flutter [...]"
+  },
+  {
+    "id": "urn:uuid:4eb59e32-4466-40ed-b797-fe8625f24b9a",
+    "title": "v1.5.4 of taboola_sdk_beta",
+    "url": "https://pub.dev/packages/taboola_sdk_beta",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T12:33:23.536Z",
+    "summary": "Taboola's Flutter Core SDK. This SDK provides access to Taboola's Widget and Feed integrations for Flutter applications."
+  },
+  {
+    "id": "urn:uuid:c0502d27-2a70-4511-bb92-a9263ee3ca4c",
+    "title": "v0.0.2 of responsive_scaler",
+    "url": "https://pub.dev/packages/responsive_scaler",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T12:16:01.975Z",
+    "summary": "A Flutter package that offers automatic, boilerplate-free responsive scaling for text, icons, and spacing across different screen sizes.\n\nChangelog excerpt:\n- Updated and improved documentation\n- Migrated to flutter_lints 6.0.0 for better linting support"
+  },
+  {
+    "id": "urn:uuid:8f7459d2-a572-46dd-85ac-c512fd43dd0d",
+    "title": "v7.0.3 of biometric_signature",
+    "url": "https://pub.dev/packages/biometric_signature",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T12:12:02.605Z",
+    "summary": "Flutter biometric functionality for cryptographic signing and encryption using the Secure Enclave and the StrongBox\n\nChangelog excerpt:\n- Updating documentations.\n- Minor bug fixes."
+  },
+  {
+    "id": "urn:uuid:b9f35eba-4e94-4ebb-82b6-e6a6928118ea",
+    "title": "v6.2.3+1 of dart_test_tools",
+    "url": "https://pub.dev/packages/dart_test_tools",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T12:07:51.193Z",
+    "summary": "Additional tools and helpers for writing dart unit tests and GitHub Actions Workflows.\n\nChangelog excerpt:\n### Changed\n\n- Allow build 4.0.0"
+  },
+  {
+    "id": "urn:uuid:105945e4-5edd-4f4f-a5ce-7916958aa93d",
+    "title": "v0.0.1 of responsive_scaler",
+    "url": "https://pub.dev/packages/responsive_scaler",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T12:04:52.079Z",
+    "summary": "A Flutter package that offers automatic, boilerplate-free responsive scaling for text, icons, and spacing across different screen sizes."
+  },
+  {
+    "id": "urn:uuid:27e73082-6494-4715-a543-f95909085b04",
+    "title": "v1.1.1 of nigeria_geo_sdk",
+    "url": "https://pub.dev/packages/nigeria_geo_sdk",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T12:00:39.675Z",
+    "summary": "High-performance Flutter SDK for comprehensive Nigerian geographic data including states, LGAs, wards, and postal codes. Zero configuration required.\n\nChangelog excerpt:\n### Fixed\n\n- **CRITICAL**: Removed unnecessary `assets/config.yaml`requirement from pubspec.yaml\n- Fixed iOS build error: \"No file or variants found for asset: packages/nigeria_geo_sdk/assets/config.yaml\"\n- SDK now works completely without any configuration files (as intended)\n- All functionality remains the same - SDK uses built-in optimized configuration\n- Users who were getting build errors should update to this version immediately"
+  },
+  {
+    "id": "urn:uuid:********-9eb9-44d0-875f-1db8970d748f",
+    "title": "v0.0.1+2 of fast_app_base",
+    "url": "https://pub.dev/packages/fast_app_base",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T11:58:38.549Z",
+    "summary": "Kickstart your Flutter app with fast_app_base — a ready-to-use foundation offering theme, routing, internationalization, dialogs, and Snackbar support out of the box.\n\nChangelog excerpt:\nTemplate bug fix"
+  },
+  {
+    "id": "urn:uuid:42605b54-c490-4b4f-b26e-7659d828bc09",
+    "title": "v1.0.2 of flutter_text_parser",
+    "url": "https://pub.dev/packages/flutter_text_parser",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T11:51:12.500Z",
+    "summary": "flutter_text_parser is a Dart library that allows you to parse any string with custom tags and render it as styled spanned text. It supports inline formatting like bold, italic, colors, and more, maki [...]\n\nChangelog excerpt:\n- Initial release."
+  },
+  {
+    "id": "urn:uuid:97953e6e-d697-45cf-9940-e533c5a8f590",
+    "title": "v1.5.3 of taboola_sdk_beta",
+    "url": "https://pub.dev/packages/taboola_sdk_beta",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T11:39:04.094Z",
+    "summary": "Taboola's Flutter Core SDK. This SDK provides access to Taboola's Widget and Feed integrations for Flutter applications."
+  },
+  {
+    "id": "urn:uuid:06ee491d-5e57-4662-a996-89378f95997e",
+    "title": "v2.0.24 of in_app_purchaser",
+    "url": "https://pub.dev/packages/in_app_purchaser",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T11:25:31.790Z",
+    "summary": "It simplifies the integration of in-app purchase services, handles subscription lifecycle events, provides revenue analytics, and ensures a smooth user experience across iOS and Android.\n\nChangelog excerpt:\n- Add localized contents [price]\n- Filter unnecessary price zeros [ex. 1.304 => 1.3, 4.00 => 4, 54.4783 => 54.48]\n- Rename currency reference name [{CURRENCY_NAME} => {CURRENCY_CODE}, {CURRENCY_SIGN} => {CURRENCY_SYMBOL}]\n- Improved Restore purchase status [restoredAvailable => exist, restoredUnavailable => empty]"
+  },
+  {
+    "id": "urn:uuid:e613dc80-713c-4f0d-b1fd-ddace9665403",
+    "title": "v1.5.2 of taboola_sdk_beta",
+    "url": "https://pub.dev/packages/taboola_sdk_beta",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T11:22:41.755Z",
+    "summary": "Taboola's Flutter Core SDK. This SDK provides access to Taboola's Widget and Feed integrations for Flutter applications."
+  },
+  {
+    "id": "urn:uuid:d330c90e-8d6a-40e0-8f09-e0f0b462e84f",
+    "title": "v0.0.6 of king_helper",
+    "url": "https://pub.dev/packages/king_helper",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T11:15:00.855Z",
+    "summary": "A Flutter package for helper methods and widgets\n\nChangelog excerpt:\n- Dependencies updated."
+  },
+  {
+    "id": "urn:uuid:bd3952d8-dcab-47df-882a-801679b1892d",
+    "title": "v0.0.54 of king_cache",
+    "url": "https://pub.dev/packages/king_cache",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T11:13:09.546Z",
+    "summary": "A Flutter package for caching api data and markdown content to disk and improving app performance. It uses the http package to make api calls and use cache directory to cache data with specialized sup [...]\n\nChangelog excerpt:\n- Fixed the issues with web\n- Renamed Couple of Functions\n- Updated Example app to have all those\n- if you find any issues, please create an issue on github and we will fix it as soon as possible"
+  },
+  {
+    "id": "urn:uuid:19ba624e-6c54-48e2-82df-463fb529b19e",
+    "title": "v0.1.15 of alghwalbi_core_app",
+    "url": "https://pub.dev/packages/alghwalbi_core_app",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T11:12:43.521Z",
+    "summary": "alghwalbi_core_app package is primarily intended for personal use, but feel free to use it in your projects."
+  },
+  {
+    "id": "urn:uuid:98bd29de-5d13-4db6-8658-0f7f569b6609",
+    "title": "v1.1.4 of auro_stream_live",
+    "url": "https://pub.dev/packages/auro_stream_live",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T11:07:03.072Z",
+    "summary": "Auro Stream Live Stream Plugin for iOS/Android/Desktop"
+  },
+  {
+    "id": "urn:uuid:a1044dd9-4236-46d2-ab04-103ec9960112",
+    "title": "v0.0.8 of hivez",
+    "url": "https://pub.dev/packages/hivez",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T10:52:55.980Z",
+    "summary": "The cleanest way to use Hive in production. Type-safe, concurrency-safe, boilerplate-free. (Using hive_ce)\n\nChangelog excerpt:\n- Improved performance by removing unnecessary checks and validation while making the package even more type safe and flexible\n- Added search extension methods for all box types, and added extensive tests with all box types   - `search`for searching the box for values that match the search query. It supports pagination, sorting and improved search with multiple search terms.\n \n- Fixed casting issues with isolated boxes"
+  },
+  {
+    "id": "urn:uuid:a730755a-ea76-4513-908c-2caa1d4ca1eb",
+    "title": "v1.0.0 of country_code_max",
+    "url": "https://pub.dev/packages/country_code_max",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T10:35:03.173Z",
+    "summary": "A beautiful, animated, and theme-aware country code picker for Flutter that works seamlessly across web and mobile platforms.\n\nChangelog excerpt:\n### Added\n\n- Initial release of Country Code Max\n- Beautiful animated country code picker component\n- Theme-aware design that adapts to light and dark themes\n- Cross-platform support for web and mobile\n- Smart search functionality by country name, code, or dial code\n- Favorites system for frequently used countries\n- Optional country flags display\n- Form validation support with error states\n- Responsive design for different screen sizes\n- Smooth animations and micro-interactions\n- Comprehensive d[...]"
+  },
+  {
+    "id": "urn:uuid:4fb5aa78-8a95-41c9-a417-1c6e89dad614",
+    "title": "v5.0.3 of flutter_yfree",
+    "url": "https://pub.dev/packages/flutter_yfree",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T10:26:38.443Z",
+    "summary": "YFree_Flutter Lite Version, Lightweight framework for Flutter development"
+  },
+  {
+    "id": "urn:uuid:d4597b3e-6a7c-4e90-b427-94f69fd718a1",
+    "title": "v0.1.9 of dep_audit",
+    "url": "https://pub.dev/packages/dep_audit",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T10:25:59.064Z",
+    "summary": "A Dart and Flutter dependency audit tool that identifies outdated packages and security vulnerabilities.\n\nChangelog excerpt:\n### Fixed\n\n- Shortened package description to comply with pub.dev guidelines and improve pub points score\n- Package description now under 180 characters for better readability"
+  },
+  {
+    "id": "urn:uuid:1d1d4bed-3058-4f97-995d-37689acfabf1",
+    "title": "v2.0.12+3 of cyber_req",
+    "url": "https://pub.dev/packages/cyber_req",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-30T19:49:49.169Z",
+    "summary": "A flexible Flutter/Dart API client for backends with dynamic headers, secure token management, and comprehensive callbacks."
+  },
+  {
+    "id": "urn:uuid:5e3bd4c4-a1f6-4e09-b43f-73a56ac93d60",
+    "title": "v0.9.20 of rdf_core",
+    "url": "https://pub.dev/packages/rdf_core",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T10:17:52.892Z",
+    "summary": "A type-safe, modular Dart library for modeling, encoding, and decoding RDF data.\n\nChangelog excerpt:\n### Changed\n\n- **N-Quads Encoder API**: Removed unused `baseUri`parameter from `convert()`and `encode()`methods for cleaner API\n\n### Fixes\n\n- Formatting fixes (dart format)\n- **N-Quads Encoder**: When set to canonical, it will deduplicate the tuples before outputting the result"
+  },
+  {
+    "id": "urn:uuid:90f293a5-f42c-4734-aafc-e4ecf2d36608",
+    "title": "v5.0.2 of flutter_yfree",
+    "url": "https://pub.dev/packages/flutter_yfree",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T10:16:11.235Z",
+    "summary": "YFree_Flutter Lite Version, Lightweight framework for Flutter development"
+  },
+  {
+    "id": "urn:uuid:1a7174f8-3fbd-4897-8d70-50133fea8436",
+    "title": "v2.0.12+2 of cyber_req",
+    "url": "https://pub.dev/packages/cyber_req",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-30T19:14:48.036Z",
+    "summary": "A flexible Flutter/Dart API client for backends with dynamic headers, secure token management, and comprehensive callbacks."
+  },
+  {
+    "id": "urn:uuid:dc7fd176-cac3-4311-a5af-17a6980ddaeb",
+    "title": "v1.5.0 of facial_liveness_detection_flutter_plugin",
+    "url": "https://pub.dev/packages/facial_liveness_detection_flutter_plugin",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-30T20:13:10.229Z",
+    "summary": "人脸活体检测，支持静默，炫彩，眨眼，张嘴，摇头，点头，远近及随意动作组合，支持Android,IOS, HMOS, H5,小程序等"
+  },
+  {
+    "id": "urn:uuid:8086d6ea-34ed-41e6-a19e-1e92f68a685b",
+    "title": "v1.1.0 of nigeria_geo_sdk",
+    "url": "https://pub.dev/packages/nigeria_geo_sdk",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-30T10:09:50.428Z",
+    "summary": "High-performance Flutter SDK for comprehensive Nigerian geographic data including states, LGAs, wards, and postal codes. Optimized for fast response times with direct database access.\n\nChangelog excerpt:\n### Added\n\n- Performance optimizations with direct database access architecture\n- Enhanced widget pickers to show comprehensive data (50+ states, 50+ LGAs, 100+ wards)\n- Production server configuration ([http://20.63.52.179:3000](http://20.63.52.179:3000))\n- Comprehensive README with detailed usage examples\n- New pagination demonstration in example app\n- Support for all 37 Nigerian states, 774 LGAs, and 8,840+ wards\n\n### Changed\n\n- **BREAKING**: Reduced default API timeout from 30s to 5s for opt[...]"
+  },
+  {
+    "id": "urn:uuid:a4872fdb-907d-43bc-b87f-5c90905427ec",
+    "title": "v5.0.1 of flutter_yfree",
+    "url": "https://pub.dev/packages/flutter_yfree",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T10:08:09.133Z",
+    "summary": "YFree_Flutter Lite Version, Lightweight framework for Flutter development"
+  },
+  {
+    "id": "urn:uuid:6e4732c5-8b61-44b9-ad4d-69479b531fcd",
+    "title": "v5.0.0 of flutter_yfree",
+    "url": "https://pub.dev/packages/flutter_yfree",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T10:06:17.409Z",
+    "summary": "YFree_Flutter Lite Version, Lightweight framework for Flutter development"
+  },
+  {
+    "id": "urn:uuid:fbe64b11-641f-4f09-ae54-afd386e01390",
+    "title": "v0.0.1+1 of fast_app_base",
+    "url": "https://pub.dev/packages/fast_app_base",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T10:05:48.099Z",
+    "summary": "Kickstart your Flutter app with fast_app_base — a ready-to-use foundation offering theme, routing, internationalization, dialogs, and Snackbar support out of the box."
+  },
+  {
+    "id": "urn:uuid:76e8239a-2d47-42a0-ad3e-09b7319957c4",
+    "title": "v0.2.0 of rdf_canonicalization",
+    "url": "https://pub.dev/packages/rdf_canonicalization",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T10:04:45.571Z",
+    "summary": "A Dart library for RDF graph canonicalization and isomorphism testing, implementing the W3C RDF Dataset Canonicalization specification.\n\nChangelog excerpt:\n### Fixed\n\n- **Test Suite Compliance**: Fixed critical bugs to achieve full compliance with the official W3C RDF canonicalization test suite\n- **Specification Alignment**: Re-implemented n-degree hashing algorithm to more closely align with the W3C specification\n- **Implementation Structure**: Improved canonicalization implementation with better quad handling and processing\n\n### Changed\n\n- **Code Quality**: Introduced typedefs to improve code readability and maintainability\n- **Test Infrastructu[...]"
+  },
+  {
+    "id": "urn:uuid:4bedfb42-2e0f-4c4d-a801-e6f8dbb4cd03",
+    "title": "v0.1.0 of photo_viewer",
+    "url": "https://pub.dev/packages/photo_viewer",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T10:03:17.646Z",
+    "summary": "Flutter image viewer library with multiple display modes. Features include: pinch-zoom, double-tap zoom, vertical dismiss, page navigation, custom overlays, and hero animations.\n\nChangelog excerpt:\n### Improvements\n\n- Check whether the url is local image path\n- Expose some GestureDetector properties in PhotoViewerImage\n- Resolve conflict between viewer zoom and horizontal swipes\n- Fix the warnings in Flutter 3.35.4 (Replaced Matrix4.translate and scale with translateByDouble and scaleByDouble)"
+  },
+  {
+    "id": "urn:uuid:8068dad2-241d-4b14-b9e5-6339a7311ae1",
+    "title": "v1.1.0 of keyboard_utils_plugin",
+    "url": "https://pub.dev/packages/keyboard_utils_plugin",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T09:53:10.917Z",
+    "summary": "fork from https://github.com/IsaiasSantana/keyboard_utils\n\nChangelog excerpt:\n- Flutter swift plugin fix"
+  },
+  {
+    "id": "urn:uuid:b0c532af-c83f-4897-9723-6b2a15545240",
+    "title": "v0.0.4 of l_value_extensions",
+    "url": "https://pub.dev/packages/l_value_extensions",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T09:53:06.195Z",
+    "summary": "值扩展\n\nChangelog excerpt:\n- LMapExtension key 类型由 String 改为 dynamic"
+  },
+  {
+    "id": "urn:uuid:834307e8-ed35-4a2e-88f1-2c09fdd6e35b",
+    "title": "v0.1.14 of alghwalbi_core_app",
+    "url": "https://pub.dev/packages/alghwalbi_core_app",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T09:52:43.577Z",
+    "summary": "alghwalbi_core_app package is primarily intended for personal use, but feel free to use it in your projects."
+  },
+  {
+    "id": "urn:uuid:26ca4075-3e9d-4fd4-9f30-74e51363e019",
+    "title": "v1.5.1 of taboola_sdk_beta",
+    "url": "https://pub.dev/packages/taboola_sdk_beta",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T09:43:35.833Z",
+    "summary": "Taboola's Flutter Core SDK. This SDK provides access to Taboola's Widget and Feed integrations for Flutter applications.\n\nChangelog excerpt:\n- fix android issue test"
+  },
+  {
+    "id": "urn:uuid:7a4bda11-a26e-4ea8-9368-d3731dcbcc6b",
+    "title": "v0.7.1 of video_player_avplay",
+    "url": "https://pub.dev/packages/video_player_avplay",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T09:39:28.364Z",
+    "summary": "Flutter plugin for displaying inline video on Tizen TV devices.\n\nChangelog excerpt:\n- Fix an assertion issue when getting AD information."
+  },
+  {
+    "id": "urn:uuid:dc2976b7-2f38-48f0-8f5d-346226b6d8e0",
+    "title": "v1.1.2 of text_field_validator",
+    "url": "https://pub.dev/packages/text_field_validator",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T09:33:30.909Z",
+    "summary": "A flutter package for both Android and iOS which provides the validation functionalities for the input fields.\n\nChangelog excerpt:\n- updated document"
+  },
+  {
+    "id": "urn:uuid:6a367ba8-af1c-4571-bf14-4a012c4d5b51",
+    "title": "v1.1.1 of text_field_validator",
+    "url": "https://pub.dev/packages/text_field_validator",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T09:12:57.680Z",
+    "summary": "A flutter package for both Android and iOS which provides the validation functionalities for the input fields.\n\nChangelog excerpt:\n- added adhar, pan, dl, ifsc, gst input formatter"
+  },
+  {
+    "id": "urn:uuid:630caa19-72a5-42ea-9ce0-563c2bfa7f2b",
+    "title": "v0.0.4 of turn_bartender",
+    "url": "https://pub.dev/packages/turn_bartender",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T09:11:45.943Z",
+    "summary": "加打印份数的参数"
+  },
+  {
+    "id": "urn:uuid:f9be82de-8619-49a2-94e2-a3c7a376aa1a",
+    "title": "v1.0.1 of flutter_unify",
+    "url": "https://pub.dev/packages/flutter_unify",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-30T17:05:08.626Z",
+    "summary": "The ultimate unified API for cross-platform Flutter development. One API, all platforms - legendary developer experience with reactive streams, pluggable adapters, and best-in-class DX.\n\nChangelog excerpt:\n### 🏆 160/160 Pub Points Achievement - Production Ready\n\nThis release focuses on achieving the maximum pub.dev score of 160/160 points through comprehensive code quality improvements, enhanced documentation, and production-ready optimizations.\n\n### 🔧 Fixed - Code Quality & Analysis\n\n#### Static Analysis Improvements\n\n- **Removed Unused Imports**   - Eliminated unused `dart:convert`import from `unified_networking.dart`\n  - Removed unnecessary `dart:typed_data`import from `media.dart`\n  - Cleane[...]"
+  },
+  {
+    "id": "urn:uuid:ede5dd24-b32e-4947-883c-daac679c5d3a",
+    "title": "v0.0.34 of delta_trace_db",
+    "url": "https://pub.dev/packages/delta_trace_db",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T09:00:54.331Z",
+    "summary": "The NoSQL in-memory database with class-based functionality and detailed operation history tracking.\n\nChangelog excerpt:\n- In search queries, sortObj is no longer required when using offset, startAfter, or endBefore. If not specified, the queries will be processed in the order they were added to the database.\n- The getAll query now supports offset, startAfter, endBefore, and limit, making it easier to implement paging within a collection.\n- Improved QueryBuilder and RawQueryBuilder descriptions."
+  },
+  {
+    "id": "urn:uuid:0e6cb89d-523a-4082-9749-a7f177941f1c",
+    "title": "v2.2.19 of dart_json_mapper",
+    "url": "https://pub.dev/packages/dart_json_mapper",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-30T09:00:19.676Z",
+    "summary": "This package allows programmers to annotate Dart objects in order to serialize / deserialize them from / to JSON.\n\n\nChangelog excerpt:\n- #230, Bug: @JsonProperty(flatten: true) fails on deserialization"
+  },
+  {
+    "id": "urn:uuid:b6b6f0bc-b3a1-4bde-ae2b-dbe055064083",
+    "title": "v1.4.5 of junny_dev_kit",
+    "url": "https://pub.dev/packages/junny_dev_kit",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T08:55:07.165Z",
+    "summary": "JunnySoft开发工具包.\n\nChangelog excerpt:\n### 🔧 菜单系统增强\n\n-  **菜单数据加载器优化**：重构菜单数据加载逻辑，新增批量处理和错误处理机制 • 影响位置：\n\n   - `lib/src/menus/widgets/menu_data_loader.dart`\n \n-  **菜单处理器改进**：优化工作台数据处理器和菜单点击处理器，提升性能和稳定性 • 影响位置：\n\n   - `lib/src/menus/processors/workbench_data_processor.dart`\n  - `lib/src/menus/processors/menu_tap_handler.dart`\n \n-  **菜单组件增强**：改进菜单处理器工厂和通用菜单组件的功能和用户体验 • 影响位置：\n\n   - `lib/src/menus/widgets/menu_handler_factory.dart`\n  - `lib/src/menus/widgets/universal_menu_widget.dart`\n \n-  **菜单配置优化**：更新菜单配置组和页面组件的配置管理 • 影响位置：\n\n   - `lib/s[...]"
+  },
+  {
+    "id": "urn:uuid:80c6b6ae-6cc2-48ab-8bf9-b10d4e712ab1",
+    "title": "v1.0.0 of keyboard_utils_plugin",
+    "url": "https://pub.dev/packages/keyboard_utils_plugin",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T08:48:33.235Z",
+    "summary": "fork from https://github.com/IsaiasSantana/keyboard_utils\n\nChangelog excerpt:\n- fork keyboard_utils_fork 1.0.1"
+  },
+  {
+    "id": "urn:uuid:12b26be5-fa51-4a75-8d4a-e1b9e73ceaed",
+    "title": "v0.0.1 of styless_snackbar",
+    "url": "https://pub.dev/packages/styless_snackbar",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T08:47:43.251Z",
+    "summary": "A modern Flutter snackbar/toast package with styles and animations.\n\nChangelog excerpt:\n- Initial release 🎉"
+  },
+  {
+    "id": "urn:uuid:1b91c177-b370-4193-9c0a-5df2fe43fa4c",
+    "title": "v1.0.6-dev.1 of adseye_ad_plugin",
+    "url": "https://pub.dev/packages/adseye_ad_plugin",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T08:44:45.531Z",
+    "summary": "Adseye Flutter Plugin Project."
+  },
+  {
+    "id": "urn:uuid:3b53b20b-8f82-4bea-b288-e188bb87fa8b",
+    "title": "v1.0.1 of text_field_validator",
+    "url": "https://pub.dev/packages/text_field_validator",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T08:40:42.766Z",
+    "summary": "A flutter package for both Android and iOS which provides the validation functionalities for the input fields.\n\nChangelog excerpt:\n- uasge document update"
+  },
+  {
+    "id": "urn:uuid:17042e10-b57b-4c66-9363-21b436c572f8",
+    "title": "v2.2.18 of dart_json_mapper",
+    "url": "https://blog.dart.dev/announcing-dart-3-8-724eaaec9f47?source=rss----23738d481ce8---4",
+    "source": "dart_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-05-20T18:08:39.000Z"
+  },
+  {
+    "id": "urn:uuid:dc5b02db-931e-48c4-bc59-10884468e0be",
+    "title": "v0.9.19 of dart_periphery",
+    "url": "https://pub.dev/packages/dart_periphery",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T08:30:39.301Z",
+    "summary": "dart_periphery is a Dart port of the native c-periphery library for Linux Peripheral I/O (GPIO, LED, PWM, SPI, I2C, MMIO, ADC and Serial peripheral I/O).\n\nChangelog excerpt:\n- Add GPS [Air530](https://github.com/pezi/dart_periphery/blob/ps/0.9.19/example/serial_air530.dart)\n- Update Dart Version to `3.9.3`"
+  },
+  {
+    "id": "urn:uuid:8f0173ed-c159-455c-b71b-8dcd4c8ff665",
+    "title": "v6.8.6 of flutter_inapp_purchase",
+    "url": "https://pub.dev/packages/flutter_inapp_purchase",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T08:28:23.116Z",
+    "summary": "In App Purchase plugin for flutter. This project has been forked by react-native-iap and we are willing to share same experience with that on react-native.\n\nChangelog excerpt:\n- feat: fetchProducts with ProductQueryType.All #568"
+  },
+  {
+    "id": "urn:uuid:9a01d5cc-56bd-4e9d-bf14-4d608ac20a56",
+    "title": "v1.0.0 of text_field_validator",
+    "url": "https://pub.dev/packages/text_field_validator",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T08:27:33.561Z",
+    "summary": "A flutter package for both Android and iOS which provides the validation functionalities for the input fields.\n\nChangelog excerpt:\n- added inputformatter feature in this release\n- supports any kind of input format."
+  },
+  {
+    "id": "urn:uuid:a8705c9e-b2ea-439d-97af-06ca6d761e03",
+    "title": "v0.0.5 of dsa_kit",
+    "url": "https://pub.dev/packages/dsa_kit",
+    "source": "pub_dev",
+    "section": "Ecosystem",
+    "publishedAt": "2025-09-28T17:21:44.145Z",
+    "summary": "A scalable DSA utilities kit for Dart. Includes Heaps, DeQueues, and more.\n\nChangelog excerpt:\n### Added\n\n- Added `INT`extension providing convenient constants:   - `INT.infinity`- Maximum positive integer value\n  - `INT.negativeInfinity`- Minimum negative integer value\n \n- Updated documentation and examples for INT extensions"
+  },
+  {
+    "id": "https://medium.com/p/c58ef72e3766",
+    "title": "What’s new in Flutter 3.35",
+    "url": "https://blog.flutter.dev/whats-new-in-flutter-3-35-c58ef72e3766?source=rss----4da7dfd21a33---4",
+    "source": "flutter_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-08-14T18:23:26.000Z"
+  },
+  {
+    "id": "https://medium.com/p/9a8c94564635",
+    "title": "Unleash new AI capabilities for Flutter in Firebase Studio",
+    "url": "https://blog.flutter.dev/unleash-new-ai-capabilities-for-flutter-in-firebase-studio-9a8c94564635?source=rss----4da7dfd21a33---4",
+    "source": "flutter_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-07-23T05:02:36.000Z"
+  },
+  {
+    "id": "https://medium.com/p/2edcc8107b49",
+    "title": "Supercharge Your Dart & Flutter Development Experience with the Dart MCP Server",
+    "url": "https://blog.flutter.dev/supercharge-your-dart-flutter-development-experience-with-the-dart-mcp-server-2edcc8107b49?source=rss----4da7dfd21a33---4",
+    "source": "flutter_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-07-23T05:02:33.000Z"
+  },
+  {
+    "id": "https://medium.com/p/4863aa4f84a4",
+    "title": "Dart & Flutter momentum at Google I/O 2025",
+    "url": "https://blog.flutter.dev/dart-flutter-momentum-at-google-i-o-2025-4863aa4f84a4?source=rss----4da7dfd21a33---4",
+    "source": "flutter_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-05-21T18:00:57.000Z"
+  },
+  {
+    "id": "https://medium.com/p/4bf7d4579d9a",
+    "title": "Flutter’s path towards seamless interop",
+    "url": "https://blog.flutter.dev/flutters-path-towards-seamless-interop-4bf7d4579d9a?source=rss----4da7dfd21a33---4",
+    "source": "flutter_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-05-20T18:03:41.000Z"
+  },
+  {
+    "id": "https://medium.com/p/915dfec98274",
+    "title": "Gemini in Android Studio now speaks fluent Flutter!",
+    "url": "https://blog.flutter.dev/gemini-in-android-studio-now-speaks-fluent-flutter-915dfec98274?source=rss----4da7dfd21a33---4",
+    "source": "flutter_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-05-20T18:03:09.000Z"
+  },
+  {
+    "id": "https://medium.com/p/40c1086bab6e",
+    "title": "What’s new in Flutter 3.32",
+    "url": "https://blog.flutter.dev/whats-new-in-flutter-3-32-40c1086bab6e?source=rss----4da7dfd21a33---4",
+    "source": "flutter_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-05-20T18:02:25.000Z"
+  },
+  {
+    "id": "https://medium.com/p/ad46b38b1adb",
+    "title": "Learn how to build agentic apps with Flutter, Angular, Firebase, and Vertex AI",
+    "url": "https://blog.flutter.dev/learn-how-to-build-agentic-apps-with-flutter-angular-firebase-and-vertex-ai-ad46b38b1adb?source=rss----4da7dfd21a33---4",
+    "source": "flutter_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-04-24T16:23:03.000Z"
+  },
+  {
+    "id": "https://medium.com/p/f127882b117f",
+    "title": "Flutter 2025 roadmap update",
+    "url": "https://blog.flutter.dev/flutter-2025-roadmap-f127882b117f?source=rss----4da7dfd21a33---4",
+    "source": "flutter_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-04-02T20:27:24.000Z"
+  },
+  {
+    "id": "https://medium.com/p/f90c380c2317",
+    "title": "What’s new in Flutter 3.29",
+    "url": "https://blog.flutter.dev/whats-new-in-flutter-3-29-f90c380c2317?source=rss----4da7dfd21a33---4",
+    "source": "flutter_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-02-12T19:09:57.000Z"
+  },
+  {
+    "id": "https://medium.com/p/ba49e8f38298",
+    "title": "Announcing Dart 3.9",
+    "url": "https://blog.dart.dev/announcing-dart-3-9-ba49e8f38298?source=rss----23738d481ce8---4",
+    "source": "dart_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-08-14T18:17:15.000Z"
+  },
+  {
+    "id": "https://medium.com/p/724eaaec9f47",
+    "title": "Announcing Dart 3.8",
+    "url": "https://blog.dart.dev/announcing-dart-3-8-724eaaec9f47?source=rss----23738d481ce8---4",
+    "source": "dart_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-05-20T18:08:39.000Z"
+  },
+  {
+    "id": "https://medium.com/p/ca962729cee6",
+    "title": "Gemini for DartPad",
+    "url": "https://blog.dart.dev/gemini-for-dartpad-ca962729cee6?source=rss----23738d481ce8---4",
+    "source": "dart_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-04-08T16:06:49.000Z"
+  },
+  {
+    "id": "https://medium.com/p/b6373bdb7a08",
+    "title": "Dart in Google Summer of Code 2025",
+    "url": "https://blog.dart.dev/dart-in-google-summer-of-code-2025-b6373bdb7a08?source=rss----23738d481ce8---4",
+    "source": "dart_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-03-27T11:25:19.000Z"
+  },
+  {
+    "id": "https://medium.com/p/bf864a1b195c",
+    "title": "Announcing Dart 3.7",
+    "url": "https://blog.dart.dev/announcing-dart-3-7-bf864a1b195c?source=rss----23738d481ce8---4",
+    "source": "dart_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-02-12T19:10:27.000Z"
+  },
+  {
+    "id": "https://medium.com/p/06d3037d4f12",
+    "title": "An update on Dart macros & data serialization",
+    "url": "https://blog.dart.dev/an-update-on-dart-macros-data-serialization-06d3037d4f12?source=rss----23738d481ce8---4",
+    "source": "dart_blog",
+    "section": "Headlines",
+    "publishedAt": "2025-01-29T21:23:22.000Z"
+  },
+  {
+    "id": "https://medium.com/p/778dd7a80983",
+    "title": "Announcing Dart 3.6",
+    "url": "https://blog.dart.dev/announcing-dart-3-6-778dd7a80983?source=rss----23738d481ce8---4",
+    "source": "dart_blog",
+    "section": "Headlines",
+    "publishedAt": "2024-12-11T20:20:13.000Z"
+  },
+  {
+    "id": "https://medium.com/p/ae925357d2d7",
+    "title": "Google Summer of Code 2024 Results",
+    "url": "https://blog.dart.dev/google-summer-of-code-2024-results-ae925357d2d7?source=rss----23738d481ce8---4",
+    "source": "dart_blog",
+    "section": "Headlines",
+    "publishedAt": "2024-10-14T17:23:41.000Z"
+  },
+  {
+    "id": "https://medium.com/p/6ca36259fa2f",
+    "title": "Announcing Dart 3.5, and an update on the Dart roadmap",
+    "url": "https://blog.dart.dev/dart-3-5-6ca36259fa2f?source=rss----23738d481ce8---4",
+    "source": "dart_blog",
+    "section": "Headlines",
+    "publishedAt": "2024-08-06T18:02:40.000Z"
+  }
+]
\ No newline at end of file
diff --git a/data/runs.json b/data/runs.json
new file mode 100644
index 0000000..e1b61f1
--- /dev/null
+++ b/data/runs.json
@@ -0,0 +1,90 @@
+[
+  {
+    "id": 1,
+    "startedAt": 1759079617843,
+    "finishedAt": 1759079618051,
+    "fetched": 0,
+    "inserted": 0
+  },
+  {
+    "id": 2,
+    "startedAt": 1759079661212,
+    "finishedAt": 1759079661846,
+    "fetched": 0,
+    "inserted": 0
+  },
+  {
+    "id": 3,
+    "startedAt": 1759079712130,
+    "finishedAt": 1759079712785,
+    "fetched": 121,
+    "inserted": 0
+  },
+  {
+    "id": 4,
+    "startedAt": 1759080099837,
+    "finishedAt": 1759080100457,
+    "fetched": 121,
+    "inserted": 0
+  },
+  {
+    "id": 5,
+    "startedAt": 1759080227106,
+    "finishedAt": 1759080228354,
+    "fetched": 121,
+    "inserted": 1
+  },
+  {
+    "id": 6,
+    "startedAt": 1759080362598,
+    "finishedAt": 1759080363306,
+    "fetched": 121,
+    "inserted": 0
+  },
+  {
+    "id": 7,
+    "startedAt": 1759080444052,
+    "finishedAt": 1759080444745,
+    "fetched": 141,
+    "inserted": 20
+  },
+  {
+    "id": 8,
+    "startedAt": 1759080533364,
+    "finishedAt": 1759080534209,
+    "fetched": 141,
+    "inserted": 0
+  },
+  {
+    "id": 9,
+    "startedAt": 1759084049162,
+    "finishedAt": 1759084049828,
+    "fetched": 141,
+    "inserted": 2
+  },
+  {
+    "id": 10,
+    "startedAt": 1759085863088,
+    "finishedAt": 1759085863740,
+    "fetched": 141,
+    "inserted": 7
+  },
+  {
+    "id": 11,
+    "startedAt": 1759289712309
+  },
+  {
+    "id": 12,
+    "startedAt": 1759289832145,
+    "finishedAt": 1759289832906,
+    "fetched": 140,
+    "inserted": 111
+  },
+  {
+    "id": 13,
+    "startedAt": 1759289880224,
+    "finishedAt": 1759289881724,
+    "fetched": 140,
+    "inserted": 0
+  }
+]
\ No newline at end of file
diff --git a/data/tokens.db b/data/tokens.db
index 5542dde..f0caac2 100644
Binary files a/data/tokens.db and b/data/tokens.db differ
diff --git a/data/tokens.db-shm b/data/tokens.db-shm
index c866a17..fe9ac28 100644
Binary files a/data/tokens.db-shm and b/data/tokens.db-shm differ
diff --git a/data/tokens.db-wal b/data/tokens.db-wal
index dafbf0d..e69de29 100644
Binary files a/data/tokens.db-wal and b/data/tokens.db-wal differ
diff --git a/src/briefing/db.ts b/src/briefing/db.ts
index 67d98c0..4f5d38d 100644
--- a/src/briefing/db.ts
+++ b/src/briefing/db.ts
@@ -1,97 +1,263 @@
+import Database from "better-sqlite3";
 import fs from "node:fs";
 import path from "node:path";
-import { Item, ItemSchema } from "./types";
+import { Item, ItemSchema, Section, SourceKey } from "./types";
 
+// Storage locations
 const DB_DIR = process.env.BRIEFING_DATA_DIR || path.join(process.cwd(), "data");
-const ITEMS_FILE = path.join(DB_DIR, "items.json");
-const RUNS_FILE = path.join(DB_DIR, "runs.json");
+const DB_FILE = path.join(DB_DIR, "briefing.db");
 
-interface DbData {
-  items: Item[];
-  runs: {
-    id: number;
-    startedAt: number;
-    finishedAt?: number;
-    fetched?: number;
-    inserted?: number;
-  }[];
-  nextRunId: number;
-}
+// Internal DB handle (opened lazily)
+let db: Database.Database | null = null;
 
-function loadDbData(): DbData {
+// Schema helpers
+function ensureDb(): Database.Database {
+  if (db) return db;
   fs.mkdirSync(DB_DIR, { recursive: true });
-  let items: Item[] = [];
-  let runs: DbData['runs'] = [];
-  let nextRunId = 1;
-
-  if (fs.existsSync(ITEMS_FILE)) {
-    try {
-      const raw = fs.readFileSync(ITEMS_FILE, "utf8");
-      const parsed = JSON.parse(raw);
-      // Validate and parse dates for items
-      items = (parsed as any[]).map(i => {
-        const item = ItemSchema.safeParse({ ...i, publishedAt: new Date(i.publishedAt) });
-        return item.success ? item.data : null;
-      }).filter(Boolean) as Item[];
-    } catch (e) {
-      console.error("Error loading items.json:", e);
-    }
+  db = new Database(DB_FILE, { fileMustExist: false });
+
+  // Pragmas tuned for a write-heavy ingestion workload with safe durability
+  db.pragma("journal_mode = WAL");
+  db.pragma("synchronous = NORMAL");
+  db.pragma("foreign_keys = ON");
+  db.pragma("busy_timeout = 5000");
+
+  // Create tables/indexes if they don't exist
+  db.exec(`
+    CREATE TABLE IF NOT EXISTS items (
+      id TEXT PRIMARY KEY,
+      title TEXT NOT NULL,
+      url TEXT NOT NULL,
+      source TEXT NOT NULL,
+      section TEXT NOT NULL,
+      published_at INTEGER NOT NULL,
+      summary TEXT,
+      meta TEXT
+    );
+    CREATE INDEX IF NOT EXISTS idx_items_published_at ON items(published_at DESC);
+
+    CREATE TABLE IF NOT EXISTS runs (
+      id INTEGER PRIMARY KEY AUTOINCREMENT,
+      started_at INTEGER NOT NULL,
+      finished_at INTEGER,
+      fetched INTEGER,
+      inserted INTEGER
+    );
+  `);
+
+  return db;
+}
+
+// Map DB row to Item with runtime validation and coercion
+function rowToItem(row: any): Item {
+  const raw = {
+    id: row.id as string,
+    title: row.title as string,
+    url: row.url as string,
+    source: row.source as SourceKey,
+    section: row.section as Section,
+    publishedAt: new Date(Number(row.published_at)),
+    summary: row.summary ?? undefined,
+    meta: row.meta ? safeParseJson(row.meta) : undefined,
+  };
+  // Validate via zod; if invalid, throw to surface corrupt data
+  const parsed = ItemSchema.safeParse(raw);
+  if (!parsed.success) {
+    throw new Error(`Invalid item row for id=${row.id}: ${parsed.error.message}`);
+  }
+  return parsed.data;
+}
+
+function safeParseJson(v: string): Record<string, unknown> | undefined {
+  try {
+    return JSON.parse(v);
+  } catch {
+    return undefined;
   }
+}
 
-  if (fs.existsSync(RUNS_FILE)) {
-    try {
-      const raw = fs.readFileSync(RUNS_FILE, "utf8");
-      runs = JSON.parse(raw);
-      nextRunId = runs.length > 0 ? Math.max(...runs.map(r => r.id)) + 1 : 1;
-    } catch (e) {
-      console.error("Error loading runs.json:", e);
-    }
+// Build parameters object for prepared statements
+function itemParams(it: Item) {
+  return {
+    id: it.id,
+    title: it.title,
+    url: it.url,
+    source: it.source,
+    section: it.section,
+    published_at: it.publishedAt instanceof Date ? it.publishedAt.getTime() : new Date(it.publishedAt).getTime(),
+    summary: it.summary ?? null,
+    meta: it.meta ? JSON.stringify(it.meta) : null,
+  } as const;
+}
+
+// Prepared statements (initialized on first use)
+const stmts = {
+  upsertItem: null as Database.Statement | null,
+  selectItemsSince: null as Database.Statement | null,
+  insertRun: null as Database.Statement | null,
+  finishRun: null as Database.Statement | null,
+} as const;
+
+function getUpsertStmt(): Database.Statement {
+  const _db = ensureDb();
+  if (!stmts.upsertItem) {
+    // Only update when values are actually different to keep change counts meaningful
+    // The WHERE clause avoids needless row updates when data is identical
+    stmts.upsertItem = _db.prepare(`
+      INSERT INTO items (id, title, url, source, section, published_at, summary, meta)
+      VALUES (@id, @title, @url, @source, @section, @published_at, @summary, @meta)
+      ON CONFLICT(id) DO UPDATE SET
+        title = excluded.title,
+        url = excluded.url,
+        source = excluded.source,
+        section = excluded.section,
+        published_at = excluded.published_at,
+        summary = excluded.summary,
+        meta = excluded.meta
+      WHERE
+        title IS NOT excluded.title OR
+        url IS NOT excluded.url OR
+        source IS NOT excluded.source OR
+        section IS NOT excluded.section OR
+        published_at IS NOT excluded.published_at OR
+        summary IS NOT excluded.summary OR
+        meta IS NOT excluded.meta;
+    `);
   }
+  return stmts.upsertItem!;
+}
 
-  return { items, runs, nextRunId };
+function getSelectItemsSinceStmt(): Database.Statement {
+  const _db = ensureDb();
+  if (!stmts.selectItemsSince) {
+    stmts.selectItemsSince = _db.prepare(
+      `SELECT id, title, url, source, section, published_at, summary, meta
+       FROM items
+       WHERE published_at >= ?
+       ORDER BY published_at DESC`
+    );
+  }
+  return stmts.selectItemsSince!;
 }
 
-function saveDbData(data: DbData) {
-  fs.writeFileSync(ITEMS_FILE, JSON.stringify(data.items, null, 2), "utf8");
-  fs.writeFileSync(RUNS_FILE, JSON.stringify(data.runs, null, 2), "utf8");
+function getInsertRunStmt(): Database.Statement {
+  const _db = ensureDb();
+  if (!stmts.insertRun) {
+    stmts.insertRun = _db.prepare(
+      `INSERT INTO runs (started_at) VALUES (?)`
+    );
+  }
+  return stmts.insertRun!;
 }
 
-export function upsertItem(item: Item): boolean {
-  const data = loadDbData();
-  const existingIndex = data.items.findIndex((i) => i.id === item.id);
-  if (existingIndex !== -1) {
-    data.items[existingIndex] = item; // Update existing
-    saveDbData(data);
-    return false; // Not inserted, but updated
-  } else {
-    data.items.push(item);
-    saveDbData(data);
-    return true; // Inserted
+function getFinishRunStmt(): Database.Statement {
+  const _db = ensureDb();
+  if (!stmts.finishRun) {
+    stmts.finishRun = _db.prepare(
+      `UPDATE runs
+       SET finished_at = ?, fetched = ?, inserted = ?
+       WHERE id = ?`
+    );
   }
+  return stmts.finishRun!;
+}
+
+// Efficiently fetch a set of existing IDs to compute inserted vs updated counts
+function fetchExistingIds(ids: string[]): Set<string> {
+  if (ids.length === 0) return new Set();
+  const _db = ensureDb();
+  const existing = new Set<string>();
+
+  // SQLite has a default max of 999 variables per statement
+  const CHUNK = 500;
+  for (let i = 0; i < ids.length; i += CHUNK) {
+    const chunk = ids.slice(i, i + CHUNK);
+    const placeholders = chunk.map(() => "?").join(",");
+    const sql = `SELECT id FROM items WHERE id IN (${placeholders})`;
+    const rows = _db.prepare(sql).all(...chunk) as { id: string }[];
+    for (const r of rows) existing.add(r.id);
+  }
+  return existing;
+}
+
+// Public API
+export function bulkUpsert(items: Item[]): { inserted: number; updated: number } {
+  if (!items || items.length === 0) return { inserted: 0, updated: 0 };
+
+  // De-duplicate by id (last one wins)
+  const map = new Map<string, Item>();
+  for (const it of items) {
+    const parsed = ItemSchema.safeParse(it);
+    if (!parsed.success) continue; // skip invalid items defensively
+    map.set(parsed.data.id, parsed.data);
+  }
+  const uniqueItems = Array.from(map.values());
+  const ids = uniqueItems.map((i) => i.id);
+  const existing = fetchExistingIds(ids);
+  const expectedInserted = uniqueItems.filter((i) => !existing.has(i.id)).length;
+
+  const stmt = getUpsertStmt();
+  const _db = ensureDb();
+  const tx = _db.transaction((batch: Item[]) => {
+    for (const it of batch) {
+      stmt.run(itemParams(it));
+    }
+  });
+
+  tx(uniqueItems);
+
+  // We count inserted using pre-check; updates are the remainder
+  return { inserted: expectedInserted, updated: uniqueItems.length - expectedInserted };
+}
+
+export function upsertItem(item: Item): boolean {
+  const res = bulkUpsert([item]);
+  return res.inserted === 1;
 }
 
 export function listItemsSince(sinceMs: number): Item[] {
-  const data = loadDbData();
-  return data.items.filter((i) => i.publishedAt.getTime() >= sinceMs).sort((a, b) => b.publishedAt.getTime() - a.publishedAt.getTime());
+  const stmt = getSelectItemsSinceStmt();
+  const rows = stmt.all(sinceMs) as any[];
+  return rows.map(rowToItem);
 }
 
 export function startRun(): number {
-  const data = loadDbData();
-  const runId = data.nextRunId++;
-  data.runs.push({ id: runId, startedAt: Date.now() });
-  saveDbData(data);
-  return runId;
+  const stmt = getInsertRunStmt();
+  const info = stmt.run(Date.now());
+  // better-sqlite3 returns BigInt or number depending on configuration
+  const id = Number(info.lastInsertRowid);
+  return id;
 }
 
 export function finishRun(runId: number, stats: { fetched: number; inserted: number }) {
-  const data = loadDbData();
-  const run = data.runs.find((r) => r.id === runId);
-  if (run) {
-    run.finishedAt = Date.now();
-    run.fetched = stats.fetched;
-    run.inserted = stats.inserted;
-    saveDbData(data);
-  } else {
+  const stmt = getFinishRunStmt();
+  const res = stmt.run(Date.now(), stats.fetched, stats.inserted, runId);
+  if (res.changes === 0) {
     console.warn(`Run with ID ${runId} not found.`);
   }
 }
+
+// Legacy helpers kept for backward compatibility. They are optimized to avoid full dataset I/O.
+// In the SQLite-backed implementation, loadDbData no longer reads entire JSON files.
+// It simply ensures the DB exists and computes the next run id lazily.
+interface DbData {
+  items: Item[];
+  runs: {
+    id: number;
+    startedAt: number;
+    finishedAt?: number;
+    fetched?: number;
+    inserted?: number;
+  }[];
+  nextRunId: number;
+}
+
+function loadDbData(): DbData {
+  const _db = ensureDb();
+  const row = _db.prepare(`SELECT COALESCE(MAX(id) + 1, 1) AS nextRunId FROM runs`).get() as { nextRunId: number };
+  return { items: [], runs: [], nextRunId: row?.nextRunId ?? 1 };
+}
+
+function saveDbData(_data: DbData) {
+  // No-op in SQLite implementation. Writes are applied transactionally per operation.
+}
diff --git a/src/briefing/sqlite_db.ts b/src/briefing/sqlite_db.ts
new file mode 100644
index 0000000..8ffdca3
--- /dev/null
+++ b/src/briefing/sqlite_db.ts
@@ -0,0 +1,92 @@
+import Database from 'better-sqlite3';
+import * as fs from 'fs';
+import * as path from 'path';
+
+// Define the path to the SQLite database file
+const DB_PATH = path.join(__dirname, '../../data/briefing.db');
+const JSON_DB_PATH = path.join(__dirname, '../../data/db.json'); // Assuming this is where the old JSON DB is
+
+let db: Database.Database;
+
+interface Item {
+    id: string;
+    title: string;
+    content: string;
+    // Add other fields from your existing Item structure
+}
+
+function initializeDatabase() {
+    if (!db) {
+        // Ensure the directory exists
+        const dbDir = path.dirname(DB_PATH);
+        if (!fs.existsSync(dbDir)) {
+            fs.mkdirSync(dbDir, { recursive: true });
+        }
+        db = new Database(DB_PATH);
+        db.pragma('journal_mode = WAL'); // Recommended for better performance and concurrency
+
+        // Create the items table if it doesn't exist
+        db.exec(`
+            CREATE TABLE IF NOT EXISTS items (
+                id TEXT PRIMARY KEY,
+                title TEXT,
+                content TEXT
+                -- Add other columns as per your Item interface
+            );
+        `);
+    }
+}
+
+export function getDbInstance(): Database.Database {
+    initializeDatabase();
+    return db;
+}
+
+export function upsertItem(item: Item): boolean {
+    const db = getDbInstance();
+    const stmt = db.prepare(`
+        INSERT INTO items (id, title, content)
+        VALUES (?, ?, ?)
+        ON CONFLICT(id) DO UPDATE SET
+            title = EXCLUDED.title,
+            content = EXCLUDED.content;
+    `);
+    const info = stmt.run(item.id, item.title, item.content);
+    return info.changes > 0; // true if inserted/updated
+}
+
+export function migrateJsonToSqlite() {
+    initializeDatabase();
+    const db = getDbInstance();
+
+    if (!fs.existsSync(JSON_DB_PATH)) {
+        console.log(`No JSON database found at ${JSON_DB_PATH}. Skipping migration.`);
+        return;
+    }
+
+    try {
+        const jsonData = fs.readFileSync(JSON_DB_PATH, 'utf-8');
+        const oldDbData = JSON.parse(jsonData);
+        const itemsToMigrate: Item[] = oldDbData.items || [];
+
+        if (itemsToMigrate.length === 0) {
+            console.log('No items to migrate from JSON database.');
+            return;
+        }
+
+        const insertMany = db.transaction((items) => {
+            for (const item of items) {
+                upsertItem(item);
+            }
+        });
+
+        insertMany(itemsToMigrate);
+        console.log(`Migrated ${itemsToMigrate.length} items from JSON to SQLite.`);
+
+    } catch (error) {
+        console.error('Error during JSON to SQLite migration:', error);
+    }
+}
+
+// Optional: call migration on startup
+// migrateJsonToSqlite();
