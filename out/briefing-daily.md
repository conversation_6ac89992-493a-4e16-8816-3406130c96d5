# Flutter Briefing – 2025-10-01
## Releases
- [3.10.0-269.0.dev](https://github.com/dart-lang/sdk/releases/tag/3.10.0-269.0.dev)
- [3.10.0-268.0.dev](https://github.com/dart-lang/sdk/releases/tag/3.10.0-268.0.dev)
- [3.10.0-267.0.dev](https://github.com/dart-lang/sdk/releases/tag/3.10.0-267.0.dev)
- [3.10.0-266.0.dev](https://github.com/dart-lang/sdk/releases/tag/3.10.0-266.0.dev)
- [3.10.0-265.0.dev](https://github.com/dart-lang/sdk/releases/tag/3.10.0-265.0.dev)
- [3.10.0-264.0.dev](https://github.com/dart-lang/sdk/releases/tag/3.10.0-264.0.dev)

## Ecosystem
- [v0.1.5 of chafon_h103_rfid](https://pub.dev/packages/chafon_h103_rfid)
- [v1.0.1 of calendar_bridge](https://pub.dev/packages/calendar_bridge)
- [v4.0.1 of golden_screenshot](https://pub.dev/packages/golden_screenshot)
- [v1.0.0 of hf_xet](https://pub.dev/packages/hf_xet)
- [v0.2.1 of resources_used_lints](https://pub.dev/packages/resources_used_lints)
- [v1.1.0 of vania](https://pub.dev/packages/vania)
- [v1.1.1 of solvro_config](https://pub.dev/packages/solvro_config)
- [v1.0.0 of jujuba_svg](https://pub.dev/packages/jujuba_svg)
- [v7.2.0 of chessground](https://pub.dev/packages/chessground)
- [v2.0.0-rc.5 of serinus](https://pub.dev/packages/serinus)
