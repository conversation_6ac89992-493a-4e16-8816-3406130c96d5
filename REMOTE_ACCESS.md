# Remote Session Access Guide

This guide explains how to use <PERSON>'s remote session feature to access your conversations from other devices on your local network (e.g., scanning a QR code with your phone).

## How It Works

Dante supports sharing sessions across devices on your private network using QR codes. When you share a session:

1. The UI generates a URL with your machine's local IP address (e.g., `http://*************:3002?session=session_id`)
2. Other devices on the same WiFi can scan the QR code to access the shared session
3. The remote device connects to both:
   - **UI Server** (Vite dev server on port 3002) - delivers the web interface
   - **API Server** (Express on port 3001) - handles chat requests

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Your Development Machine                  │
│                                                              │
│  ┌──────────────────┐         ┌─────────────────────────┐  │
│  │  API Server      │         │  Vite Dev Server (UI)   │  │
│  │  Port: 3001      │◄────────│  Port: 3002             │  │
│  │  Host: 0.0.0.0   │  Proxy  │  Host: 0.0.0.0          │  │
│  └──────────────────┘         └─────────────────────────┘  │
│         ▲                               ▲                   │
│         │                               │                   │
└─────────┼───────────────────────────────┼───────────────────┘
          │                               │
          │        Local Network          │
          │      (192.168.1.x)           │
          │                               │
    ┌─────┴───────────────────────────────┴─────┐
    │                                            │
    │         Remote Device (Phone/Tablet)       │
    │         Scans QR Code                      │
    │         http://*************:3002          │
    └────────────────────────────────────────────┘
```

## Setup Instructions

### 1. Ensure Correct Configuration

Make sure your `.env` file has the following settings:

```bash
# API Server Configuration
PORT=3001
HOST=0.0.0.0  # IMPORTANT: Must be 0.0.0.0 to accept network connections

# Optional: Override API base URL for proxy
# API_BASE_URL=http://localhost:3001
```

### 2. Start Both Servers

Start both the API server and the UI development server:

```bash
# Start both servers together
bun run dev:all

# Or start them separately:
# Terminal 1:
bun run dev:api

# Terminal 2:
bun run dev
```

### 3. Verify Network Binding

Check that servers are listening on all interfaces:

```bash
# You should see output like:
# Dante API server running on http://0.0.0.0:3001
# Network accessible at: http://*************:3001
```

### 4. Share a Session

1. In the Dante UI, click the share icon in the chat interface
2. A dialog will appear with:
   - A shareable URL (e.g., `http://*************:3002?session=...`)
   - A QR code for easy mobile access
3. Scan the QR code with your phone or copy the URL to another device

## Troubleshooting

### Issue: UI loads but chat requests fail with "Sorry, I encountered an error"

**Symptoms:**
- QR code works and UI displays correctly on remote device
- When sending a message, you get an error instead of a response

**Cause:**
The Vite proxy is not correctly forwarding API requests to the backend server.

**Solution:**
1. Verify the Vite proxy configuration in `vite.config.ts`:
   ```typescript
   proxy: {
     '/api': {
       target: 'http://localhost:3001',  // Should point to localhost
       changeOrigin: true,
       secure: false,
       ws: true,
     }
   }
   ```

2. Restart the dev server after any configuration changes:
   ```bash
   # Stop with Ctrl+C and restart
   bun run dev:all
   ```

### Issue: Cannot connect to the QR code URL

**Symptoms:**
- Scanning QR code results in "Cannot connect" or timeout

**Possible Causes & Solutions:**

1. **Firewall blocking connections:**
   ```bash
   # macOS: Allow incoming connections for Node/Bun
   # System Preferences > Security & Privacy > Firewall > Firewall Options
   # Add exceptions for your terminal app or Bun
   ```

2. **Servers not bound to 0.0.0.0:**
   - Check that `HOST=0.0.0.0` in your `.env` file
   - Verify server startup logs show `0.0.0.0` not `127.0.0.1`

3. **Different WiFi networks:**
   - Ensure all devices are on the same WiFi network
   - Some networks isolate devices (AP isolation) - try a different network

4. **VPN interfering:**
   - Disable VPN on development machine temporarily
   - Or configure VPN to allow LAN access

### Issue: CORS errors in browser console

**Symptoms:**
- Browser console shows CORS policy errors

**Solution:**
The API server already has comprehensive CORS configuration for private networks. Verify in `src/api/server.ts`:

```typescript
// CORS patterns include:
// - localhost (any port)
// - 127.0.0.1 (any port)
// - 10.x.x.x (Class A private)
// - 192.168.x.x (Class C private)
// - 172.16-31.x.x (Class B private)
```

If you need custom patterns, set `CORS_ALLOWED_ORIGINS` in `.env`:
```bash
CORS_ALLOWED_ORIGINS=http://************:3002,http://**********:3002
```

### Issue: Session not syncing between devices

**Symptoms:**
- Remote device shows old messages or doesn't update

**Explanation:**
Session data is stored in IndexedDB on each device. When you scan a QR code:
- The remote device adopts the session ID
- New messages sync via the API server
- Historical messages only exist on the original device

**This is by design** for privacy - only the current conversation syncs, not full history.

## Network Security Notes

⚠️ **Important Security Considerations:**

1. **Private Network Only:** This feature is designed for trusted private networks (home/office WiFi)
2. **No Authentication:** There is no password protection on shared sessions
3. **Local Data:** Session history remains on individual devices (not centrally stored)
4. **No Internet Exposure:** Never expose these ports to the public internet

## Advanced Configuration

### Using a Custom Port

If you need to use different ports:

```bash
# In .env
PORT=3005  # API server port

# Update vite.config.ts proxy target accordingly
target: 'http://localhost:3005'
```

### Production Deployment

For production with remote access:

1. Use a reverse proxy (nginx/Apache) with HTTPS
2. Implement proper authentication
3. Use environment-specific CORS configuration
4. Consider using a real-time sync service (WebSockets/SSE)

## Technical Details

### How the Proxy Works

1. Remote device requests `http://*************:3002` (UI)
2. Vite serves the React app
3. React app makes API calls to `/api/*`
4. Vite proxy intercepts these requests
5. Proxy forwards to `http://localhost:3001/api/*` (API server on same machine)
6. API server processes request and returns response
7. Proxy returns response to React app
8. React app displays result to user

### Why Use localhost in Proxy Target?

The Vite proxy runs **on the server side** (your development machine), not in the browser. When the remote device makes a request to `/api/chat`, the request flow is:

```
Remote Device → Vite Server (0.0.0.0:3002) → Proxy → API Server (localhost:3001)
```

The proxy is connecting from the Vite server process to the API server process **on the same machine**, so `localhost` is correct.

## Getting Help

If you continue to experience issues:

1. Check server logs for error messages
2. Use browser DevTools Network tab to see failed requests
3. Verify both servers are running: `lsof -i :3001` and `lsof -i :3002`
4. Test API directly: `curl http://*************:3001/api/health`
5. Test UI directly: Open `http://*************:3002` in browser on another device
